package com.ruoyi.common.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * 大门IP地址映射枚举
 * 根据精确IP地址识别对应的门号
 */
public enum GateIpMappingEnum {

    /**
     * 1号门 - 主大门
     */
    GATE_1(1, "主大门", new String[]{
        "************",
        "************1",
        "**********"
    }),

    /**
     * 2号门 - 东门
     */
    GATE_2(2, "东门", new String[]{
        "************",
        "************2",
        "**********"
    }),

    /**
     * 3号门 - 西门
     */
    GATE_3(3, "西门", new String[]{
        "************",
        "************3",
        "**********"
    }),

    /**
     * 4号门 - 南门
     */
    GATE_4(4, "南门", new String[]{
        "************",
        "*************",
        "**********"
    }),

    /**
     * 默认门号（未匹配到IP时使用）
     */
    DEFAULT_GATE(0, "默认门", new String[]{});

    private final int gateNumber;
    private final String gateName;
    private final String[] ipAddresses;

    GateIpMappingEnum(int gateNumber, String gateName, String[] ipAddresses) {
        this.gateNumber = gateNumber;
        this.gateName = gateName;
        this.ipAddresses = ipAddresses;
    }

    public int getGateNumber() {
        return gateNumber;
    }

    public String getGateName() {
        return gateName;
    }

    public String[] getIpAddresses() {
        return ipAddresses;
    }

    /**
     * 根据IP地址获取对应的门号
     * @param clientIp 客户端IP地址
     * @return 门号，如果未匹配到则返回默认门号
     */
    public static int getGateNumberByIp(String clientIp) {
        if (clientIp == null || clientIp.trim().isEmpty()) {
            return DEFAULT_GATE.getGateNumber();
        }

        Optional<GateIpMappingEnum> matchedGate = Arrays.stream(values())
            .filter(gate -> gate != DEFAULT_GATE)
            .filter(gate -> isIpMatched(clientIp, gate.getIpAddresses()))
            .findFirst();

        return matchedGate.map(GateIpMappingEnum::getGateNumber)
                         .orElse(DEFAULT_GATE.getGateNumber());
    }

    /**
     * 根据IP地址获取对应的门信息
     * @param clientIp 客户端IP地址
     * @return 门信息枚举
     */
    public static GateIpMappingEnum getGateByIp(String clientIp) {
        if (clientIp == null || clientIp.trim().isEmpty()) {
            return DEFAULT_GATE;
        }

        return Arrays.stream(values())
            .filter(gate -> gate != DEFAULT_GATE)
            .filter(gate -> isIpMatched(clientIp, gate.getIpAddresses()))
            .findFirst()
            .orElse(DEFAULT_GATE);
    }

    /**
     * 检查IP是否在指定的IP地址列表中（精确匹配）
     * @param ip 要检查的IP地址
     * @param ipAddresses IP地址数组
     * @return 是否匹配
     */
    private static boolean isIpMatched(String ip, String[] ipAddresses) {
        if (ipAddresses == null || ipAddresses.length == 0) {
            return false;
        }

        return Arrays.stream(ipAddresses)
                    .anyMatch(address -> address.equals(ip));
    }

    @Override
    public String toString() {
        return String.format("Gate[number=%d, name=%s, ipAddresses=%s]",
                           gateNumber, gateName, Arrays.toString(ipAddresses));
    }
}
