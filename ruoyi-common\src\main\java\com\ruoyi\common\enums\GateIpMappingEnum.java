package com.ruoyi.common.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * 大门IP地址映射枚举
 * 根据IP地址段识别对应的门号
 */
public enum GateIpMappingEnum {
    
    /**
     * 1号门 - 主大门
     */
    GATE_1(1, "主大门", new String[]{
        "***********/24",    // ***********-*************
        "********/24"        // ********-**********
    }),
    
    /**
     * 2号门 - 东门
     */
    GATE_2(2, "东门", new String[]{
        "***********/24",    // ***********-*************
        "********/24"        // ********-**********
    }),
    
    /**
     * 3号门 - 西门
     */
    GATE_3(3, "西门", new String[]{
        "***********/24",    // ***********-*************
        "********/24"        // ********-**********
    }),
    
    /**
     * 4号门 - 南门
     */
    GATE_4(4, "南门", new String[]{
        "***********/24",    // ***********-*************
        "********/24"        // ********-**********
    }),
    
    /**
     * 默认门号（未匹配到IP时使用）
     */
    DEFAULT_GATE(1, "默认门", new String[]{});
    
    private final int gateNumber;
    private final String gateName;
    private final String[] ipRanges;
    
    GateIpMappingEnum(int gateNumber, String gateName, String[] ipRanges) {
        this.gateNumber = gateNumber;
        this.gateName = gateName;
        this.ipRanges = ipRanges;
    }
    
    public int getGateNumber() {
        return gateNumber;
    }
    
    public String getGateName() {
        return gateName;
    }
    
    public String[] getIpRanges() {
        return ipRanges;
    }
    
    /**
     * 根据IP地址获取对应的门号
     * @param clientIp 客户端IP地址
     * @return 门号，如果未匹配到则返回默认门号
     */
    public static int getGateNumberByIp(String clientIp) {
        if (clientIp == null || clientIp.trim().isEmpty()) {
            return DEFAULT_GATE.getGateNumber();
        }
        
        Optional<GateIpMappingEnum> matchedGate = Arrays.stream(values())
            .filter(gate -> gate != DEFAULT_GATE)
            .filter(gate -> isIpInRanges(clientIp, gate.getIpRanges()))
            .findFirst();
            
        return matchedGate.map(GateIpMappingEnum::getGateNumber)
                         .orElse(DEFAULT_GATE.getGateNumber());
    }
    
    /**
     * 根据IP地址获取对应的门信息
     * @param clientIp 客户端IP地址
     * @return 门信息枚举
     */
    public static GateIpMappingEnum getGateByIp(String clientIp) {
        if (clientIp == null || clientIp.trim().isEmpty()) {
            return DEFAULT_GATE;
        }
        
        return Arrays.stream(values())
            .filter(gate -> gate != DEFAULT_GATE)
            .filter(gate -> isIpInRanges(clientIp, gate.getIpRanges()))
            .findFirst()
            .orElse(DEFAULT_GATE);
    }
    
    /**
     * 检查IP是否在指定的IP段范围内
     * @param ip 要检查的IP地址
     * @param ipRanges IP段数组（CIDR格式）
     * @return 是否在范围内
     */
    private static boolean isIpInRanges(String ip, String[] ipRanges) {
        if (ipRanges == null || ipRanges.length == 0) {
            return false;
        }
        
        return Arrays.stream(ipRanges)
                    .anyMatch(range -> isIpInCidrRange(ip, range));
    }
    
    /**
     * 检查IP是否在CIDR格式的IP段内
     * @param ip 要检查的IP地址
     * @param cidr CIDR格式的IP段（如：***********/24）
     * @return 是否在范围内
     */
    private static boolean isIpInCidrRange(String ip, String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }
            
            String networkIp = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            
            long ipLong = ipToLong(ip);
            long networkLong = ipToLong(networkIp);
            long mask = (-1L << (32 - prefixLength)) & 0xFFFFFFFFL;
            
            return (ipLong & mask) == (networkLong & mask);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 将IP地址转换为长整型
     * @param ip IP地址字符串
     * @return 长整型表示的IP
     */
    private static long ipToLong(String ip) {
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            throw new IllegalArgumentException("Invalid IP address: " + ip);
        }
        
        long result = 0;
        for (int i = 0; i < 4; i++) {
            result = (result << 8) + Integer.parseInt(parts[i]);
        }
        return result;
    }
    
    @Override
    public String toString() {
        return String.format("Gate[number=%d, name=%s, ipRanges=%s]", 
                           gateNumber, gateName, Arrays.toString(ipRanges));
    }
}
