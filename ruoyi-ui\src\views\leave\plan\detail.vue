<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="card-header">
        <h3>申请详情</h3>
      </div>

      <!-- 基本信息部分 -->
      <div class="section-container">
        <div class="section-title">基本信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号">
            <template slot="label"><i class="el-icon-document"></i> 申请编号</template>
            {{ planInfo.applyNo }}
          </el-descriptions-item>
          <el-descriptions-item label="计划号">
            <template slot="label"><i class="el-icon-document"></i> 计划号</template>
            {{ planInfo.planNo }}
          </el-descriptions-item>
          <el-descriptions-item label="计划状态">
            <template slot="label"><i class="el-icon-s-flag"></i> 计划状态</template>
            <el-tag :type="getPlanStatusType(planInfo.planStatus)">{{ getPlanStatusText(planInfo.planStatus) }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="计划类型">
            <template slot="label"><i class="el-icon-s-order"></i> 计划类型</template>
            <el-tag :type="getPlanTypeTagType(planInfo.planType)">{{ getPlanTypeText(planInfo.planType) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="业务类型">
            <template slot="label"><i class="el-icon-s-management"></i> 业务类型</template>
            <el-tag :type="getBusinessCategoryTagType(planInfo.businessCategory)">{{
              getBusinessCategoryText(planInfo.businessCategory) }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="是否计量">
            <template slot="label"><i class="el-icon-s-operation"></i> 是否计量</template>
            <el-tag :type="planInfo.measureFlag === 1 ? 'success' : 'danger'">
              {{ planInfo.measureFlag === 1 ? '计量' : '不计量' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="计划量" v-if="planInfo.plannedAmount">
            <template slot="label"><i class="el-icon-document"></i> 计划量（吨）</template>
            {{ planInfo.plannedAmount }}
          </el-descriptions-item>
          <el-descriptions-item label="是否复审">
            <template slot="label"><i class="el-icon-s-check"></i> 是否复审</template>
            <el-tag :type="planInfo.secApproveFlag === 1 ? 'warning' : 'info'">
              {{ planInfo.secApproveFlag === 1 ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="申请单位" v-if="planInfo.sourceCompany">
            <template slot="label"><i class="el-icon-office-building"></i> 申请单位</template>
            {{ planInfo.sourceCompany }}
          </el-descriptions-item>
          <el-descriptions-item label="收货单位" v-if="planInfo.receiveCompany">
            <template slot="label"><i class="el-icon-school"></i> 收货单位</template>
            {{ planInfo.receiveCompany }}
          </el-descriptions-item>

          <el-descriptions-item label="返回单位" v-if="planInfo.targetCompany">
            <template slot="label"><i class="el-icon-s-home"></i> 返回单位</template>
            {{ planInfo.targetCompany }}
          </el-descriptions-item>
          <el-descriptions-item label="计划返回时间" v-if="planInfo.planReturnTime">
            <template slot="label"><i class="el-icon-time"></i> 计划返回时间</template>
            {{ planInfo.planReturnTime }}
          </el-descriptions-item>

          <el-descriptions-item label="退货单位" v-if="planInfo.refundDepartment">
            <template slot="label"><i class="el-icon-s-shop"></i> 退货单位</template>
            {{ planInfo.refundDepartment }}
          </el-descriptions-item>

          <el-descriptions-item label="开始时间" v-if="planInfo.startTime">
            <template slot="label"><i class="el-icon-date"></i> 开始时间</template>
            {{ planInfo.startTime }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间" v-if="planInfo.endTime">
            <template slot="label"><i class="el-icon-date"></i> 结束时间</template>
            {{ planInfo.endTime }}
          </el-descriptions-item>
          <el-descriptions-item label="有效期" v-if="!planInfo.endTime">
            <template slot="label"><i class="el-icon-date"></i> 有效期</template>
            {{ planInfo.expireTime }}
          </el-descriptions-item>

          <el-descriptions-item label="监装人" v-if="planInfo.monitor">
            <template slot="label"><i class="el-icon-user"></i> 监装人</template>
            {{ planInfo.monitor }}
          </el-descriptions-item>
          <el-descriptions-item label="物资专管员" v-if="planInfo.specialManager">
            <template slot="label"><i class="el-icon-s-custom"></i> 物资专管员</template>
            {{ planInfo.specialManager }}
          </el-descriptions-item>

          <el-descriptions-item label="物资类型" v-if="planInfo.itemType">
            <template slot="label"><i class="el-icon-goods"></i> 物资类型</template>
            <el-tag :type="getMaterialTypeTagType(planInfo.itemType)">
              {{ getMaterialTypeText(planInfo.itemType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="出厂原因" v-if="planInfo.reason">
            <template slot="label"><i class="el-icon-info"></i> 出厂原因</template>
            {{ planInfo.reason }}
          </el-descriptions-item>

          <el-descriptions-item label="合同号" v-if="planInfo.contractNo">
            <template slot="label"><i class="el-icon-tickets"></i> 合同号</template>
            {{ planInfo.contractNo }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间" v-if="planInfo.applyTime">
            <template slot="label"><i class="el-icon-timer"></i> 申请时间</template>
            {{ planInfo.applyTime }}
          </el-descriptions-item>

          <el-descriptions-item label="申请人" v-if="planInfo.applyUserName">
            <template slot="label"><i class="el-icon-user-solid"></i> 申请人</template>
            {{ planInfo.applyUserName }}
          </el-descriptions-item>
          <el-descriptions-item label="计划量" v-if="planInfo.planned_amount">
            <template slot="label"><i class="el-icon-user-solid"></i> 计划量(吨)</template>
            {{ planInfo.planned_amount }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 新增图片列表部分 -->
      <div class="section-container" v-if="imageList.length > 0">
        <div class="section-title">
          <span><i class="el-icon-picture-outline"></i> 申请图片</span>
        </div>
        <div class="image-container">
          <viewer :images="imageList">
            <div class="image-list">
              <div class="image-item" v-for="(image, index) in imageList" :key="'img-' + index">
                <img :src="image.url" :alt="image.name">
                <div class="image-name">{{ image.name }}</div>
              </div>
            </div>
          </viewer>
        </div>
      </div>

      <!-- 新增文件列表部分 -->
      <div class="section-container" v-if="fileList.length > 0">
        <div class="section-title">
          <span><i class="el-icon-document"></i> 申请附件</span>
        </div>
        <div class="file-container">
          <div class="file-list">
            <div class="file-item" v-for="(file, index) in fileList" :key="'file-' + index"
              @click="downloadFile(file.url, file.name)">
              <i class="el-icon-document file-icon"></i>
              <div class="file-name">{{ file.name }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 物资列表部分 -->
      <div class="section-container">
        <div class="section-title">物资列表</div>
        <el-table :data="planInfo.materials" style="width: 100%" border>
          <el-table-column type="index" width="50" label="序号">
          </el-table-column>
          <el-table-column prop="materialName" label="物资名称" width="180">
          </el-table-column>
          <el-table-column prop="materialSpec" label="物资型号规格" width="180">
          </el-table-column>
          <el-table-column prop="planNum" label="计划数量" width="120">
          </el-table-column>
          <el-table-column prop="measureUnit" label="单位" width="120">
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="150">
          </el-table-column>
        </el-table>
      </div>

      <!-- 派车信息部分 -->
      <div class="section-container">
        <div class="section-title">
          <span>派车信息</span>
          <el-button type="primary" size="small" icon="el-icon-truck" @click="openDispatchDialog"
            :disabled="!canDispatchCar" class="dispatch-btn">
            派车
          </el-button>
        </div>

        <el-table v-if="taskListInfo.length > 0" :data="taskListInfo" style="width: 100%" border>
          <el-table-column type="index" width="50" label="序号">
          </el-table-column>
          <el-table-column prop="carNum" label="车牌号" width="120">
          </el-table-column>
          <el-table-column prop="driverName" label="司机姓名" width="100">
          </el-table-column>
          <el-table-column prop="mobilePhone" label="司机手机号" width="120">
          </el-table-column>
          <el-table-column prop="taskType" label="任务类型" width="120" :formatter="taskTypeFormat">
          </el-table-column>
          <!-- <el-table-column prop="planNum" label="计划数量" width="180" v-if="planInfo.measureFlag == 0">
          </el-table-column>
          <el-table-column prop="doormanReceiveNum" label="门卫确认数量" width="180" v-if="planInfo.measureFlag == 0 ">
          </el-table-column>
          <el-table-column prop="factoryReceiveNum" label="分厂确认数量" width="180" v-if="planInfo.measureFlag == 0">
          </el-table-column> -->
          <!-- <el-table-column prop="planNum" label="计划数量" width="120" v-if="planInfo.measureFlag == 1">
          </el-table-column>
          <el-table-column prop="measureUnit" label="计量单位" width="120" v-if="planInfo.measureFlag == 1">
          </el-table-column> -->
          <el-table-column prop="tareWeight" label="皮重" width="100" v-if="planInfo.measureFlag == 1">
            <template slot-scope="scope">
              {{ scope.row.tare || '-' }} {{ scope.row.tare ? 't' : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="grossWeight" label="毛重" width="100" v-if="planInfo.measureFlag == 1">
            <template slot-scope="scope">
              {{ scope.row.gross || '-' }} {{ scope.row.gross ? 't' : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="tareWeight" label="皮重(复磅)" width="100"
            v-if="planInfo.measureFlag == 1 && planInfo.planType !== 1">
            <template slot-scope="scope">
              {{ scope.row.secTare || '-' }} {{ scope.row.secTare ? 't' : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="grossWeight" label="毛重(复磅)" width="100"
            v-if="planInfo.measureFlag == 1 && planInfo.planType !== 1">
            <template slot-scope="scope">
              {{ scope.row.secGross || '-' }} {{ scope.row.secGross ? 't' : '' }}
            </template>
          </el-table-column>


          <el-table-column prop="createTime" label="派车时间" width="160">
          </el-table-column>
          <el-table-column prop="taskStatus" label="任务状态" width="120" :formatter="taskStatusFormat">
          </el-table-column>
          <el-table-column label="操作" min-width="120">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="goToTaskDetail(scope.row)">
                任务详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div v-else class="empty-data">
          <el-empty description="暂无派车记录"></el-empty>
        </div>

        <!-- 物资确认按钮 -->
        <div style="text-align: right; margin-top: 15px;">
          <el-button type="primary" icon="el-icon-finished" @click="handleMaterialConfirm">
            物资确认
          </el-button>
        </div>
      </div>
      <!-- v-if="canShowMaterialConfirm" -->

      <!-- 审核内容部分 -->
      <div class="section-container" v-if="planInfo.approveButtonShow">
        <div class="section-title">审核内容</div>
        <el-form label-width="80px" :model="approveForm" ref="approveForm">
          <el-form-item label="审核建议">
            <el-input type="textarea" v-model="approveForm.approveContent" :rows="4" placeholder="请输入审核建议"
              maxlength="200" show-word-limit></el-input>
            <div style="color: #909399; font-size: 12px; margin-top: 5px; margin-bottom: 10px;">审核建议可不填，默认通过为同意，驳回为拒绝
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 日志列表部分 -->
      <div class="section-container">
        <div class="section-title">日志列表</div>
        <el-timeline>
          <el-timeline-item v-for="(log, index) in planInfo.leaveLogs" :key="index" :timestamp="log.createTime"
            :color="getLogColor(log)">
            {{ log.info }}
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 固定底部操作栏 -->
      <div class="fixed-bottom-action">
        <el-row :gutter="10" type="flex" justify="center" align="middle">
          <!-- 返回按钮 -->
          <el-col :span="2" :xs="6">
            <el-button size="medium" @click="cancel">返回</el-button>
          </el-col>

          <!-- 通过按钮 -->
          <el-col :span="2" :xs="6" v-if="planInfo.approveButtonShow">
            <el-button size="medium" type="primary" icon="el-icon-check" @click="handleApprove">通过</el-button>
          </el-col>

          <!-- 驳回按钮 -->
          <el-col :span="2" :xs="6" v-if="planInfo.rejectButtonShow">
            <el-button size="medium" type="danger" icon="el-icon-close" @click="handleReject">驳回</el-button>
          </el-col>

          <!-- 废弃按钮 -->
          <el-col :span="2" :xs="6" v-if="planInfo.discardButtonShow">
            <el-button size="medium" type="success" icon="el-icon-delete" @click="handleDiscard">废弃</el-button>
          </el-col>
        </el-row>
      </div>

    </el-card>

    <!-- 派车弹框 -->
    <el-dialog title="派车" :visible.sync="dispatchDialogVisible" width="1200px" append-to-body destroy-on-close
      @closed="resetDispatchForm">
      <el-form ref="dispatchForm" :model="dispatchForm" :rules="dispatchRules" label-width="100px"
        class="dispatch-form">

        <el-form-item label="货车司机" prop="driverId" :rules="[{ required: true, message: '司机信息不能为空' }]">
          <el-select style="width:300px" v-model="dispatchForm.driverId" filterable :filter-method="filterDriverData"
            placeholder="请选择（如果显示不出请在输入框搜索）" @change="handleDriverChange">
            <el-option v-for="item in filteredDriverOptions.slice(0, 50)" :key="item.id" :label="item.driverInfo"
              :value="item.id">
            </el-option>
          </el-select>
          <el-button style="margin-left: 10px; font-size: 14px; padding: 5px 10px;" type="primary"
            @click="openNewDriverWindow">前往新增或修改司机信息
          </el-button>
        </el-form-item>

        <el-form-item label="司机名称" prop="name" v-if="dispatchForm.name != null">
          <el-input v-model="dispatchForm.name" placeholder="请输入司机名称" disabled style="width:300px" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone" v-if="dispatchForm.phone != null">
          <el-input v-model="dispatchForm.phone" placeholder="请输入手机号" disabled style="width:300px" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard" v-if="dispatchForm.idCard != null">
          <el-input v-model="dispatchForm.idCard" placeholder="请输入身份证号" disabled style="width:300px" />
        </el-form-item>

        <el-form-item label="人脸照片" prop="faceImg" v-if="dispatchForm.photo != null && dispatchForm.photo != ''">
          <div class="image-grid">
            <!-- <el-image v-for="(image, index) in form.faceImgList" :key="index" :src="image" fit="cover"
                      lazy></el-image> -->
            <el-image style="width: 200px; height: 200px" :src="dispatchForm.photo" fit="cover"></el-image>
          </div>
        </el-form-item>

        <el-form-item label="驾驶证照片" prop="driverLicenseImgs"
          v-if="dispatchForm.driverLicenseImgs != null && dispatchForm.driverLicenseImgs != ''">
          <div class="image-grid">
            <!-- <el-image v-for="(image, index) in form.drivingLicenseImgList" :key="index" :src="image" fit="cover"
              lazy></el-image> -->
            <el-image style="width: 200px; height: 200px" :src="dispatchForm.driverLicenseImgs" fit="cover"></el-image>
          </div>
        </el-form-item>

        <el-form-item label="行驶证照片" prop="vehicleLicenseImgs"
          v-if="dispatchForm.vehicleLicenseImgs != null && dispatchForm.vehicleLicenseImgs != ''">
          <div class="image-grid">
            <!-- <el-image v-for="(image, index) in form.driverLicenseImgList" :key="index" :src="image" fit="cover"
              lazy></el-image> -->
            <el-image style="width: 200px; height: 200px" :src="dispatchForm.vehicleLicenseImgs" fit="cover"></el-image>
          </div>
        </el-form-item>

        <el-form-item label="货车" prop="carUUId" :rules="[{ required: true, message: '货车信息不能为空' }]">
          <el-select style="width:300px" v-model="dispatchForm.carUUId" filterable :filter-method="filterCarData"
            placeholder="请选择（如果显示不出请在输入框搜索）" @change="handleCarChange">
            <el-option v-for="item in filteredCarOptions.slice(0, 50)" :key="item.id" :label="item.carNumber"
              :value="item.id">
            </el-option>
          </el-select>

          <el-button style="margin-left: 10px; font-size: 14px; padding: 5px 10px;" type="primary"
            @click="openNewCarWindow">前往新增或修改货车信息
          </el-button>
        </el-form-item>

        <el-form-item label="车牌号" prop="carNumber" v-if="dispatchForm.carNumber != null">
          <el-input v-model="dispatchForm.carNumber" placeholder="请输入车牌号" disabled style="width:300px" />
        </el-form-item>
        <el-form-item label="车辆排放标准" prop="vehicleEmissionStandards"
          v-if="dispatchForm.vehicleEmissionStandards != null">
          <el-select v-model="dispatchForm.vehicleEmissionStandards" placeholder="请选择车辆排放标准" disabled
            style="width:300px">
            <el-option v-for="dict in vehicleEmissionStandardsOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="任务类型" prop="taskType" :rules="[{ required: true, message: '任务类型不能为空' }]"
          v-if="isTaskTypeEdit == true">
          <el-select v-model="dispatchForm.taskType" placeholder="请选择车任务类型" style="width:300px">
            <el-option v-for="dict in taskTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue"></el-option>
          </el-select>
        </el-form-item>

        <!-- 新增物资选择表格 -->
        <el-form-item label="物资选择" prop="selectedMaterials"
          v-if="planInfo.measureFlag == 0 && dispatchForm.taskType == 2">
          <el-table :data="materialSelectionList" border style="width: 100%">
            <el-table-column type="index" width="50" label="序号"></el-table-column>
            <el-table-column prop="materialName" label="物资名称" width="180">
              <template slot-scope="scope">
                <el-select v-model="scope.row.materialId" placeholder="请选择物资"
                  @change="handleMaterialChange(scope.row, scope.$index)">
                  <el-option v-for="item in availableMaterials" :key="item.materialId" :label="item.materialName"
                    :value="item.materialId" :disabled="isMaterialAvailable(item)">

                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="materialSpec" label="物资规格" width="180">
              <template slot-scope="scope">
                {{ scope.row.materialSpec }}
              </template>
            </el-table-column>
            <el-table-column prop="planNum" label="计划数量" width="120">
              <template slot-scope="scope">
                {{ scope.row.planNum }}
              </template>
            </el-table-column>
            <el-table-column prop="remainingNum" label="剩余数量" width="120">
              <template slot-scope="scope">
                {{ scope.row.remainingNum }}
              </template>
            </el-table-column>
            <el-table-column prop="currentNum" label="本次数量" width="150">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.currentNum" :min="0"
                  @change="handleNumChange($event, scope.$index)" :disabled="!scope.row.materialId">
                </el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="80">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-delete" @click="removeMaterial(scope.$index)"
                  :disabled="materialSelectionList.length === 1">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 10px;">
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addMaterialRow">添加物资</el-button>
          </div>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dispatchDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitDispatchForm">确 认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { detailPlan, approve, discard, listTaskMaterial, confirmMaterial } from "@/api/leave/plan";
import { listAllTask, addTask, addTaskMaterial, addTaskAndMaterial, addLeaveLog, isAllowDispatch, addTaskAndMaterialAndAddLeaveLog } from "@/api/leave/task";
import { listAllDriver,  getXctgDriverUserListByPage, getXctgDriverCarListByPage } from "@/api/dgcb/driver/driver";
import { mount } from "sortablejs";
export default {
  name: "DetailLeavePlan",
  data() {
    // 验证车牌号
    const validateCarNumber = (rule, value, callback) => {
      const pattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;
      if (!pattern.test(value)) {
        callback(new Error('请输入正确的车牌号'));
      } else {
        callback();
      }
    };

    // 验证手机号
    const validatePhone = (rule, value, callback) => {
      const pattern = /^1[3-9]\d{9}$/;
      if (!pattern.test(value)) {
        callback(new Error('请输入正确的手机号'));
      } else {
        callback();
      }
    };

    // 验证身份证号
    const validateIdCard = (rule, value, callback) => {
      const pattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!pattern.test(value)) {
        callback(new Error('请输入正确的身份证号'));
      } else {
        callback();
      }
    };

    return {
      isTaskTypeEdit: true,
      vehicleEmissionStandardsOptions: [],
      taskTypeOptions: [],
      carList: [],
      searchCarQuery: '',
      filteredCarOptions: [],
      driverList: [],
      searchDriverQuery: '',
      filteredDriverOptions: [],
      //审核表单
      approveForm: {
        applyNo: null,
        approveContent: '',//审核意见
        approveFlag: true,//审核状态
      },

      // 图片列表
      imageList: [],

      // 文件列表
      fileList: [],

      // 派车弹框可见性
      dispatchDialogVisible: false,

      taskListInfo: [],

      // 派车表单数据
      dispatchForm: {
        // carNumber: '',
        // driverName: '',
        // driverPhone: '',
        // driverIdCard: ''
      },

      // 派车表单验证规则
      dispatchRules: {
        carNumber: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
          { validator: validateCarNumber, trigger: 'blur' }
        ],
        driverName: [
          { required: true, message: '请输入司机姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        driverPhone: [
          { required: true, message: '请输入司机手机号', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        driverIdCard: [
          { required: true, message: '请输入司机身份证号', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' }
        ]
      },

      // 派车列表数据
      dispatchList: [
        {
          id: 1,
          carNumber: '京A12345',
          driverName: '王小明',
          driverPhone: '13800138000',
          driverIdCard: '110101199001010001',
          dispatchTime: '2025-03-18 09:30:00',
          status: 2,
          tareWeight: 8500,
          grossWeight: 15800,
          recheckedGrossWeight: 15750,
          recheckedTareWeight: 8480
        },
        {
          id: 2,
          carNumber: '京B98765',
          driverName: '李大壮',
          driverPhone: '13900139000',
          driverIdCard: '110101199102020002',
          dispatchTime: '2025-03-19 14:15:00',
          status: 1,
          tareWeight: 7800,
          grossWeight: 12600,
          recheckedGrossWeight: null,
          recheckedTareWeight: null
        }
      ],

      // 计划详情信息
      planInfo: {},
      applyNo: null,
      taskQueryParams: {
        applyNo: null,
      },

      taskMaterialList: null,
      // 物资选择相关数据
      materialSelectionList: [{
        materialId: null,
        materialName: '',
        materialSpec: '',
        planNum: 0,
        usedNum: 0,
        remainingNum: 0,
        currentNum: 0
      }],
      availableMaterials: [], // 可选的物资列表
      taskMaterialListMap: new Map(), // 已派车的物资列表
      taskMaterialMap: new Map(), // 存储所有任务物资的映射
    };
  },
  computed: {
    // 判断是否可以派车
    canDispatchCar() {
      // 判断申请单是否已通过
      // const isPlanApproved = this.planInfo.planStatus === 2;

      // // 如果是非计量类型，且已经派过车，则不能再派车
      // if (this.planInfo.measureFlag !== 1 && this.dispatchList.length > 0) {
      //   return false;
      // }

      return true;
    },
    // 默认显示前50条，若有搜索，则显示搜索后的数据
    displayDriverListOptions() {
      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);
    },
    displayCarListOptions() {
      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);
    },
    canShowMaterialConfirm() {
      // 只有planStatus为5或6时显示（已出厂/部分收货），且不是已完成/废弃/驳回/过期
      return [5, 6].includes(this.planInfo.planStatus);
    }
  },
  activated() {
    this.getDicts("xctg_driver_car_emission_standards").then(response => {
      this.vehicleEmissionStandardsOptions = response.data;
    });
    // 初始化任务类型选项（在获取计划信息后会重新更新）
    this.getDicts("leave_task_type").then(response => {
      this.taskTypeOptions = response.data;
    });
    // 获取路由参数中的ID
    const applyNo = this.$route.params.applyNo;
    this.applyNo = applyNo
    this.taskQueryParams.applyNo = applyNo;
    this.approveForm.applyNo = applyNo;
    if (applyNo) {
      detailPlan(applyNo).then(response => {
        this.planInfo = response.data;
        this.taskTypeEditUpdate();
        console.log("this.planInfo", this.planInfo);
        // 解析图片和文件数据
        this.parseImageAndFileData();
        // 获取任务信息后更新任务类型选项
        this.getListTaskInfo();
      });
    };
    this.getDriverList();
    this.getCarList();



  },


  methods: {
    handleApprove() {
      this.approveForm.approveFlag = true;
      console.log("this.approveForm", this.approveForm);
      approve(this.approveForm).then(response => {
        this.$message.success('审核通过');
        // 跳转到列表页面并刷新
        this.$router.push({
          path: "/leave/leavePlanList",
          query: {
            t: Date.now(),
            refresh: true // 添加刷新标记
          }
        });
      }).catch(error => {
        this.$message.error('审核失败');
        console.error('Approval error:', error);
      });
    },
    handleReject() {
      this.approveForm.approveFlag = false;
      console.log("this.approveForm", this.approveForm);
      approve(this.approveForm).then(response => {
        this.$message.success('驳回成功');
        // 跳转到列表页面并刷新
        this.$router.push({
          path: "/leave/leavePlanList",
          query: {
            t: Date.now(),
            refresh: true // 添加刷新标记
          }
        });
      }).catch(error => {
        this.$message.error('审核失败');
        console.error('Approval error:', error);
      });
    },
    handleDiscard() {
      discard(this.planInfo).then(response => {
        this.$message.success('废弃成功');

        if (window.history.length > 1) {
          this.$router.go(-1);
        } else {
          this.$router.push({ path: "/leave/leavePlanList", query: { t: Date.now() } });
        }
      }).catch(error => {
        this.$message.error('废弃失败');
        console.error('Approval error:', error);
      });
    },
    taskTypeEditUpdate() {
      if (this.planInfo.planType !== 2) {
        this.isTaskTypeEdit = false;
      }
    },
    // 更新任务类型选项
    updateTaskTypeOptions() {
      // 获取原始的任务类型选项
      this.getDicts("leave_task_type").then(response => {
        let options = response.data;
        console.log("原始任务类型选项:", options);
        console.log("计划类型:", this.planInfo.planType);
        console.log("任务数量:", this.taskListInfo.length);

        // 对于出厂返回任务（planType=2）
        if (this.planInfo.planType === 2) {
          // 如果当前任务数为0，只显示出厂选项
          if (this.taskListInfo.length === 0) {
            options = options.filter(option =>
              option.dictValue === '1' || option.dictValue === 1
            ); // 只保留出厂选项，兼容字符串和数字类型
            console.log("过滤后只保留出厂选项:", options);
          }
          // 如果已有任务，显示所有选项（出厂和返厂）
        } else {
          // 对于其他计划类型，保持原有逻辑
          if (this.planInfo.planType !== 3) {
            options = options.filter(option =>
              option.dictValue !== '3' && option.dictValue !== 3
            ); // 移除跨区调拨选项，兼容字符串和数字类型
          }
        }

        console.log("最终任务类型选项:", options);
        this.taskTypeOptions = options;
        // 强制刷新下拉框
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      });
    },
    getListTaskInfo() {
      listAllTask(this.taskQueryParams).then(response => {
        console.log("response.data", response.rows);
        this.taskListInfo = response.data;
        console.log("this.taskListInfo", this.taskListInfo);
        // 获取所有任务物资
        this.getAllTaskMaterials();
        // 更新任务类型选项（基于当前任务数）
        this.updateTaskTypeOptions();
      });
    },
    openNewDriverWindow() {
      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL
      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL
    },
    openNewCarWindow() {
      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL
      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL
    },
    // 1国五，2国六，3新能源字典翻译
    vehicleEmissionStandardsFormat(row, column) {
      return this.selectDictLabel(this.vehicleEmissionStandardsOptions, row.vehicleEmissionStandards);
    },

    taskTypeFormat(row, column) {
      return this.getTaskTypeText(row.taskType);
    },
    taskStatusFormat(row, column) {
      return this.getStatusText(row.taskStatus);
    },

    /** 查询司机信息列表 */
    getCarList() {
      this.loading = true;
      // listAllDriver().then(response => {
      //   this.driverList = response.data;
      //   this.loading = false;
      // });
      getXctgDriverCarListByPage().then(response => {
        this.carList = response.rows;
        this.filteredCarOptions = this.carList;
        this.loading = false;
      });
    },
    // 搜索过滤逻辑
    filterCarData(query) {
      this.searchCarQuery = query;

      if (this.searchCarQuery) {
        // 调用后端接口进行搜索
        const searchParams = {
          carNumber: query
        };
        getXctgDriverCarListByPage(searchParams).then(response => {
          this.filteredCarOptions = response.rows || [];
        }).catch(error => {
          console.error('搜索货车失败:', error);
          this.filteredCarOptions = [];
        });
      } else {
        // 如果没有搜索条件，显示前50条数据
        this.filteredCarOptions = this.carList.slice(0, 50);
      }
    },
    //通过driverId获取司机信息
    handleDriverChange() {
      if (this.dispatchForm.driverId != null) {
        this.driverList.forEach(item => {
          if (item.id == this.dispatchForm.driverId) {
            this.dispatchForm.name = item.name;
            this.dispatchForm.idCard = item.idCard;
            this.dispatchForm.company = item.company;
            this.dispatchForm.phone = item.phone;
            this.dispatchForm.photo = item.photo;
            this.dispatchForm.faceImgList = item.faceImgList;
            this.dispatchForm.driverLicenseImgs = item.driverLicenseImgs;
            this.dispatchForm.vehicleLicenseImgs = item.vehicleLicenseImgs;
            this.dispatchForm.sex = item.gender;

          }
        });
      }
    },
    //通过driverId获取司机信息
    handleCarChange() {
      console.log("handleCarChange")
      if (this.dispatchForm.carUUId != null) {
        this.carList.forEach(item => {
          if (item.id == this.dispatchForm.carUUId) {
            this.dispatchForm.carNumber = item.carNumber;

            if (item.vehicleEmissionStandards == 1) {
              this.dispatchForm.vehicleEmissionStandards = "国五";
            } else if (item.vehicleEmissionStandards == 2) {
              this.dispatchForm.vehicleEmissionStandards = "国六";
            } else if (item.vehicleEmissionStandards == 3) {
              this.dispatchForm.vehicleEmissionStandards = "新能源";
            } else {
              this.dispatchForm.vehicleEmissionStandards = "";
            }
            this.dispatchForm.licensePlateColor = item.licensePlateColor;
            this.dispatchForm.carId = item.carId;
            this.dispatchForm.trailerNumber = item.trailerNumber;
            this.dispatchForm.trailerId = item.trailerId;
            this.dispatchForm.axisType = item.axisType;
            this.dispatchForm.driverWeight = item.driverWeight;
            this.dispatchForm.maxWeight = item.maxWeight;
            this.dispatchForm.engineNumber = item.engineNumber;
            this.dispatchForm.vinNumber = item.vinNumber;
          }

        });
      }
    },
    /** 查询司机信息列表 */
    getDriverList() {
      // listAllDriver().then(response => {
      //   this.driverList = response.data;
      //   this.loading = false;
      // });
      getXctgDriverUserListByPage().then(response => {
        this.driverList = response.rows;
        console.log("this.driverList", this.driverList);
        this.filteredDriverOptions = this.driverList;
      });
    },
    // 搜索过滤逻辑
    filterDriverData(query) {
      this.searchDriverQuery = query;

      if (this.searchDriverQuery) {
        // 调用后端接口进行搜索
        const searchParams = {
          searchValue: query
        };
        getXctgDriverUserListByPage(searchParams).then(response => {
          this.filteredDriverOptions = response.rows || [];
        }).catch(error => {
          console.error('搜索货车司机失败:', error);
          this.filteredDriverOptions = [];
        });
      } else {
        // 如果没有搜索条件，显示前50条数据
        this.filteredDriverOptions = this.driverList.slice(0, 50);
      }
    },
    // 解析图片和文件数据
    parseImageAndFileData() {
      // 解析图片数据
      if (this.planInfo.applyImgUrl) {
        try {
          this.imageList = JSON.parse(this.planInfo.applyImgUrl);
        } catch (e) {
          console.error('解析图片数据失败:', e);
          this.imageList = [];
        }
      }

      // 解析文件数据
      if (this.planInfo.applyFileUrl) {
        try {
          this.fileList = JSON.parse(this.planInfo.applyFileUrl);
        } catch (e) {
          console.error('解析文件数据失败:', e);
          this.fileList = [];
        }
      }
    },

    // 下载文件
    downloadFile(url, fileName) {
      if (!url) {
        this.$message.error('文件链接无效');
        return;
      }

      // 创建一个a元素用于下载
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || '下载文件';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 获取计划类型文本
    getPlanTypeText(type) {
      const typeMap = {
        1: '出厂不返回',
        2: '出厂返回',
        3: '跨区调拨',
        4: '退货申请'
      };
      return typeMap[type] || '未知类型';
    },

    // 获取业务类型文本
    getBusinessCategoryText(category) {
      const categoryMap = {
        1: '通用',
        11: '通用',
        12: '委外加工',
        21: '有计划量计量',
        22: '短期',
        23: '钢板（圆钢）',
        31: '通用'
      };
      return categoryMap[category] || '未知类型';
    },

    // 获取物资类型文本
    getMaterialTypeText(type) {
      const typeMap = {
        1: '钢材',
        2: '钢板',
        3: '其他'
      };
      return typeMap[type] || '未知类型';
    },

    // 获取计划状态文本
    getPlanStatusText(status) {
      const statusMap = {
        1: '待分厂审批',
        2: '待分厂复审',
        3: '待生产指挥中心审批',
        4: '审批完成',
        5: '已出厂',
        6: '部分收货',
        7: '已完成',
        11: '驳回',
        12: '废弃',
        13: '过期'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取日志颜色
    getLogColor(log) {
      const logTypeColorMap = {
        1: '#409EFF', // 创建
        2: '#67C23A', // 审批
        3: '#E6A23C', // 流转
        4: '#F56C6C', // 驳回
        5: '#909399'  // 其他
      };
      return logTypeColorMap[log.logType] || '#409EFF';
    },

    // 获取派车状态文本
    getDispatchStatusText(status) {
      const statusMap = {
        0: '待出发',
        1: '已出发',
        2: '已到达',
        3: '已完成',
        4: '已取消'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取派车状态类型（用于标签颜色）
    getDispatchStatusType(status) {
      const statusMap = {
        0: 'info',
        1: 'primary',
        2: 'success',
        3: 'success',
        4: 'danger'
      };
      return statusMap[status] || 'info';
    },

    // 获取计划类型标签样式
    getPlanTypeTagType(type) {
      const typeMap = {
        1: 'success',  // 出厂不返回
        2: 'warning',  // 出厂返回
        3: 'info',     // 跨区调拨
        4: 'danger'    // 退货申请
      };
      return typeMap[type] || 'info';
    },

    // 获取物资类型标签样式
    getMaterialTypeTagType(type) {
      const typeMap = {
        1: 'primary',  // 钢材
        2: 'success',  // 钢板
        3: 'info'      // 其他
      };
      return typeMap[type] || 'info';
    },

    // 获取业务类型标签样式
    getBusinessCategoryTagType(category) {
      const typeMap = {
        '1': 'primary',   // 通用
        '11': 'primary',  // 通用
        '12': 'warning',  // 委外加工
        '21': 'success',  // 有计划量计量
        '22': 'info',     // 短期
        '23': 'danger',   // 钢板（圆钢）
        '31': 'primary'   // 通用
      };
      return typeMap[category] || 'info';
    },

    // 打开派车弹框
    openDispatchDialog() {
      // 初始化物资数据
      this.availableMaterials = this.planInfo.materials || [];
      console.log("this.availableMaterials", this.availableMaterials);

      // 获取已派车的物资列表，并在回调中初始化 materialSelectionList
      this.getTaskMaterialListAndInitSelection();
      // 判断非计量且taskType为1的情况
      if (this.planInfo.measureFlag == 0) {
        if (this.dispatchForm.taskType == 1) {
          // 检查是否已经有taskType为1的任务
          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);
          if (hasType1Task) {
            this.$message.warning('非计量只能派车出厂一次');
            return;
          }
          console.log("hasType1Task", hasType1Task)
        }
      }


      // 判断用户角色权限
      const roles = this.$store.getters.roles;
      console.log("roles", roles);
      if (!roles.includes('leave.supplier') && !roles.includes('leave.applicant')) {
        this.$message.error('您没有派车权限');
        return;
      }

      console.log("this.planInfo.planStatus", this.planInfo.planStatus);
      if (![4, 5, 6].includes(this.planInfo.planStatus)) {
        this.$message.warning('当前状态无法派车');
        return;
      }




      console.log("openDispatchDialog", this.taskListInfo.length);
      if (this.planInfo.businessCategory == 22 && this.taskListInfo.length >= 1) {
        this.$message.warning('短期计划只允许派一次车');
        return;
      }

      if (this.planInfo.businessCategory == 23 && this.taskListInfo.length >= 1) {
        this.$message.warning('钢板（圆钢）计划只允许派一次车');
        return;
      }

      this.dispatchForm = {};

      // 更新任务类型选项
      this.updateTaskTypeOptions();

      if (this.planInfo.planType == 1) {
        this.dispatchForm.taskType = "1"
      } else if (this.planInfo.planType == 3) {
        this.dispatchForm.taskType = "3"
      } else if (this.planInfo.planType == 4) {
        this.dispatchForm.taskType = "1"
      } else if (this.planInfo.planType == 2) {
        // 对于出厂返回任务，根据当前任务数决定默认任务类型
        if (this.taskListInfo.length === 0) {
          this.dispatchForm.taskType = "1"; // 默认选择出厂
        } else {
          this.dispatchForm.taskType = "2"; // 默认选择返厂
        }
      }
      console.log(this.dispatchForm.taskType),
        this.dispatchDialogVisible = true;


    },

    // 新增方法
    getTaskMaterialListAndInitSelection() {
      // 清空已用数量映射
      this.taskMaterialListMap.clear();
      // 统计所有已派车物资
      const type2List = this.taskListInfo.filter(item => item.taskType === 2);
      console.log("type2List", type2List);
      if (!type2List || type2List.length === 0) {
        // 初始化 materialSelectionList：全部选上且数量为剩余数量
        this.materialSelectionList = (this.planInfo.materials || []).map(mat => {
          // const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;
          // const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);
          return {
            materialId: mat.materialId,
            materialName: mat.materialName,
            materialSpec: mat.materialSpec,
            planNum: mat.planNum,
            usedNum: 0,
            remainingNum: mat.planNum,
            currentNum: mat.planNum
          };
        });
      } else {
        console.log("this.taskListInfo", this.taskListInfo);
        type2List.forEach(task => {
          const params = { taskNo: task.taskNo };
          listTaskMaterial(params).then(response => {
            let taskMaterials = response.rows || [];
            taskMaterials.forEach(material => {
              if (!this.taskMaterialListMap.has(material.materialId)) {
                this.taskMaterialListMap.set(material.materialId, {
                  taskMaterialInfo: material,
                  usedNum: material.planNum
                });
              } else {
                const existingMaterial = this.taskMaterialListMap.get(material.materialId);
                existingMaterial.usedNum += material.planNum;
              }
            });

            // 将taskMaterialListMap转换为数组集合
            this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({
              materialId: key,
              ...value
            }));

            // 初始化 materialSelectionList：全部选上且数量为剩余数量
            this.materialSelectionList = (this.planInfo.materials || []).map(mat => {
              const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;
              const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);

              return {
                materialId: mat.materialId,
                materialName: mat.materialName,
                materialSpec: mat.materialSpec,
                planNum: mat.planNum,
                usedNum: usedNum,
                remainingNum: remainingNum,
                currentNum: remainingNum
              };
            });

            this.materialSelectionList = this.materialSelectionList.filter(item => item.remainingNum > 0);
          });
        });
      }

         // 判断非计量且taskType为1的情况
      if (this.planInfo.measureFlag == 0) {
        if (this.dispatchForm.taskType == 1) {
          // 检查是否已经有taskType为1的任务
          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);
          if (hasType1Task) {
            this.$message.warning('非计量只能派车出厂一次');
            return;
          }
          console.log("hasType1Task", hasType1Task)
        }
      }


    },

    // 重置派车表单
    resetDispatchForm() {
      this.$refs.dispatchForm && this.$refs.dispatchForm.resetFields();
      this.materialSelectionList = [{
        materialId: null,
        materialName: '',
        materialSpec: '',
        planNum: 0,
        remainingNum: 0,
        usedNum: 0,
        currentNum: 0
      }];
    },

    // 获取已派车的物资列表
    getTaskMaterialList() {
      // 从taskListInfo中获取已派车的物资信息
      this.taskMaterialListMap.clear();
      this.taskListInfo.forEach(task => {
        const params = {
          taskNo: task.taskNo,
        };
        listTaskMaterial(params).then(response => {
          console.log("listTaskMaterial", response.rows);
          let taskMaterials = [];
          taskMaterials = response.rows;
          taskMaterials.forEach(material => {
            if (!this.taskMaterialListMap.has(material.materialId)) {
              this.taskMaterialListMap.set(material.materialId, {
                taskMaterialInfo: material,
                usedNum: material.planNum
              });
            } else {
              const existingMaterial = this.taskMaterialListMap.get(material.materialId);
              existingMaterial.usedNum += material.planNum;
            }
          });
          // 将taskMaterialListMap转换为数组集合
          this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({
            materialId: key,
            ...value
          }));
          console.log("taskMaterialArray", this.taskMaterialList);
          console.log("taskMaterialListMap", this.taskMaterialListMap);
        });
      });
    },

    // 添加物资行
    addMaterialRow() {
      this.materialSelectionList.push({
        materialId: null,
        materialName: '',
        materialSpec: '',
        planNum: 0,
        remainingNum: 0,
        usedNum: 0,
        currentNum: 0
      });
    },

    // 移除物资行
    removeMaterial(index) {
      this.materialSelectionList.splice(index, 1);
    },

    // 处理物资选择变化
    handleMaterialChange(row, index) {
      console.log("handleMaterialChange", this.taskMaterialList);


      const selectedMaterial = this.taskMaterialList.find(item => item.materialId === row.materialId);
      if (selectedMaterial) {
        row.usedNum = selectedMaterial.usedNum;
      }
      const selectPlanMaterial = this.planInfo.materials.find(item => item.materialId === row.materialId);

      if (selectPlanMaterial) {
        row.planNum = selectPlanMaterial.planNum;
        row.materialName = selectPlanMaterial.materialName;
        row.materialSpec = selectPlanMaterial.materialSpec;
      }

      row.remainingNum = row.planNum - row.usedNum;
      row.currentNum = row.planNum - row.usedNum;

      console.log("handleMaterialChange", row, index);

    },

    // 获取物资最大可用数量
    getMaxAvailableNum(row) {
      console.log(111);
      if (!row.materialId) return 0;

      // 从taskMaterialListMap中获取已用数量
      const materialInfo = this.taskMaterialListMap.get(row.materialId);
      const usedNum = materialInfo ? materialInfo.usedNum : 0;

      return row.planNum - usedNum;
    },

    // 处理数量变化
/*     handleNumChange(value, index) { */
      console.log(222);
      if (!this.materialSelectionList[index]) return;

      const row = this.materialSelectionList[index];
      if (!row.materialId) return;

      // 从taskMaterialListMap中获取已用数量
      const materialInfo = this.taskMaterialListMap.get(row.materialId);
      const usedNum = materialInfo ? materialInfo.usedNum : 0;

      // 计算最大可用数量
      const maxAvailable = row.planNum - usedNum;

      // 如果当前值大于最大可用数量，则将值置为最大可用数量
      if (value > maxAvailable) {
        // 显示提示消息
        this.$message({
          message: `数量已自动调整为最大可用数量：${maxAvailable}`,
          type: 'warning',
          duration: 3000,
          showClose: true
        });

        this.$nextTick(() => {
          row.currentNum = maxAvailable;
        });
      }
    },

    // 判断物资是否可选
    isMaterialAvailable(material) {
      // 从taskMaterialListMap中获取已用数量
      // const materialInfo = this.taskMaterialListMap.get(material.id);
      // const usedNum = materialInfo ? materialInfo.usedNum : 0;

      // let selected = false;

      // this.availableMaterials.forEach(item => {
      //   if (item.materialId === material.materialId) {
      //     selected = true;
      //   }
      // });

      return this.materialSelectionList.some(row => row.materialId === material.materialId);;
    },

    // 修改提交派车表单方法
    submitDispatchForm() {
      this.$refs.dispatchForm.validate(valid => {
        if (valid) {
          // 判断非计量且taskType为1的情况
          if (this.planInfo.measureFlag == 0) {
            if (this.dispatchForm.taskType == 1) {
              // 检查是否已经有taskType为1的任务
              const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);
              if (hasType1Task) {
                this.$message.warning('非计量只能派车出厂一次');
                return;
              }
            }
          }

          // 新集合
          let resultList = [];

          console.log("this.planInfo.measureFlag", this.planInfo.measureFlag);
          console.log("this.dispatchForm.taskType", this.dispatchForm.taskType);

          if (this.planInfo.measureFlag == 0 && this.dispatchForm.taskType == 2) {
            this.materialSelectionList.forEach(selRow => {
              // 在 planInfo.materials 中查找相同 materialId 的元素
              const planMaterial = (this.planInfo.materials || []).find(
                mat => mat.materialId === selRow.materialId
              );
              if (planMaterial) {
                // 深拷贝一份，避免影响原数据
                const newItem = { ...planMaterial };
                newItem.planNum = selRow.currentNum; // 设置为本次数量
                resultList.push(newItem);
              }
            });

            // resultList 即为你需要的新集合
            console.log('this.materialSelectionList', this.materialSelectionList);
            console.log('resultList', resultList);

            // 物资校验：必须有物资
            if (!this.materialSelectionList.length) {
              this.$message.warning('请至少选择一种物资');
              return;
            }

            // 校验每一行物资
            const hasInvalidMaterial = this.materialSelectionList.some(row => {
              // 必须选择物资，数量>0，且数量<=剩余数量
              return (
                !row.materialId ||
                row.currentNum <= 0 ||
                row.currentNum > row.remainingNum
              );
            });

            if (hasInvalidMaterial) {
              this.$message.warning('请选择物资且本次数量需大于0且不超过剩余数量');
              return;
            }
          } else {
            console.log("this.planInfo.materials", this.planInfo.materials);
            resultList = this.planInfo.materials ? this.planInfo.materials.map(item => ({ ...item })) : [];
            console.log("123321", resultList);
          }





          if (this.planInfo.measureFlag == 1 && this.dispatchForm.taskType !== 2) {
            this.dispatchForm.taskStatus = 1;
          } else {
            this.dispatchForm.taskStatus = 4;
          }

          if (this.dispatchForm.taskType == 2) {
            this.dispatchForm.taskStatus = 5;
          }


          //是否直供默认为0
          this.dispatchForm.isDirectSupply = 0;
          // todo 任务状态确认
          this.dispatchForm.applyNo = this.applyNo;
          this.dispatchForm.planNo = this.planInfo.planNo;
          this.dispatchForm.carNum = this.dispatchForm.carNumber;
          this.dispatchForm.companyName = this.dispatchForm.company;
          this.dispatchForm.driverLicenseImg = this.dispatchForm.driverLicenseImgs;
          this.dispatchForm.driverName = this.dispatchForm.name;
          this.dispatchForm.mobilePhone = this.dispatchForm.phone;
          this.dispatchForm.faceImg = this.dispatchForm.photo;
          this.dispatchForm.drivingLicenseImg = this.dispatchForm.vehicleLicenseImgs;
          this.dispatchForm.idCardNo = this.dispatchForm.idCard;
          if (this.dispatchForm.sex == "1") {
            this.dispatchForm.sex = 1;
          } else if (this.dispatchForm.sex == "2") {
            this.dispatchForm.sex = 2;
          }
          if (this.dispatchForm.vehicleEmissionStandards == "国五") {
            this.dispatchForm.vehicleEmissionStandards = 1;
          } else if (this.dispatchForm.vehicleEmissionStandards == "国六") {
            this.dispatchForm.vehicleEmissionStandards = 2;
          } else if (this.dispatchForm.vehicleEmissionStandards == "新能源") {
            this.dispatchForm.vehicleEmissionStandards = 3;
          }
          console.log("this.dispatchForm", this.dispatchForm);

          let dispatchInfo = {};
          dispatchInfo.carNum = this.dispatchForm.carNum;

          isAllowDispatch(dispatchInfo).then(response => {
            let row = response.data;
            if (row > 0) {
              this.$message.error("当前车有正在执行的任务")
            } else {
              let param = {};
              param.leaveTask = this.dispatchForm;
              param.leaveTaskMaterialList = resultList;
              addTaskAndMaterialAndAddLeaveLog(param).then(res => {
                console.log("addTaskAndMaterialAndAddLeaveLog", res)
                if (res.code == 200) {
                  this.$message.success('派车成功');
                  this.dispatchDialogVisible = false;
                  this.getListTaskInfo();
                } else {
                  // 其他失败原因
                  this.$message.error(res.message || '派车失败');
                }
              }).catch(err => {
                console.error('dispatch error:', err);
                this.$message.error('网络异常，稍后重试');
              });

              // addTaskAndMaterial(this.dispatchForm).then(response => {
              //   console.log("addTaskAndMaterial", response);
              //   let snowId = response.data;
              //   this.planInfo.materials.forEach(item => {
              //     item.taskNo = snowId;
              //     addTaskMaterial(item);
              //   });

              //   console.log("生成派车日志");

              //   //生成派车日志
              //   let leaveTaskLog = {};


              //   leaveTaskLog.logType = 2;
              //   leaveTaskLog.taskNo = snowId;
              //   leaveTaskLog.applyNo = this.applyNo;
              //   leaveTaskLog.info = '派车任务创建：' + this.dispatchForm.carNum + ' ' + this.dispatchForm.driverName
              //   addLeaveLog(leaveTaskLog);

              //   this.$message.success('派车成功');
              //   this.dispatchDialogVisible = false;
              //   this.getListTaskInfo();
              // });

              this.dispatchDialogVisible = false;
            }
            console.log("this.isAllowDispatch", response);
          }).catch(err => {
            console.error('dispatch error:', err);
            this.$message.error('网络异常，稍后重试');
          });

        } else {
          return false;
        }
      });
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 打印功能
    handlePrint() {
      this.$message.success('打印功能尚未实现');
      // 实际项目中可以调用浏览器打印功能
      // window.print();
    },

    // 返回按钮
    cancel() {
      this.$tab.closeOpenPage(this.$route);
      this.$router.push({ path: "/leave/leavePlanList", query: { t: Date.now() } });
    },

    // 跳转到任务详情页面
    goToTaskDetail(row) {
      this.$router.push({
        path: `/leave/plan/task/${row.taskNo}`
      });
    },

    getTaskTypeText(taskType) {
      const standardMap = {
        1: '出厂',
        2: '返厂',
        3: '跨区调拨'
      };
      return standardMap[taskType] || '未知';
    },

    getStatusText(standard) {
      const standardMap = {
        1: '待过皮重',
        2: '待装货',
        3: '待过毛重',
        4: '待出厂',
        5: '待返厂',
        6: '待过毛重(复磅)',
        7: '待卸货',
        8: '待过皮重(复磅)',
        9: '完成'
      };
      return standardMap[standard] || '未知';
    },

    // 获取计划状态类型
    getPlanStatusType(status) {
      const statusMap = {
        '1': 'warning',  // 待分厂审批
        '2': 'warning',  // 待分厂复审
        '3': 'warning',  // 待生产指挥中心审批
        '4': 'success',  // 审批完成
        '5': 'primary',  // 已出厂
        '6': 'info',     // 部分收货
        '7': 'success',  // 已完成
        '11': 'danger',  // 驳回
        '12': 'danger',  // 废弃
        '13': 'danger'   // 过期
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取计划下所有任务的任务物资
     * @returns {Promise<void>}
     */
    async getAllTaskMaterials() {
      try {
        // 清空现有数据
        this.taskMaterialMap.clear();

        // 获取该计划下所有任务的任务物资
        const params = {
          applyNo: this.applyNo
        };

        const response = await listTaskMaterial(params);
        if (response.code === 200 && response.rows) {
          // 将任务物资按物资ID分组存储
          response.rows.forEach(material => {
            const key = material.materialId;
            if (!this.taskMaterialMap.has(key)) {
              this.taskMaterialMap.set(key, {
                materialId: material.materialId,
                materialName: material.materialName,
                materialSpec: material.materialSpec,
                planNum: material.planNum,
                usedNum: 0,
                taskMaterials: [] // 存储每个任务的具体物资信息
              });
            }

            const materialInfo = this.taskMaterialMap.get(key);
            // 累加每个任务物资的计划数量作为已使用数量
            materialInfo.usedNum += material.planNum;
            materialInfo.taskMaterials.push({
              taskNo: material.taskNo,
              carNum: material.carNum,
              planNum: material.planNum,
              createTime: material.createTime
            });
          });
        }

        // 更新物资选择列表中的已使用数量
        this.updateMaterialUsedNum();

        console.log('Task Material Map:', this.taskMaterialMap);
      } catch (error) {
        console.error('获取任务物资失败:', error);
        this.$message.error('获取任务物资失败');
      }
    },

    /**
     * 更新物资选择列表中的已使用数量
     */
    updateMaterialUsedNum() {
      this.materialSelectionList.forEach(row => {
        if (row.materialId) {
          const materialInfo = this.taskMaterialMap.get(row.materialId);
          if (materialInfo) {
            // 直接使用累加的计划数量作为已使用数量
            row.usedNum = materialInfo.usedNum;
          }
        }
      });
    },

    // 物资确认按钮点击事件
    async handleMaterialConfirm() {
      try {
        // 校验所有任务的taskStatus是否为9
        if (this.taskListInfo && this.taskListInfo.length > 0) {
          const unfinishedTasks = this.taskListInfo.filter(task => task.taskStatus !== 9);
          if (unfinishedTasks.length > 0) {
            this.$message.error('存在未完成的任务，无法进行物资确认');
            return;
          }
        }

        // 调用后端接口，传递applyNo
        await confirmMaterial({ applyNo: this.applyNo });
        this.$message.success('物资确认成功');
        // 刷新详情
        this.getListTaskInfo();
        // 重新获取planInfo
        detailPlan(this.applyNo).then(response => {
          this.planInfo = response.data;
        });
      } catch (e) {
        this.$message.error('物资确认失败');
      }
    },

  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-container {
  margin-bottom: 30px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.section-container:nth-child(1) {
  border-top: 4px solid #8957e5;
  /* 基本信息模块 - 紫色 */
}

.section-container:nth-child(2) {
  border-top: 4px solid #409EFF;
  /* 图片列表模块 - 蓝色 */
}

.section-container:nth-child(3) {
  border-top: 4px solid #F56C6C;
  /* 文件列表模块 - 红色 */
}

.section-container:nth-child(4) {
  border-top: 4px solid #67C23A;
  /* 物资列表模块 - 绿色 */
}

.section-container:nth-child(5) {
  border-top: 4px solid #E6A23C;
  /* 派车信息模块 - 橙色 */
}

.section-container:nth-child(6) {
  border-top: 4px solid #909399;
  /* 日志列表模块 - 灰色 */
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  padding: 15px 20px;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
  position: relative;
  padding-left: 30px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background: currentColor;
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 2px;
}

.section-container:nth-child(1) .section-title {
  color: #8957e5;
}

.section-container:nth-child(2) .section-title {
  color: #409EFF;
}

.section-container:nth-child(3) .section-title {
  color: #F56C6C;
}

.section-container:nth-child(4) .section-title {
  color: #67C23A;
}

.section-container:nth-child(5) .section-title {
  color: #E6A23C;
}

.section-container:nth-child(6) .section-title {
  color: #909399;
}

.section-container .el-descriptions,
.section-container .el-table,
.section-container .el-timeline {
  padding: 0 20px 20px;
}

.fixed-bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px 0;
  text-align: center;
}

.dispatch-btn {
  margin-left: 15px;
}

.empty-data {
  padding: 30px 0;
  display: flex;
  justify-content: center;
}

.el-dialog__body {
  padding: 20px 30px 0;
}

.image-container,
.file-container {
  padding: 20px;
}

.image-list,
.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.image-item {
  width: 150px;
  height: 180px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  cursor: pointer;
}

.image-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.image-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.image-name {
  padding: 5px;
  text-align: center;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #f5f7fa;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 4px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 180px;
  max-width: 250px;
}

.file-item:hover {
  background-color: #ecf5ff;
  color: #409EFF;
}

.file-icon {
  font-size: 24px;
  margin-right: 8px;
  color: #909399;
}

.file-item:hover .file-icon {
  color: #409EFF;
}

.file-name {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 新增物资选择相关样式 */
.el-input-number {
  width: 120px;
}

.material-selection {
  margin-top: 20px;
}

.material-selection .el-table {
  margin-bottom: 10px;
}
</style>

<style lang="scss">
.dispatch-log-dialog .el-dialog__body {
  padding: 20px;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;

  th {
    background-color: #fafafa !important;
    color: #606266;
    font-weight: bold;
  }

  td {
    padding: 12px 0;
  }
}

.el-timeline {
  padding: 20px !important;

  .el-timeline-item__node {
    width: 12px;
    height: 12px;
  }

  .el-timeline-item__content {
    padding: 0 0 0 0px;
  }
}

.el-descriptions {
  .el-descriptions-item__label {
    background-color: #fafafa;
  }
}

.el-tag {
  border-radius: 12px;
  padding: 0 10px;
}

.el-button--text {
  color: #409EFF;
  padding-left: 0;
  padding-right: 0;

  &:hover {
    color: #66b1ff;
    background-color: transparent;
  }

  &:focus {
    color: #409EFF;
  }
}
</style>
