package com.ruoyi.app.leave.mapper;

import java.util.List;
import com.ruoyi.app.leave.domain.DicMeasure;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;

import javax.annotation.Resource;

/**
 * IC卡信息Mapper接口
 * 
 * <AUTHOR>
 */
public interface DicMapper 
{
    /**
     * 查询IC卡信息
     * 
     * @param id IC卡信息主键
     * @return IC卡信息
     */
    @DataSource(DataSourceType.XCC1)
    public DicMeasure selectDicById(Long id);

    /**
     * 查询IC卡信息列表
     * 
     * @param dicMeasure IC卡信息
     * @return IC卡信息集合
     */
    @DataSource(DataSourceType.XCC1)
    public List<DicMeasure> selectDicList(DicMeasure dicMeasure);

    /**
     * 新增IC卡信息
     * 
     * @param dicMeasure IC卡信息
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int insertDic(DicMeasure dicMeasure);

    /**
     * 修改IC卡信息
     * 
     * @param dicMeasure IC卡信息
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int updateDic(DicMeasure dicMeasure);

    /**
     * 删除IC卡信息
     * 
     * @param id IC卡信息主键
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteDicById(Long id);

    /**
     * 批量删除IC卡信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteDicByIds(Long[] ids);

    /**
     * 根据匹配ID删除IC卡信息
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @DataSource(DataSourceType.XCC1)
    public int deleteDicByMatchid(String matchid);

    @DataSource(DataSourceType.XCC1)
    int updateDicByCarNo(DicMeasure dicMeasure);

    @DataSource(DataSourceType.XCC1)
    int deleteDicByCarNo(String carNum);
}