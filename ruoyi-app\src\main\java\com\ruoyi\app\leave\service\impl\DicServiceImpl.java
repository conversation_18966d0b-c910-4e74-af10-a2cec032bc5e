package com.ruoyi.app.leave.service.impl;

import java.math.BigDecimal;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.leave.mapper.DicMapper;
import com.ruoyi.app.leave.domain.DicMeasure;
import com.ruoyi.app.leave.service.IDicService;

/**
 * IC卡信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class DicServiceImpl implements IDicService 
{
    @Autowired
    private DicMapper dicMapper;

    /**
     * 查询IC卡信息
     * 
     * @param id IC卡信息主键
     * @return IC卡信息
     */
    @Override
    public DicMeasure selectDicById(Long id)
    {
        return dicMapper.selectDicById(id);
    }

    /**
     * 查询IC卡信息列表
     * 
     * @param dicMeasure IC卡信息
     * @return IC卡信息
     */
    @Override
    public List<DicMeasure> selectDicList(DicMeasure dicMeasure)
    {
        return dicMapper.selectDicList(dicMeasure);
    }

    /**
     * 新增IC卡信息
     * 
     * @param dicMeasure IC卡信息
     * @return 结果
     */
    @Override
    public int insertDic(DicMeasure dicMeasure)
    {
        return dicMapper.insertDic(dicMeasure);
    }

    /**
     * 修改IC卡信息
     * 
     * @param dicMeasure IC卡信息
     * @return 结果
     */
    @Override
    public int updateDic(DicMeasure dicMeasure)
    {
        return dicMapper.updateDic(dicMeasure);
    }

    /**
     * 批量删除IC卡信息
     * 
     * @param ids 需要删除的IC卡信息主键
     * @return 结果
     */
    @Override
    public int deleteDicByIds(Long[] ids)
    {
        return dicMapper.deleteDicByIds(ids);
    }

    /**
     * 删除IC卡信息信息
     * 
     * @param id IC卡信息主键
     * @return 结果
     */
    @Override
    public int deleteDicById(Long id)
    {
        return dicMapper.deleteDicById(id);
    }

    /**
     * 根据匹配ID删除IC卡信息
     * 
     * @param matchid 匹配ID
     * @return 结果
     */
    @Override
    public int deleteDicByMatchid(String matchid)
    {
        return dicMapper.deleteDicByMatchid(matchid);
    }

    @Override
    public void handleDICTCar(String carNum) {
        DicMeasure dicMeasure = new DicMeasure();
        dicMeasure.setCarno(carNum);
        List<DicMeasure> dicMeasures = dicMapper.selectDicList(dicMeasure);
        if (dicMeasures.size() > 1) {
            throw new RuntimeException("计量D_IC_T表车牌号不唯一");
        }
        DicMeasure dicMeasure1 = dicMeasures.get(0);
        Long type = dicMeasure1.getType();
        if (type == 1) {
            //固定车
            DicMeasure dicMeasure2 = new DicMeasure();
            dicMeasure2.setCarno(carNum);
            dicMeasure2.setPlanid("");
            dicMeasure2.setTaskcode("");
            dicMeasure2.setOrderno("");
            dicMeasure2.setSourcename("");
            dicMeasure2.setSourcecode("");
            dicMeasure2.setTargetcode("");
            dicMeasure2.setTargetname("");
            dicMeasure2.setMaterialname("");
            dicMeasure2.setMaterialcode("");
            dicMeasure2.setMaterialspec("");
            dicMeasure2.setMaterialspeccode("");
            dicMeasure2.setOperatype(-1L);
            dicMeasure2.setShip("");
            dicMeasure2.setShipcode("");
            dicMeasure2.setMflag("0");
            dicMeasure2.setShflag("0");
            dicMeasure2.setPlancount(new BigDecimal(0));
            dicMeasure2.setTarehour(0L);
            dicMeasure2.setBflag("0");
            dicMeasure2.setMsrmemo("");
            dicMeasure2.setMaterialcount(0L);
            dicMeasure2.setMatno("");
            dicMeasure2.setSourcetime(null);
            dicMeasure2.setTargettime(null);
            dicMeasure2.setDeduction(new BigDecimal(0));
            dicMeasure2.setMatchid("");
            dicMeasure2.setGross(new BigDecimal(0));
            dicMeasure2.setGrosslogid("");
            dicMeasure2.setGrossoperator("");
            dicMeasure2.setGrossoperatorid("");
            dicMeasure2.setGrosstime(null);
            dicMeasure2.setGrossweigh("");
            dicMeasure2.setGrossweighid("");
            dicMeasure2.setTare(new BigDecimal(0));
            dicMeasure2.setTarelogid("");
            dicMeasure2.setTareoperator("");
            dicMeasure2.setTareoperatorid("");
            dicMeasure2.setTaretime(null);
            dicMeasure2.setTareweigh("");
            dicMeasure2.setTareweighid("");
            int i = dicMapper.updateDicByCarNo(dicMeasure2);
            if (i <= 0) {
                throw new RuntimeException("D_IC_T表固定车更新失败");
            }
        } else if (type == 0) {
            //临时车
            int i = dicMapper.deleteDicByCarNo(carNum);
            if (i <= 0) {
                throw new RuntimeException("D_IC_T表临时车删除失败");
            }
        }
    }
} 