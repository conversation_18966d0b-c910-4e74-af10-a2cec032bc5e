{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue", "mtime": 1756428094595}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_task", "require", "_plan", "_xctgDriverCar", "_elementUi", "_qrcodejs", "_interopRequireDefault", "_fileReader", "name", "data", "factoryConfirmDialogVisible", "factoryConfirmForm", "companyName", "taskNo", "applyNo", "planNo", "taskType", "unloadingWorkNo", "unloadingTime", "spec1Length", "spec2Width", "totals", "total", "totalUnit", "processType", "heatNo", "steelGrade", "axles", "remark", "taskStatus", "carNum", "stockOutSpec1Length", "stockOutSpec2Width", "stockOutTotals", "stockOutTotalUnit", "stockOutTotal", "stockOutProcessType", "stockOutHeatNo", "stockOutSteelGrade", "stockOutAxles", "stockOutRemark", "handledMaterialName", "sourceCompany", "receiveCompany", "showDropdown", "extraOption", "deductWeight", "optionDialogVisible", "searchForm", "optionList", "editDoorManStatus", "editFactoryStatus", "driverInfo", "id", "idCard", "phone", "gender", "company", "photo", "driverLicenseImgs", "vehicleLicenseImgs", "carInfo", "taskMaterials", "taskLogs", "isdoorMan", "dispatchId", "taskInfoForm", "measureFlag", "backupTaskMaterials", "selectedOption", "planForm", "processTypeOptions", "filteredProcessTypeOptions", "searchProcessTypeQuery", "directSupplyPlanList", "editingRow", "selectedRows", "directSupplyParams", "computed", "displayProcessTypeOptions", "hasSelectedItems", "length", "materialNames", "map", "item", "materialName", "join", "materialSpecs", "materialSpec", "activated", "console", "log", "resetTaskInfoForm", "$route", "params", "query", "validDoorMan", "initializeDataByTaskNo", "_this$$route$query", "planType", "queryTaskNo", "initializeData", "methods", "callApiWithGateLocation", "param", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "clientIp", "_t", "w", "_context", "n", "p", "getClientIpSilent", "v", "addLeaveLogAndEditTaskMaterialsAndUpdateTask", "a", "error", "getDirectSupplyPlanAndTask", "_this", "leaveTask0", "directSupplyTaskNo", "getDirectSupplyPlanAndTaskDetail", "then", "res", "code", "rows", "$message", "message", "catch", "err", "_this2", "$store", "getters", "roles", "for<PERSON>ach", "_this3", "_callee2", "_t2", "_context2", "getTaskInfo", "getTaskmaterialList", "getPlanInfo", "uploadFactoryConfirmForm", "getTaskLogList", "getProcessType", "_this4", "_callee3", "_t3", "_context3", "getTaskInfoByTaskNo", "_this5", "doorman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "planNum", "doormanReceiveNumIn", "gross", "secGross", "<PERSON><PERSON><PERSON>", "tare", "Date", "openNewWindow", "newWindowUrl", "window", "open", "getDirectSupplyList", "_this6", "_callee4", "leavePlan", "_t4", "_context4", "getDirectSupplyPlans", "filterProcessType", "filter", "value", "includes", "_this7", "getProcessList", "processname", "label", "_this8", "_callee5", "response", "_t5", "_context5", "detailPlan", "openFactoryConfirmDialog", "submitFactoryConfirm", "_this9", "submitData", "isDirectSupply", "leaveTask", "leaveTaskMaterial", "directSupplyTask", "secGrossTime", "sex", "mobilePhone", "idCardNo", "vehicleEmissionStandards", "faceImg", "drivingLicenseImg", "driverLicenseImg", "directSupplyTaskMaterialList", "handleUnload", "success", "submitStockOutConfirm", "_this0", "handleStockOut", "handleFactoryConfirm", "_this1", "warning", "leaveTaskLog", "logType", "info", "factoryTaskInfo", "taskMaterialList", "leaveLog", "handleDoorManConfirm", "_this10", "doorManTaskInfo", "leaveTime", "toISOString", "slice", "replace", "enterTime", "handleDoorManMeasureConfirm", "_this11", "creatQrCode", "qrCode<PERSON>ontent", "$refs", "qrCode", "innerHTML", "YSqrCode", "QRCode", "text", "width", "height", "colorDark", "colorLight", "correctLevel", "CorrectLevel", "H", "_this12", "taskLog", "getTaskLogs", "logs", "finishedLogs", "otherLogs", "concat", "_toConsumableArray2", "_this13", "_callee6", "leaveMaterial", "_t6", "_context6", "getTaskmaterials", "editDoorManRow", "row", "_backup", "JSON", "parse", "stringify", "editFactoryRow", "backupMaterials", "cancelDoorManEdit", "Object", "assign", "cancelFactoryEdit", "saveDoorManRowIn", "_this14", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "e", "f", "saveDoorManRow", "_this15", "_iterator2", "_step2", "saveFactoryRow", "_this16", "_callee7", "_t7", "_context7", "getTask", "licensePlateColor", "$nextTick", "_this17", "_callee8", "_t8", "_context8", "getTaskByTaskNo", "getStatusText", "standard", "standardMap", "getPlanStatusText", "getEmissionStandardsText", "getEmissionStandardsTagType", "typeMap", "getMaterialStatusText", "status", "statusMap", "getMaterialStatusType", "getLogColor", "logTypeColorMap", "type", "cancel", "$router", "go", "getTaskDetail", "handleShowDropdownChange", "val", "openOptionDialog", "_this18", "loadOptions", "optionTable", "clearSelection", "handleOptionSelection", "selection", "lastSelected", "toggleRowSelection", "confirmOptionSelection", "_this19", "planStatus", "getBusinessCategoryText", "category", "categoryMap", "searchOptions", "_this20", "searchPlanNo", "toLowerCase", "searchApplyNo", "searchReceiveCompany", "toString", "matchPlanNo", "matchApplyNo", "matchReceiveCompany", "resetSearch", "getTaskTypeText", "handleSelectionChange", "handleNonMeasureFactoryConfirm", "_this21", "isHandled", "factoryReceiveNum", "openNewTaskWindow", "BigInt", "url"], "sources": ["src/views/leave/plan/task.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\" style=\"display: flex; align-items: center; justify-content: flex-start;\">\r\n        <h2>派车任务详情</h2>\r\n        <el-tag size=\"medium\" style=\"margin-left: 20px; margin-top: 10px;\">\r\n          任务状态： {{ getStatusText(taskInfoForm.taskStatus) }}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <!-- 任务流程图部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务流程</div>\r\n        <div class=\"process-flow-container\">\r\n          <!-- <img style=\"width: 100%; max-height: 400px; object-fit: contain;\" :src=\"require('@/assets/images/task-flow-chart.png')\" /> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 通行证二维码部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">通行证二维码</div>\r\n        <div class=\"qrcode-container\">\r\n          <div ref=\"qrCode\" class=\"qrcode\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 司机信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">司机信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"姓名\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 姓名</template>\r\n            {{ taskInfoForm.driverName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">\r\n            <template slot=\"label\"><i class=\"el-icon-mobile-phone\"></i> 手机号</template>\r\n            {{ taskInfoForm.mobilePhone }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"身份证号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 身份证号</template>\r\n            {{ taskInfoForm.idCardNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"性别\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 性别</template>\r\n            {{ taskInfoForm.sex === 1 ? '男' : '女' }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"所属单位\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 所属单位</template>\r\n            {{ taskInfoForm.companyName }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 司机照片和证件照片 -->\r\n        <div class=\"driver-photos\"\r\n          v-if=\"driverInfo.photo || driverInfo.driverLicenseImgs || driverInfo.vehicleLicenseImgs\">\r\n          <div class=\"photo-item\" v-if=\"driverInfo.photo\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 司机照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.faceImg\" alt=\"司机照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.faceImg\" fit=\"contain\" fallback=\"\"\r\n                :preview-src-list=\"[taskInfoForm.faceImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.driverLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 驾驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.driverLicenseImg\" alt=\"驾驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.driverLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.driverLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.vehicleLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 行驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.drivingLicenseImg\" alt=\"行驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.drivingLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.drivingLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 车辆信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">车辆信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"车牌号\" v-if=\"taskInfoForm.carNum != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 车牌号</template>\r\n            <el-tag type=\"primary\">{{ taskInfoForm.carNum }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车牌颜色\" v-if=\"taskInfoForm.licensePlateColor != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-takeaway-box\"></i> 车牌颜色</template>\r\n            {{ taskInfoForm.licensePlateColor }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆道路运输证号\" v-if=\"taskInfoForm.trailerId != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"挂车号牌\" v-if=\"taskInfoForm.trailerNumber\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 挂车号牌</template>\r\n            <el-tag type=\"info\">{{ taskInfoForm.trailerNumber }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"挂车道路运输证号\" v-if=\"taskInfoForm.trailerId\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 挂车运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"轴型\" v-if=\"taskInfoForm.axisType != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-data-line\"></i> 轴型</template>\r\n            {{ taskInfoForm.axisType }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"货车自重\" v-if=\"taskInfoForm.driverWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-heavy-rain\"></i> 货车自重</template>\r\n            {{ taskInfoForm.driverWeight }} kg\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车货总质量限值\" v-if=\"taskInfoForm.maxWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-opportunity\"></i> 总质量限值</template>\r\n            {{ taskInfoForm.maxWeight }} kg\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆排放标准\" v-if=\"taskInfoForm.vehicleEmissionStandards != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-magic-stick\"></i> 排放标准</template>\r\n            <el-tag :type=\"getEmissionStandardsTagType(taskInfoForm.vehicleEmissionStandards)\">\r\n              {{ getEmissionStandardsText(taskInfoForm.vehicleEmissionStandards) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"发动机号\" v-if=\"taskInfoForm.engineNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-set-up\"></i> 发动机号</template>\r\n            {{ taskInfoForm.engineNumber }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆识别代码\" v-if=\"taskInfoForm.vinNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document-checked\"></i> 车辆识别代码</template>\r\n            {{ taskInfoForm.vinNumber }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 任务物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"taskMaterials\" style=\"width: 100%\" border @selection-change=\"handleSelectionChange\">\r\n          <!-- <el-table-column type=\"selection\" width=\"55\" v-if=\"measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"150\">\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0\" -->\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫出厂确认数量\" width=\"230\"\r\n            v-if=\"taskInfoForm.taskStatus >= 4 && measureFlag == 0 && taskInfoForm.taskType != 2\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 4\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNumIn\" label=\"门卫入厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskType !== 1 && taskInfoForm.taskStatus >= 5\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNumIn\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 5\" />\r\n            </template>\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0 && taskInfoForm.taskType == 2\" -->\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"分厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskStatus > 7 && (taskInfoForm.taskType == 3 || taskInfoForm.taskType == 2)\">\r\n            <!-- <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\" disabled />\r\n            </template> -->\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\">\r\n          </el-table-column>\r\n\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5 && (measureFlag == 0 && taskInfoForm.taskType == 2 && taskInfoForm.taskStatus == 7)\" -->\r\n          <!-- <el-table-column v-if=\"measureFlag == 0 && (taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5)\"\r\n            label=\"操作\" width=\"200\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px;\">\r\n\r\n                <div v-if=\"editingRow === scope.row\">\r\n                  <el-button size=\"mini\" type=\"success\" @click=\"saveDoorManRow(scope.row)\">保存</el-button>\r\n                  <el-button size=\"mini\" @click=\"cancelDoorManEdit(scope.row)\">取消</el-button>\r\n                </div>\r\n\r\n                <div v-else>\r\n                  <el-button v-hasPermi=\"['leave:task:doorManConfirm']\" size=\"mini\" type=\"primary\"\r\n                    @click=\"editDoorManRow(scope.row)\">门卫编辑</el-button>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column> -->\r\n        </el-table>\r\n\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 4\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRow\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫出厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 5\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRowIn\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫入厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"button-container\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 7\">\r\n          <el-button type=\"primary\" @click=\"handleNonMeasureFactoryConfirm\">\r\n            分厂确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1\">\r\n        <div class=\"section-title\">计量信息</div>\r\n        <div class=\"info-footer\" style=\"margin-top: 20px;\" v-if=\"measureFlag == 1\">\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"皮重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tare != null\">\r\n              {{ taskInfoForm.tare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.gross != null\">\r\n              {{ taskInfoForm.gross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.netWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tareTime != null\">\r\n              {{ taskInfoForm.tareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.grossTime != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secTare != null\">\r\n              {{ taskInfoForm.secTare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secGross != null\">\r\n              {{ taskInfoForm.secGross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeight != null\">\r\n              {{ taskInfoForm.secNetWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secTareTime != null\">\r\n              {{ taskInfoForm.secTareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secGrossTime != null\">\r\n              {{ taskInfoForm.secGrossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeightTime != null\">\r\n              {{ taskInfoForm.secNetWeightTime }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5\" -->\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 4\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫出厂确认\r\n            </el-button>\r\n          </div>\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 5\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫入厂确认\r\n            </el-button>\r\n          </div>\r\n          <!-- 新增分厂确认按钮 -->\r\n          <!-- <div class=\"btn-wrapper\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"openFactoryConfirmDialog\" class=\"dispatch-btn\">\r\n              分厂确认\r\n            </el-button>\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可编辑的出库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 2\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialName).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialSpec).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\" v-if=\"taskInfoForm.taskType\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSpec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutTotal\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutTotalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutProcessType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutHeatNo\" placeholder=\"请输入炉号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSteelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutAxles\" placeholder=\"请选择轴数\">\r\n                  <el-option label=\"2\" value=\"2\"></el-option>\r\n                  <el-option label=\"3\" value=\"3\"></el-option>\r\n                  <el-option label=\"4\" value=\"4\"></el-option>\r\n                  <el-option label=\"5\" value=\"5\"></el-option>\r\n                  <el-option label=\"6轴标准\" value=\"6轴标准\"></el-option>\r\n                  <el-option label=\"6轴非标准\" value=\"6轴非标准\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.stockOutRemark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitStockOutConfirm\" size=\"medium\" class=\"dispatch-btn\">确认出库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的出库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 2 && taskInfoForm.taskType !== 2 && taskInfoForm.isDirectSupply != 3\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"materialSpecs\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.stockOutSpec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.stockOutTotals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.stockOutProcessType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.stockOutHeatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.stockOutSteelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-input :value=\"taskInfoForm.stockOutAxles\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.stockOutRemark\" disabled></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 可编辑的入库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 7\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.secGross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.gross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.processType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.steelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input v-model=\"factoryConfirmForm.spec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.total\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.totalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.heatNo\" placeholder=\"请输入炉号/批号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重(t)\">\r\n                <el-input v-model=\"factoryConfirmForm.deductWeight\" placeholder=\"请输入扣重\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.remark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"是否直供\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-checkbox v-model=\"factoryConfirmForm.showDropdown\" @change=\"handleShowDropdownChange\">是否直供</el-checkbox>\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"factoryConfirmForm.showDropdown\" label=\"直供申请单号\">\r\n            <el-input v-model=\"factoryConfirmForm.extraOption\" placeholder=\"请选择直供申请单号\" readonly style=\"width: 300px;\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openOptionDialog\"></el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitFactoryConfirm\" size=\"medium\" class=\"dispatch-btn\">确认入库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的入库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 7 && taskInfoForm.taskType !== 1\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.gross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.processType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.steelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.spec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.totals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.heatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重\">\r\n                <el-input :value=\"taskInfoForm.deductWeight\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.remark\" disabled></el-input>\r\n          </el-form-item>\r\n\r\n\r\n          <el-form-item v-if=\"taskInfoForm.directSupplyTaskNo\" label=\"直供对应任务单号\">\r\n            <el-input :value=\"taskInfoForm.directSupplyTaskNo\" disabled style=\"width: 300px;\"></el-input>\r\n            <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n              @click=\"openNewTaskWindow\">前往任务单号</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务日志</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in taskLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <div class=\"form-footer\">\r\n        <el-button @click=\"cancel\">返 回</el-button>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 选项弹窗 -->\r\n    <el-dialog title=\"选择直供申请单号\" :visible.sync=\"optionDialogVisible\" width=\"1600px\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"计划号\">\r\n          <el-input v-model=\"searchForm.planNo\" placeholder=\"请输入计划号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请编号\">\r\n          <el-input v-model=\"searchForm.applyNo\" placeholder=\"请输入申请编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位\">\r\n          <el-input v-model=\"searchForm.receiveCompany\" placeholder=\"请输入收货单位\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"searchOptions\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewWindow\">直供对应任务号\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table :data=\"optionList\" style=\"width: 100%\" @selection-change=\"handleOptionSelection\" ref=\"optionTable\">\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        <el-table-column prop=\"planNo\" label=\"计划号\" width=\"150\" />\r\n        <el-table-column prop=\"applyNo\" label=\"申请编号\" width=\"150\" />\r\n        <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\" />\r\n        <el-table-column prop=\"materialSpec\" label=\"物料规格\" width=\"120\" />\r\n        <el-table-column prop=\"sourceCompany\" label=\"申请单位\" width=\"150\" />\r\n        <el-table-column prop=\"receiveCompany\" label=\"收货单位\" width=\"150\" />\r\n        <el-table-column prop=\"plannedAmount\" label=\"计划量/t\" width=\"150\" />\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"planStatus\" label=\"状态\" width=\"150\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"optionDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOptionSelection\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getTask, getTaskByTaskNo, getTaskmaterials, getProcessList, getDirectSupplyPlans, getDirectSupplyPlanAndTaskDetail, handleUnload, handleStockOut, isAllowDispatch, getPlanMaterials, editTaskmaterials, getTaskLogs, addLeaveLog, updateTask, addLeaveLogAndEditTaskMaterialsAndUpdateTask } from \"@/api/leave/task\";\r\nimport { detailPlan } from \"@/api/leave/plan\";\r\nimport { listXctgDriverCar, getXctgDriverCar, delXctgDriverCar, addXctgDriverCar, updateXctgDriverCar, exportXctgDriverCar } from \"@/api/truck/common/xctgDriverCar\";\r\nimport { Message } from \"element-ui\";\r\nimport QRCode from \"qrcodejs2\";\r\nimport { getClientIpSilent } from \"@/utils/fileReader\";\r\n\r\n\r\nexport default {\r\n  name: \"DispatchTaskDetail\",\r\n  data() {\r\n    return {\r\n      factoryConfirmDialogVisible: false,\r\n      factoryConfirmForm: {\r\n        companyName: '',\r\n        taskNo: '',\r\n        applyNo: '',\r\n        planNo: '',\r\n        taskType: null,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: null,\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9, // 完成状态\r\n        carNum: '', // 车牌号\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        handledMaterialName: '',\r\n        sourceCompany: '',\r\n        receiveCompany: '',\r\n        showDropdown: false,\r\n        extraOption: '',\r\n        deductWeight: null, // 添加扣重字段\r\n      },\r\n      optionDialogVisible: false,\r\n      searchForm: {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      },\r\n      optionList: [],\r\n      editDoorManStatus: false,\r\n      editFactoryStatus: false,\r\n      // 司机信息\r\n      driverInfo: {\r\n        id: 1,\r\n        name: '王小明',\r\n        idCard: '110101199001010001',\r\n        phone: '13800138000',\r\n        gender: '1',\r\n        company: '北京运输有限公司',\r\n        photo: 'https://via.placeholder.com/150',\r\n        driverLicenseImgs: 'https://via.placeholder.com/300x200',\r\n        vehicleLicenseImgs: 'https://via.placeholder.com/300x200'\r\n      },\r\n\r\n      // 车辆信息\r\n      carInfo: {},\r\n\r\n      // 任务物资列表\r\n      taskMaterials: [],\r\n\r\n      // 任务日志列表\r\n      taskLogs: [],\r\n\r\n      // 申请编号\r\n      applyNo: null,\r\n\r\n      isdoorMan: false,\r\n\r\n      // 派车任务ID\r\n      dispatchId: null,\r\n\r\n      taskInfoForm: {},\r\n\r\n      measureFlag: null,\r\n\r\n      backupTaskMaterials: null,\r\n      taskNo: null,\r\n\r\n      selectedOption: null,\r\n\r\n      planForm: {},\r\n\r\n      processTypeOptions: [], // 动态加载的加工类型选项\r\n\r\n      filteredProcessTypeOptions: [], // 过滤后的加工类型选项\r\n\r\n      searchProcessTypeQuery: '',// 搜索框的值\r\n\r\n      directSupplyPlanList: [], // 直供计划列表\r\n\r\n      editingRow: null,\r\n\r\n      selectedRows: [], // 添加选中行数据数组\r\n\r\n      directSupplyParams: {}\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    displayProcessTypeOptions() {\r\n      return this.searchProcessTypeQuery ? this.filteredProcessTypeOptions : this.processTypeOptions;\r\n    },\r\n\r\n    // 是否有选中的项\r\n    hasSelectedItems() {\r\n      return this.selectedRows.length > 0;\r\n    },\r\n\r\n    // 添加计算属性\r\n    materialNames() {\r\n      return this.taskMaterials.map(item => item.materialName).join(' ');\r\n    },\r\n\r\n    materialSpecs() {\r\n      return this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n    }\r\n  },\r\n\r\n  activated() {\r\n    console.log(\"activated执行\");\r\n    this.resetTaskInfoForm();\r\n\r\n    // 获取路由参数 - 支持两种方式：query参数和路径参数\r\n    let taskNo = this.$route.params.taskNo || this.$route.query.taskNo;\r\n\r\n    if (taskNo) {\r\n      // 新的方式：通过taskNo获取所有参数\r\n      this.taskNo = taskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeDataByTaskNo();\r\n    } else {\r\n      // 兼容旧的方式：从query参数获取\r\n      const { dispatchId, applyNo, measureFlag, planType, taskNo: queryTaskNo } = this.$route.query;\r\n      this.dispatchId = dispatchId;\r\n      this.applyNo = applyNo;\r\n      this.measureFlag = measureFlag;\r\n      console.log(\"this.measureFlag\", this.measureFlag)\r\n      this.planType = planType;\r\n      this.taskNo = queryTaskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeData();\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    /**\r\n     * 调用带客户端IP信息的接口\r\n     * @param {Object} param 接口参数\r\n     * @returns {Promise} 接口调用结果\r\n     */\r\n    async callApiWithGateLocation(param) {\r\n      try {\r\n        // 获取客户端IP地址\r\n        const clientIp = await getClientIpSilent();\r\n\r\n        // 将客户端IP添加到参数中\r\n        param.clientIp = clientIp;\r\n        console.log('获取到客户端IP地址:', clientIp, '已添加到请求参数中');\r\n\r\n        // 调用接口\r\n        return await addLeaveLogAndEditTaskMaterialsAndUpdateTask(param);\r\n      } catch (error) {\r\n        console.error('调用接口失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    getDirectSupplyPlanAndTask() {\r\n\r\n\r\n      let leaveTask0 = {\r\n        taskNo: this.taskInfoForm.directSupplyTaskNo\r\n      }\r\n\r\n      getDirectSupplyPlanAndTaskDetail(leaveTask0).then(res => {\r\n        console.log(\"getDirectSupplyPlanAndTaskDetail\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyParams.dispatchId = res.rows[0].id;\r\n          this.directSupplyParams.applyNo = res.rows[0].applyNo;\r\n          this.directSupplyParams.taskNo = res.rows[0].taskNo;\r\n          this.directSupplyParams.measureFlag = res.rows[1].measureFlag;\r\n          this.directSupplyParams.planType = res.rows[1].planType;\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getDirectSupplyPlanAndTaskDetail error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      });\r\n\r\n    },\r\n\r\n    validDoorMan() {\r\n      this.$store.getters.roles.forEach(item => {\r\n        if (item == 'leave.quard') {\r\n          this.isdoorMan = true;\r\n        }\r\n      });\r\n      console.log(\"isdoorMan\", this.isdoorMan)\r\n    },\r\n    async initializeData() {\r\n      try {\r\n        // 等待所有异步操作完成\r\n        await this.getTaskInfo();\r\n        await this.getTaskmaterialList(this.taskNo);\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    async initializeDataByTaskNo() {\r\n      try {\r\n        // 通过taskNo获取任务信息\r\n        await this.getTaskInfoByTaskNo();\r\n\r\n        // 通过applyNo获取计划信息\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 获取任务物资列表\r\n        await this.getTaskmaterialList(this.taskNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data by taskNo:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    uploadFactoryConfirmForm() {\r\n      // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n      this.taskMaterials.forEach(item => {\r\n        item.doormanReceiveNum = item.planNum;\r\n        console.log(\"item.planType\", this.planForm.planType);\r\n        if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n          item.doormanReceiveNumIn = item.planNum;\r\n        }\r\n      });\r\n\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      let materialSpecs = this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        driverName: this.taskInfoForm.driverName,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        materialSpecs: materialSpecs,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n    },\r\n\r\n    openNewWindow() {\r\n      const newWindowUrl = 'http://localhost/leave/leavePlanList'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    //获取可以直供的计划\r\n    async getDirectSupplyList() {\r\n      try {\r\n        let leavePlan = {\r\n          sourceCompany: this.planForm.sourceCompany,\r\n          planType: 3,\r\n        }\r\n        console.log(\"获取可以直供的计划\", leavePlan)\r\n\r\n        const res = await getDirectSupplyPlans(leavePlan);\r\n        console.log(\"getDirectSupplyPlans\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyPlanList = res.rows;\r\n          // //查询每个计划的物资\r\n          // for (const item of this.directSupplyPlanList) {\r\n          //   console.log(\"item\", item)\r\n          //   let leavePlanMaterial = {\r\n          //     applyNo: item.applyNo\r\n          //   };\r\n          //   const response = await getPlanMaterials(leavePlanMaterial);\r\n          //   if (response.code == 200) {\r\n          //     console.log(\"getPlanMaterials\", response)\r\n          //     item.materialName = response.rows[0].materialName;\r\n          //     item.materialSpec = response.rows[0].materialSpec;\r\n          //   } else {\r\n          //     this.$message.error(response.message || '获取计划物资失败');\r\n          //   }\r\n          // }\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      } catch (err) {\r\n        console.error('getDirectSupplyPlans error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      }\r\n    },\r\n    filterProcessType(query) {\r\n      this.searchProcessTypeQuery = query;\r\n\r\n      if (this.searchProcessTypeQuery) {\r\n        console.log(\"processTypeOptions\", this.processTypeOptions)\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions.filter(item =>\r\n          item.value.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions;\r\n      }\r\n    },\r\n    getProcessType() {\r\n      getProcessList().then(res => {\r\n        console.log(\"getProcessList\", res)\r\n        if (res.code == 200) {\r\n          this.processTypeOptions = res.rows.map(item => ({\r\n            value: item.processname,\r\n            label: item.processname\r\n          }));\r\n          this.filteredProcessTypeOptions = this.processTypeOptions; // 初始化过滤后的选项\r\n        } else {\r\n          this.$message.error(res.message || '获取加工类型失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getProcessList error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    async getPlanInfo(applyNo) {\r\n      try {\r\n        const response = await detailPlan(applyNo);\r\n        console.log(\"detailPlan\", response);\r\n        this.planForm = response.data;\r\n\r\n        // 从计划信息中获取planType和measureFlag\r\n        this.planType = this.planForm.planType;\r\n        this.measureFlag = this.planForm.measureFlag;\r\n        console.log(\"this.planType\", this.planType);\r\n        console.log(\"this.measureFlag\", this.measureFlag);\r\n\r\n        await this.getDirectSupplyList();\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getPlanInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    openFactoryConfirmDialog() {\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n      this.factoryConfirmDialogVisible = true;\r\n    },\r\n    submitFactoryConfirm() {\r\n      if (this.factoryConfirmForm.showDropdown == true) {\r\n        if (this.factoryConfirmForm.extraOption == null || this.factoryConfirmForm.extraOption == '') {\r\n          this.$message.error('请选择额外选项');\r\n          return;\r\n        }\r\n      }\r\n\r\n      let submitData = {};\r\n      if (this.taskInfoForm.isDirectSupply == 3) {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 3,\r\n            planNo: this.taskInfoForm.planNo,\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      } else {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            planNo: this.taskInfoForm.planNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 0, // 默认不是直供\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            directSupplyTaskNo: this.factoryConfirmForm.extraOption,\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      }\r\n\r\n\r\n\r\n      let directSupplyTask = {\r\n        //taskNo后台雪花生成\r\n        applyNo: this.factoryConfirmForm.extraOption,\r\n        taskType: 3,\r\n        taskStatus: 7,\r\n        secGross: this.taskInfoForm.secGross,\r\n        secGrossTime: this.taskInfoForm.secGrossTime,\r\n        planNo: this.taskInfoForm.planNo,\r\n        driverName: this.taskInfoForm.driverName,\r\n        sex: this.taskInfoForm.sex,\r\n        mobilePhone: this.taskInfoForm.mobilePhone,\r\n        idCardNo: this.taskInfoForm.idCardNo,\r\n        carNum: this.taskInfoForm.carNum,\r\n        vehicleEmissionStandards: this.taskInfoForm.vehicleEmissionStandards,\r\n        faceImg: this.taskInfoForm.faceImg,\r\n        drivingLicenseImg: this.taskInfoForm.drivingLicenseImg,\r\n        driverLicenseImg: this.taskInfoForm.driverLicenseImg,\r\n        companyName: this.taskInfoForm.companyName,\r\n        isDirectSupply: 3\r\n      };\r\n\r\n      let directSupplyTaskMaterialList = this.taskMaterials;\r\n\r\n      if (this.factoryConfirmForm.showDropdown == true && this.factoryConfirmForm.extraOption != null && this.factoryConfirmForm.extraOption != '') {\r\n        submitData.leaveTask.isDirectSupply = 1; // 设置为直供\r\n        submitData.directSupplyTask = directSupplyTask;\r\n        submitData.directSupplyTaskMaterialList = directSupplyTaskMaterialList;\r\n      }\r\n\r\n      handleUnload(submitData).then(res => {\r\n        console.log(\"handleUnload\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认入库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认入库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    submitStockOutConfirm() {\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有确认出库权限');\r\n        return;\r\n      }\r\n      // 构建提交数据\r\n      let submitData = {\r\n        leaveTask: {\r\n          //todo 计量系统补充信息待完善\r\n          id: this.dispatchId,\r\n          taskNo: this.taskNo,\r\n          applyNo: this.applyNo,\r\n          planNo: this.taskInfoForm.planNo,\r\n          // 出库信息\r\n          stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n          stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n          stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n          stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n          stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n          stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n          stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n          stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n\r\n          // 更改任务状态: 9\r\n          taskStatus: 3,\r\n          carNum: this.taskInfoForm.carNum,\r\n        },\r\n        leavePlan: this.planForm,\r\n        leaveTaskMaterial: this.taskMaterials[0],\r\n      };\r\n\r\n      handleStockOut(submitData).then(res => {\r\n        console.log(\"handleStockOut\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认出库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认出库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    handleFactoryConfirm() {\r\n      if (this.editFactoryStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n\r\n      //todo\r\n      //生成派车日志\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '分厂确认数量';\r\n\r\n\r\n      let factoryTaskInfo = {}\r\n      //todo 出入场\r\n      factoryTaskInfo.id = this.taskInfoForm.id\r\n      factoryTaskInfo.unloadingWorkNo = '卸货人占位符'\r\n      factoryTaskInfo.unloadingTime = new Date()\r\n      factoryTaskInfo.taskStatus = 9\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = factoryTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '分厂确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n\r\n    handleDoorManConfirm() {\r\n      if (this.editDoorManStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n\r\n\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      // this.taskMaterials.map(item => {\r\n      //   editTaskmaterials(item);\r\n      // })\r\n      //todo\r\n      // let leaveTaskLog = {};\r\n      // leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n      // addLeaveLog(leaveTaskLog);\r\n      // this.getTaskLogList(this.taskNo);\r\n\r\n      // let doorManTaskInfo = {}\r\n      // doorManTaskInfo.id = this.taskInfoForm.id\r\n      // if (this.taskInfoForm.taskType == 1) {\r\n      //   doorManTaskInfo.taskStatus = 9\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n      //   doorManTaskInfo.taskStatus = 5\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // }\r\n      // updateTask(doorManTaskInfo);\r\n      // this.$message.success('门卫确认成功');\r\n\r\n      // setTimeout(() => {\r\n      //   this.getTaskInfo();\r\n      // }, 500)\r\n\r\n    },\r\n\r\n    handleDoorManMeasureConfirm() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      if (this.taskInfoForm.taskStatus == 4) {\r\n        leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      } else {\r\n        leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      }\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      doorManTaskInfo.taskType = this.taskInfoForm.taskType\r\n      doorManTaskInfo.carNum = this.taskInfoForm.carNum\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n      //todo\r\n\r\n    },\r\n    // 生成二维码\r\n    creatQrCode() {\r\n      if (this.taskInfoForm.qrCodeContent) {\r\n        this.$refs.qrCode.innerHTML = \"\";\r\n        var YSqrCode = new QRCode(this.$refs.qrCode, {\r\n          text: this.taskInfoForm.qrCodeContent, // 需要转换为二维码的内容\r\n          width: 120,\r\n          height: 120,\r\n          colorDark: \"#000000\",\r\n          colorLight: \"#ffffff\",\r\n          correctLevel: QRCode.CorrectLevel.H,\r\n        });\r\n      }\r\n    },\r\n    getTaskLogList(taskNo) {\r\n      let taskLog = {};\r\n      taskLog.taskNo = taskNo\r\n      getTaskLogs(taskLog).then(response => {\r\n        console.log(\"getTaskLogs\", response);\r\n        // this.taskLogs = response.rows;\r\n        let logs = response.rows || [];\r\n        // 找出包含\"任务完成\"的日志\r\n        const finishedLogs = logs.filter(log => log.info && log.info.includes('任务完成'));\r\n        const otherLogs = logs.filter(log => !(log.info && log.info.includes('任务完成')));\r\n        // 先放\"任务完成\"，再放其他\r\n        this.taskLogs = [...finishedLogs, ...otherLogs];\r\n      })\r\n\r\n    },\r\n    async getTaskmaterialList(taskNo) {\r\n      try {\r\n        console.log(\"getTaskmaterialList\");\r\n        let leaveMaterial = {};\r\n        leaveMaterial.taskNo = taskNo;\r\n        const response = await getTaskmaterials(leaveMaterial);\r\n        this.taskMaterials = response.rows;\r\n        // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n        this.taskMaterials.forEach(item => {\r\n          item.doormanReceiveNum = item.planNum;\r\n          console.log(\"item.planType\", this.planForm.planType);\r\n          if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n            item.doormanReceiveNumIn = item.planNum;\r\n          }\r\n        });\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskmaterialList error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    editDoorManRow(row) {\r\n      row._backup = JSON.parse(JSON.stringify(row));//深拷贝\r\n      this.editingRow = row;\r\n      this.editDoorManStatus = true;\r\n      console.log(\"this.editDoorManRow\", row);\r\n    },\r\n    editFactoryRow() {\r\n      this.backupMaterials = JSON.parse(JSON.stringify(this.taskMaterials));//深拷贝\r\n      this.editFactoryStatus = true;\r\n    },\r\n    cancelDoorManEdit(row) {\r\n      //深拷贝\r\n      if (row._backup) {\r\n        // 恢复备份数据\r\n        Object.assign(row, row._backup);\r\n        delete row._backup; // 删除备份数据\r\n      };\r\n      this.editingRow = null; // 清空当前编辑行\r\n      this.editDoorManStatus = false;\r\n    },\r\n    cancelFactoryEdit() {\r\n      this.taskMaterials = JSON.parse(JSON.stringify(this.backupMaterials));//深拷贝\r\n      console.log(\"this.taskMaterials\", this.taskMaterials);\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    saveDoorManRowIn() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNumIn是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNumIn !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫入厂确认数量(${item.doormanReceiveNumIn})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id;\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n    saveDoorManRow() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNum是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫确认数量(${item.doormanReceiveNum})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n\r\n    saveFactoryRow() {\r\n\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    resetTaskInfoForm() {\r\n      this.taskInfoForm = {};\r\n    },\r\n\r\n    async getTaskInfo() {\r\n      try {\r\n        const response = await getTask(this.dispatchId);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async getTaskInfoByTaskNo() {\r\n      try {\r\n        const response = await getTaskByTaskNo(this.taskNo);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n\r\n        // 从返回的数据中获取所需的参数\r\n        this.dispatchId = this.taskInfoForm.id;\r\n        this.applyNo = this.taskInfoForm.applyNo;\r\n\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfoByTaskNo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    //计划状态\r\n    getPlanStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期',\r\n        '待分厂审批': '待分厂审批',\r\n        '待分厂复审': '待分厂复审',\r\n        '待生产指挥中心审批': '待生产指挥中心审批',\r\n        '审批完成': '审批完成',\r\n        '已出厂': '已出厂',\r\n        '部分收货': '部分收货',\r\n        '已完成': '已完成',\r\n        '驳回': '驳回',\r\n        '废弃': '废弃',\r\n        '过期': '过期',\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n    // 获取排放标准文本\r\n    getEmissionStandardsText(standard) {\r\n      const standardMap = {\r\n        1: '国五',\r\n        2: '国六',\r\n        3: '新能源'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取排放标准标签类型\r\n    getEmissionStandardsTagType(standard) {\r\n      const typeMap = {\r\n        1: 'warning',  // 国五\r\n        2: 'success',  // 国六\r\n        3: 'primary'   // 新能源\r\n      };\r\n      return typeMap[standard] || 'info';\r\n    },\r\n\r\n    // 获取物资状态文本\r\n    getMaterialStatusText(status) {\r\n      const statusMap = {\r\n        1: '待装载',\r\n        2: '已装载',\r\n        3: '已签收',\r\n        4: '异常'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取物资状态标签类型\r\n    getMaterialStatusType(status) {\r\n      const typeMap = {\r\n        1: 'info',     // 待装载\r\n        2: 'warning',  // 已装载\r\n        3: 'success',  // 已签收\r\n        4: 'danger'    // 异常\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#E6A23C', // 更新\r\n        3: '#67C23A', // 完成\r\n        4: '#F56C6C', // 异常\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.type] || '#409EFF';\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    // 获取任务详情数据\r\n    getTaskDetail(dispatchId) {\r\n      // 实际项目中这里需要调用API获取数据\r\n      // getDispatchTaskDetail(dispatchId).then(response => {\r\n      //   const { driverInfo, carInfo, taskMaterials, taskLogs } = response.data;\r\n      //   this.driverInfo = driverInfo;\r\n      //   this.carInfo = carInfo;\r\n      //   this.taskMaterials = taskMaterials;\r\n      //   this.taskLogs = taskLogs;\r\n      // });\r\n    },\r\n    handleShowDropdownChange(val) {\r\n      if (!val) {\r\n        this.factoryConfirmForm.extraOption = '';\r\n      }\r\n    },\r\n    openOptionDialog() {\r\n      this.optionDialogVisible = true;\r\n      this.loadOptions();\r\n      // 重置选中状态\r\n      this.selectedOption = null;\r\n      this.$nextTick(() => {\r\n        if (this.$refs.optionTable) {\r\n          this.$refs.optionTable.clearSelection();\r\n        }\r\n      });\r\n    },\r\n    handleOptionSelection(selection) {\r\n      // 只保留最后选中的一项\r\n      if (selection.length > 1) {\r\n        const lastSelected = selection[selection.length - 1];\r\n        this.$refs.optionTable.clearSelection();\r\n        this.$refs.optionTable.toggleRowSelection(lastSelected, true);\r\n        this.selectedOption = lastSelected;\r\n      } else {\r\n        this.selectedOption = selection[0];\r\n      }\r\n    },\r\n    confirmOptionSelection() {\r\n      if (!this.selectedOption) {\r\n        this.$message.warning('请选择一个选项');\r\n        return;\r\n      }\r\n\r\n      this.factoryConfirmForm.extraOption = this.selectedOption.applyNo;\r\n\r\n      // let dispatchInfo = {};\r\n      // dispatchInfo.carNum = this.taskInfoForm.carNum;\r\n      // dispatchInfo.isDirectSupply = 1;\r\n\r\n      // isAllowDispatch(dispatchInfo).then(response => {\r\n      //   let row = response.data;\r\n      //   if (row > 0) {\r\n      //     this.$message.error(\"当前车有正在执行的任务\")\r\n      //     return;\r\n      //   } else {\r\n      //     this.optionDialogVisible = false;\r\n      //     this.$message.success('选项已确认');\r\n      //   }\r\n      //   console.log(\"this.isAllowDispatch\", response);\r\n      // }).catch(err => {\r\n      //   console.error('dispatch error:', err);\r\n      //   this.$message.error('网络异常，稍后重试');\r\n      // });\r\n\r\n      this.optionDialogVisible = false;\r\n      this.$message.success('选项已确认');\r\n\r\n\r\n\r\n    },\r\n    loadOptions() {\r\n      // 这里应该调用API获取leave_plan表的数据\r\n      this.optionList = this.directSupplyPlanList; // 使用直供计划列表作为选项数据\\\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n      console.log(\"optionList\", this.optionList)\r\n    },\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用（出厂不返回）',\r\n        11: '通用（出厂返回）',\r\n        12: '委外加工（出厂返回）',\r\n        21: '有计划量计量（跨区调拨）',\r\n        22: '短期（跨区调拨）',\r\n        23: '钢板（圆钢）（跨区调拨）',\r\n        31: '通用（退货申请）'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n    searchOptions() {\r\n      // 取出并转小写\r\n      const searchPlanNo = (this.searchForm.planNo || '').toLowerCase();\r\n      const searchApplyNo = (this.searchForm.applyNo || '').toLowerCase();\r\n      const searchReceiveCompany = (this.searchForm.receiveCompany || '').toLowerCase();\r\n\r\n      // 过滤\r\n      this.optionList = this.directSupplyPlanList.filter(item => {\r\n        const planNo = (item.planNo || '').toString().toLowerCase();\r\n        const applyNo = (item.applyNo || '').toString().toLowerCase();\r\n        const receiveCompany = (item.receiveCompany || '').toString().toLowerCase();\r\n\r\n        // 为空不作为条件\r\n        const matchPlanNo = !searchPlanNo || planNo.includes(searchPlanNo);\r\n        const matchApplyNo = !searchApplyNo || applyNo.includes(searchApplyNo);\r\n        const matchReceiveCompany = !searchReceiveCompany || receiveCompany.includes(searchReceiveCompany);\r\n\r\n        return matchPlanNo && matchApplyNo && matchReceiveCompany;\r\n      });\r\n\r\n      // 更新状态显示\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n    },\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      };\r\n      this.loadOptions(); // 重新加载所有数据\r\n    },\r\n    getTaskTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return typeMap[type] || '未知';\r\n    },\r\n    // // 判断行是否可选\r\n    // isSelectable(row) {\r\n    //   // 当门卫确认数量不为0时，该行可选\r\n    //   return row.doormanReceiveNum > 0 && this.taskInfoForm.taskStatus !== 9;\r\n    // },\r\n\r\n    // 表格选择变化时的处理函数\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    // 处理非计量分厂确认\r\n    handleNonMeasureFactoryConfirm() {\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n      let isHandled = false;\r\n      this.selectedRows.forEach(item => {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning('门卫确认数量和计划数量不一致，请检查');\r\n          isHandled = true;\r\n        }\r\n      });\r\n\r\n      if (isHandled) {\r\n        return;\r\n      }\r\n\r\n      // if (this.selectedRows.length === 0) {\r\n      //   this.$message.warning('请选择需要确认的物资');\r\n      //   return;\r\n      // }\r\n\r\n      // 生成派车日志\r\n      let leaveTaskLog = {\r\n        logType: 2,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        info: '分厂接收确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ')\r\n      };\r\n\r\n      // 构建任务信息\r\n      let factoryTaskInfo = {\r\n        id: this.taskInfoForm.id,\r\n        unloadingWorkNo: '卸货人占位符',//后端updateLeaveTask方法\r\n        unloadingTime: new Date(),\r\n        taskStatus: 9\r\n      };\r\n\r\n      this.selectedRows.forEach(item => {\r\n        // 设置非计量分厂确认数量\r\n        item.factoryReceiveNum = item.doormanReceiveNum;\r\n      });\r\n\r\n      // 构建请求参数\r\n      let param = {\r\n        taskMaterialList: this.selectedRows, // 使用选中的行数据\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: factoryTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      // 发送请求\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        if (res.code == 200) {\r\n          this.$message.success('非计量分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n          // 清空选中状态\r\n          this.selectedRows = [];\r\n        } else {\r\n          this.$message.error(res.message || '非计量分厂确认失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleNonMeasureFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    openNewTaskWindow() {\r\n      console.log(\"openNewTaskWindow\", this.directSupplyParams);\r\n      let dispatchId = this.directSupplyParams.dispatchId;\r\n      let applyNo = BigInt(this.directSupplyParams.applyNo);\r\n      let measureFlag = this.directSupplyParams.measureFlag;\r\n      let planType = this.directSupplyParams.planType;\r\n      let taskNo = BigInt(this.directSupplyParams.taskNo);\r\n      const url = `http://localhost/leave/plan/task?dispatchId=${dispatchId}&applyNo=${applyNo}&measureFlag=${measureFlag}&planType=${planType}&taskNo=${taskNo}`;\r\n      window.open(url, '_blank');\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.btn-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode {\r\n  margin: 0 auto;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 通行证二维码模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 司机信息模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 车辆信息模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 物资列表模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.driver-photos {\r\n  padding: 0 20px 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.photo-item {\r\n  width: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.photo-item h4 {\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.photo-container {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.photo-container img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  object-fit: contain;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 25px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy1BA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,WAAA,GAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAO,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,2BAAA;MACAC,kBAAA;QACAC,WAAA;QACAC,MAAA;QACAC,OAAA;QACAC,MAAA;QACAC,QAAA;QACAC,eAAA;QACAC,aAAA;QACAC,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QAAA;QACAC,MAAA;QAAA;QACA;QACAC,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAC,mBAAA;QACAC,aAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,YAAA;MACA;MACAC,mBAAA;MACAC,UAAA;QACAjC,MAAA;QACAD,OAAA;QACA6B,cAAA;MACA;MACAM,UAAA;MACAC,iBAAA;MACAC,iBAAA;MACA;MACAC,UAAA;QACAC,EAAA;QACA7C,IAAA;QACA8C,MAAA;QACAC,KAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;QACAC,iBAAA;QACAC,kBAAA;MACA;MAEA;MACAC,OAAA;MAEA;MACAC,aAAA;MAEA;MACAC,QAAA;MAEA;MACAjD,OAAA;MAEAkD,SAAA;MAEA;MACAC,UAAA;MAEAC,YAAA;MAEAC,WAAA;MAEAC,mBAAA;MACAvD,MAAA;MAEAwD,cAAA;MAEAC,QAAA;MAEAC,kBAAA;MAAA;;MAEAC,0BAAA;MAAA;;MAEAC,sBAAA;MAAA;;MAEAC,oBAAA;MAAA;;MAEAC,UAAA;MAEAC,YAAA;MAAA;;MAEAC,kBAAA;IACA;EACA;EAEAC,QAAA;IACAC,yBAAA,WAAAA,0BAAA;MACA,YAAAN,sBAAA,QAAAD,0BAAA,QAAAD,kBAAA;IACA;IAEA;IACAS,gBAAA,WAAAA,iBAAA;MACA,YAAAJ,YAAA,CAAAK,MAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MACA,YAAApB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;IACA;IAEAC,aAAA,WAAAA,cAAA;MACA,YAAAzB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAI,YAAA;MAAA,GAAAF,IAAA;IACA;EACA;EAEAG,SAAA,WAAAA,UAAA;IACAC,OAAA,CAAAC,GAAA;IACA,KAAAC,iBAAA;;IAEA;IACA,IAAA/E,MAAA,QAAAgF,MAAA,CAAAC,MAAA,CAAAjF,MAAA,SAAAgF,MAAA,CAAAE,KAAA,CAAAlF,MAAA;IAEA,IAAAA,MAAA;MACA;MACA,KAAAA,MAAA,GAAAA,MAAA;MACA6E,OAAA,CAAAC,GAAA,gBAAA9E,MAAA;MACA,KAAAmF,YAAA;;MAEA;MACA,KAAAC,sBAAA;IACA;MACA;MACA,IAAAC,kBAAA,QAAAL,MAAA,CAAAE,KAAA;QAAA9B,UAAA,GAAAiC,kBAAA,CAAAjC,UAAA;QAAAnD,OAAA,GAAAoF,kBAAA,CAAApF,OAAA;QAAAqD,WAAA,GAAA+B,kBAAA,CAAA/B,WAAA;QAAAgC,QAAA,GAAAD,kBAAA,CAAAC,QAAA;QAAAC,WAAA,GAAAF,kBAAA,CAAArF,MAAA;MACA,KAAAoD,UAAA,GAAAA,UAAA;MACA,KAAAnD,OAAA,GAAAA,OAAA;MACA,KAAAqD,WAAA,GAAAA,WAAA;MACAuB,OAAA,CAAAC,GAAA,0BAAAxB,WAAA;MACA,KAAAgC,QAAA,GAAAA,QAAA;MACA,KAAAtF,MAAA,GAAAuF,WAAA;MACAV,OAAA,CAAAC,GAAA,gBAAA9E,MAAA;MACA,KAAAmF,YAAA;;MAEA;MACA,KAAAK,cAAA;IACA;EACA;EAEAC,OAAA;IACA;AACA;AACA;AACA;AACA;IACAC,uBAAA,WAAAA,wBAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGA,IAAAE,6BAAA;YAAA;cAAAN,QAAA,GAAAG,QAAA,CAAAI,CAAA;cAEA;cACAb,KAAA,CAAAM,QAAA,GAAAA,QAAA;cACApB,OAAA,CAAAC,GAAA,gBAAAmB,QAAA;;cAEA;cAAAG,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAI,kDAAA,EAAAd,KAAA;YAAA;cAAA,OAAAS,QAAA,CAAAM,CAAA,IAAAN,QAAA,CAAAI,CAAA;YAAA;cAAAJ,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEA3B,OAAA,CAAA8B,KAAA,YAAAT,EAAA;cAAA,MAAAA,EAAA;YAAA;cAAA,OAAAE,QAAA,CAAAM,CAAA;UAAA;QAAA,GAAAV,OAAA;MAAA;IAGA;IAEAY,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,KAAA;MAGA,IAAAC,UAAA;QACA9G,MAAA,OAAAqD,YAAA,CAAA0D;MACA;MAEA,IAAAC,sCAAA,EAAAF,UAAA,EAAAG,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,qCAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAN,KAAA,CAAA7C,kBAAA,CAAAZ,UAAA,GAAA8D,GAAA,CAAAE,IAAA,IAAA5E,EAAA;UACAqE,KAAA,CAAA7C,kBAAA,CAAA/D,OAAA,GAAAiH,GAAA,CAAAE,IAAA,IAAAnH,OAAA;UACA4G,KAAA,CAAA7C,kBAAA,CAAAhE,MAAA,GAAAkH,GAAA,CAAAE,IAAA,IAAApH,MAAA;UACA6G,KAAA,CAAA7C,kBAAA,CAAAV,WAAA,GAAA4D,GAAA,CAAAE,IAAA,IAAA9D,WAAA;UACAuD,KAAA,CAAA7C,kBAAA,CAAAsB,QAAA,GAAA4B,GAAA,CAAAE,IAAA,IAAA9B,QAAA;QACA;UACAuB,KAAA,CAAAQ,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,4CAAAa,GAAA;QACAX,KAAA,CAAAQ,QAAA,CAAAV,KAAA;QACA,MAAAa,GAAA;MACA;IAEA;IAEArC,YAAA,WAAAA,aAAA;MAAA,IAAAsC,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,CAAAC,KAAA,CAAAC,OAAA,WAAAtD,IAAA;QACA,IAAAA,IAAA;UACAkD,MAAA,CAAAtE,SAAA;QACA;MACA;MACA0B,OAAA,CAAAC,GAAA,mBAAA3B,SAAA;IACA;IACAqC,cAAA,WAAAA,eAAA;MAAA,IAAAsC,MAAA;MAAA,WAAAlC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgC,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAAlC,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA8B,SAAA;UAAA,kBAAAA,SAAA,CAAA5B,CAAA;YAAA;cAAA4B,SAAA,CAAA3B,CAAA;cAAA2B,SAAA,CAAA5B,CAAA;cAAA,OAGAyB,MAAA,CAAAI,WAAA;YAAA;cAAAD,SAAA,CAAA5B,CAAA;cAAA,OACAyB,MAAA,CAAAK,mBAAA,CAAAL,MAAA,CAAA9H,MAAA;YAAA;cAAAiI,SAAA,CAAA5B,CAAA;cAAA,OACAyB,MAAA,CAAAM,WAAA,CAAAN,MAAA,CAAA7H,OAAA;YAAA;cAEA;cACA6H,MAAA,CAAAO,wBAAA;;cAEA;cACAP,MAAA,CAAAQ,cAAA,CAAAR,MAAA,CAAA9H,MAAA;cACA8H,MAAA,CAAAS,cAAA;;cAEA;cACAT,MAAA,CAAAlB,0BAAA;cAAAqB,SAAA,CAAA5B,CAAA;cAAA;YAAA;cAAA4B,SAAA,CAAA3B,CAAA;cAAA0B,GAAA,GAAAC,SAAA,CAAAzB,CAAA;cAEA3B,OAAA,CAAA8B,KAAA,6BAAAqB,GAAA;cACAF,MAAA,CAAAT,QAAA,CAAAV,KAAA;YAAA;cAAA,OAAAsB,SAAA,CAAAvB,CAAA;UAAA;QAAA,GAAAqB,QAAA;MAAA;IAEA;IAEA3C,sBAAA,WAAAA,uBAAA;MAAA,IAAAoD,MAAA;MAAA,WAAA5C,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA0C,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAA5C,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAwC,SAAA;UAAA,kBAAAA,SAAA,CAAAtC,CAAA;YAAA;cAAAsC,SAAA,CAAArC,CAAA;cAAAqC,SAAA,CAAAtC,CAAA;cAAA,OAGAmC,MAAA,CAAAI,mBAAA;YAAA;cAAAD,SAAA,CAAAtC,CAAA;cAAA,OAGAmC,MAAA,CAAAJ,WAAA,CAAAI,MAAA,CAAAvI,OAAA;YAAA;cAAA0I,SAAA,CAAAtC,CAAA;cAAA,OAGAmC,MAAA,CAAAL,mBAAA,CAAAK,MAAA,CAAAxI,MAAA;YAAA;cAEA;cACAwI,MAAA,CAAAH,wBAAA;;cAEA;cACAG,MAAA,CAAAF,cAAA,CAAAE,MAAA,CAAAxI,MAAA;cACAwI,MAAA,CAAAD,cAAA;;cAEA;cACAC,MAAA,CAAA5B,0BAAA;cAAA+B,SAAA,CAAAtC,CAAA;cAAA;YAAA;cAAAsC,SAAA,CAAArC,CAAA;cAAAoC,GAAA,GAAAC,SAAA,CAAAnC,CAAA;cAEA3B,OAAA,CAAA8B,KAAA,uCAAA+B,GAAA;cACAF,MAAA,CAAAnB,QAAA,CAAAV,KAAA;YAAA;cAAA,OAAAgC,SAAA,CAAAjC,CAAA;UAAA;QAAA,GAAA+B,QAAA;MAAA;IAEA;IAEAJ,wBAAA,WAAAA,yBAAA;MAAA,IAAAQ,MAAA;MACA;MACA,KAAA5F,aAAA,CAAA4E,OAAA,WAAAtD,IAAA;QACAA,IAAA,CAAAuE,iBAAA,GAAAvE,IAAA,CAAAwE,OAAA;QACAlE,OAAA,CAAAC,GAAA,kBAAA+D,MAAA,CAAApF,QAAA,CAAA6B,QAAA;QACA,IAAAuD,MAAA,CAAApF,QAAA,CAAA6B,QAAA,SAAAuD,MAAA,CAAApF,QAAA,CAAA6B,QAAA;UACAf,IAAA,CAAAyE,mBAAA,GAAAzE,IAAA,CAAAwE,OAAA;QACA;MACA;MAEA,IAAAnH,mBAAA,QAAAqB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MACA,IAAAC,aAAA,QAAAzB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAI,YAAA;MAAA,GAAAF,IAAA;MACA;MACA,KAAA3E,kBAAA;QACAC,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACAkJ,KAAA,OAAA5F,YAAA,CAAA4F,KAAA;QACAC,QAAA,OAAA7F,YAAA,CAAA6F,QAAA;QACAC,UAAA,OAAA9F,YAAA,CAAA8F,UAAA;QACAC,IAAA,OAAA/F,YAAA,CAAA+F,IAAA;QACApJ,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAE,eAAA;QACAC,aAAA,MAAAgJ,IAAA;QACA/I,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QAAA;QACAW,mBAAA,EAAAA,mBAAA;QACA8C,aAAA,EAAAA,aAAA;QACA7C,aAAA,OAAA4B,QAAA,CAAA5B,aAAA;QACAC,cAAA,OAAA2B,QAAA,CAAA3B,cAAA;QACAC,YAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAd,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAM,YAAA;MACA;IACA;IAEAqH,aAAA,WAAAA,cAAA;MACA,IAAAC,YAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,YAAA;IACA;IACA;IACAG,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MAAA,WAAA/D,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA6D,SAAA;QAAA,IAAAC,SAAA,EAAA3C,GAAA,EAAA4C,GAAA;QAAA,WAAAhE,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA4D,SAAA;UAAA,kBAAAA,SAAA,CAAA1D,CAAA;YAAA;cAAA0D,SAAA,CAAAzD,CAAA;cAEAuD,SAAA;gBACAhI,aAAA,EAAA8H,MAAA,CAAAlG,QAAA,CAAA5B,aAAA;gBACAyD,QAAA;cACA;cACAT,OAAA,CAAAC,GAAA,cAAA+E,SAAA;cAAAE,SAAA,CAAA1D,CAAA;cAAA,OAEA,IAAA2D,0BAAA,EAAAH,SAAA;YAAA;cAAA3C,GAAA,GAAA6C,SAAA,CAAAvD,CAAA;cACA3B,OAAA,CAAAC,GAAA,yBAAAoC,GAAA;cACA,IAAAA,GAAA,CAAAC,IAAA;gBACAwC,MAAA,CAAA9F,oBAAA,GAAAqD,GAAA,CAAAE,IAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACAuC,MAAA,CAAAtC,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;cACA;cAAAyC,SAAA,CAAA1D,CAAA;cAAA;YAAA;cAAA0D,SAAA,CAAAzD,CAAA;cAAAwD,GAAA,GAAAC,SAAA,CAAAvD,CAAA;cAEA3B,OAAA,CAAA8B,KAAA,gCAAAmD,GAAA;cACAH,MAAA,CAAAtC,QAAA,CAAAV,KAAA;cAAA,MAAAmD,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAArD,CAAA;UAAA;QAAA,GAAAkD,QAAA;MAAA;IAGA;IACAK,iBAAA,WAAAA,kBAAA/E,KAAA;MACA,KAAAtB,sBAAA,GAAAsB,KAAA;MAEA,SAAAtB,sBAAA;QACAiB,OAAA,CAAAC,GAAA,4BAAApB,kBAAA;QAEA,KAAAC,0BAAA,QAAAD,kBAAA,CAAAwG,MAAA,WAAA3F,IAAA;UAAA,OACAA,IAAA,CAAA4F,KAAA,CAAAC,QAAA,CAAAlF,KAAA;QAAA,CACA;MACA;QAEA,KAAAvB,0BAAA,QAAAD,kBAAA;MACA;IACA;IACA6E,cAAA,WAAAA,eAAA;MAAA,IAAA8B,MAAA;MACA,IAAAC,oBAAA,IAAArD,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,mBAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAkD,MAAA,CAAA3G,kBAAA,GAAAwD,GAAA,CAAAE,IAAA,CAAA9C,GAAA,WAAAC,IAAA;YAAA;cACA4F,KAAA,EAAA5F,IAAA,CAAAgG,WAAA;cACAC,KAAA,EAAAjG,IAAA,CAAAgG;YACA;UAAA;UACAF,MAAA,CAAA1G,0BAAA,GAAA0G,MAAA,CAAA3G,kBAAA;QACA;UACA2G,MAAA,CAAAhD,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,0BAAAa,GAAA;QACA6C,MAAA,CAAAhD,QAAA,CAAAV,KAAA;MACA;IACA;IACAyB,WAAA,WAAAA,YAAAnI,OAAA;MAAA,IAAAwK,MAAA;MAAA,WAAA7E,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA2E,SAAA;QAAA,IAAAC,QAAA,EAAAC,GAAA;QAAA,WAAA9E,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA0E,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,CAAA;YAAA;cAAAwE,SAAA,CAAAvE,CAAA;cAAAuE,SAAA,CAAAxE,CAAA;cAAA,OAEA,IAAAyE,gBAAA,EAAA7K,OAAA;YAAA;cAAA0K,QAAA,GAAAE,SAAA,CAAArE,CAAA;cACA3B,OAAA,CAAAC,GAAA,eAAA6F,QAAA;cACAF,MAAA,CAAAhH,QAAA,GAAAkH,QAAA,CAAA/K,IAAA;;cAEA;cACA6K,MAAA,CAAAnF,QAAA,GAAAmF,MAAA,CAAAhH,QAAA,CAAA6B,QAAA;cACAmF,MAAA,CAAAnH,WAAA,GAAAmH,MAAA,CAAAhH,QAAA,CAAAH,WAAA;cACAuB,OAAA,CAAAC,GAAA,kBAAA2F,MAAA,CAAAnF,QAAA;cACAT,OAAA,CAAAC,GAAA,qBAAA2F,MAAA,CAAAnH,WAAA;cAAAuH,SAAA,CAAAxE,CAAA;cAAA,OAEAoE,MAAA,CAAAf,mBAAA;YAAA;cAAA,OAAAmB,SAAA,CAAAnE,CAAA,IACAiE,QAAA;YAAA;cAAAE,SAAA,CAAAvE,CAAA;cAAAsE,GAAA,GAAAC,SAAA,CAAArE,CAAA;cAEA3B,OAAA,CAAA8B,KAAA,uBAAAiE,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAnE,CAAA;UAAA;QAAA,GAAAgE,QAAA;MAAA;IAGA;IACAK,wBAAA,WAAAA,yBAAA;MACA,IAAAnJ,mBAAA,QAAAqB,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MACA;MACA,KAAA3E,kBAAA;QACAC,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACAkJ,KAAA,OAAA5F,YAAA,CAAA4F,KAAA;QACAC,QAAA,OAAA7F,YAAA,CAAA6F,QAAA;QACAE,IAAA,OAAA/F,YAAA,CAAA+F,IAAA;QACApJ,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAE,eAAA;QACAC,aAAA,MAAAgJ,IAAA;QACA/I,WAAA;QACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,SAAA;QACAC,WAAA;QACAC,MAAA;QACAC,UAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;QACAC,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QAAA;QACAW,mBAAA,EAAAA,mBAAA;QACAC,aAAA,OAAA4B,QAAA,CAAA5B,aAAA;QACAC,cAAA,OAAA2B,QAAA,CAAA3B,cAAA;QACAC,YAAA;QAAA;QACAC,WAAA;QAAA;QACA;QACAd,mBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,aAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,cAAA;QACAM,YAAA;MACA;MACA,KAAApC,2BAAA;IACA;IACAmL,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAnL,kBAAA,CAAAiC,YAAA;QACA,SAAAjC,kBAAA,CAAAkC,WAAA,iBAAAlC,kBAAA,CAAAkC,WAAA;UACA,KAAAqF,QAAA,CAAAV,KAAA;UACA;QACA;MACA;MAEA,IAAAuE,UAAA;MACA,SAAA7H,YAAA,CAAA8H,cAAA;QACA;QACAD,UAAA;UACAE,SAAA;YACA5I,EAAA,OAAAY,UAAA;YACApD,MAAA,OAAAA,MAAA;YACAC,OAAA,OAAAA,OAAA;YACA;YACAK,WAAA,OAAAR,kBAAA,CAAAQ,WAAA;YACAC,UAAA,OAAAT,kBAAA,CAAAS,UAAA;YACAC,MAAA,OAAAV,kBAAA,CAAAW,KAAA,QAAAX,kBAAA,CAAAY,SAAA;YACAC,WAAA,OAAAb,kBAAA,CAAAa,WAAA;YACAC,MAAA,OAAAd,kBAAA,CAAAc,MAAA;YACAC,UAAA,OAAAf,kBAAA,CAAAe,UAAA;YACAC,KAAA,OAAAhB,kBAAA,CAAAgB,KAAA;YACAC,MAAA,OAAAjB,kBAAA,CAAAiB,MAAA;YACAE,MAAA,OAAAoC,YAAA,CAAApC,MAAA;YACAkI,UAAA,OAAA9F,YAAA,CAAA8F,UAAA;YACAgC,cAAA;YACAjL,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;YACA+B,YAAA,OAAAnC,kBAAA,CAAAmC,YAAA;YAAA;;YAEA;YACAf,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;YACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;YACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;YACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;YACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;YACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;YACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;YACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;YACA;YACA;YACAX,UAAA;YACAb,QAAA,OAAAkD,YAAA,CAAAlD;UACA;UACA0J,SAAA,OAAApG,QAAA;UACA4H,iBAAA,OAAApI,aAAA;QACA;MACA;QACA;QACAiI,UAAA;UACAE,SAAA;YACA5I,EAAA,OAAAY,UAAA;YACApD,MAAA,OAAAA,MAAA;YACAC,OAAA,OAAAA,OAAA;YACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;YACA;YACAI,WAAA,OAAAR,kBAAA,CAAAQ,WAAA;YACAC,UAAA,OAAAT,kBAAA,CAAAS,UAAA;YACAC,MAAA,OAAAV,kBAAA,CAAAW,KAAA,QAAAX,kBAAA,CAAAY,SAAA;YACAC,WAAA,OAAAb,kBAAA,CAAAa,WAAA;YACAC,MAAA,OAAAd,kBAAA,CAAAc,MAAA;YACAC,UAAA,OAAAf,kBAAA,CAAAe,UAAA;YACAC,KAAA,OAAAhB,kBAAA,CAAAgB,KAAA;YACAC,MAAA,OAAAjB,kBAAA,CAAAiB,MAAA;YACAE,MAAA,OAAAoC,YAAA,CAAApC,MAAA;YACAkI,UAAA,OAAA9F,YAAA,CAAA8F,UAAA;YACAgC,cAAA;YAAA;YACAlJ,YAAA,OAAAnC,kBAAA,CAAAmC,YAAA;YAAA;YACA8E,kBAAA,OAAAjH,kBAAA,CAAAkC,WAAA;YACA;YACAd,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;YACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;YACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;YACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;YACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;YACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;YACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;YACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;YACA;YACA;YACAX,UAAA;YACAb,QAAA,OAAAkD,YAAA,CAAAlD;UACA;UACA0J,SAAA,OAAApG,QAAA;UACA4H,iBAAA,OAAApI,aAAA;QACA;MACA;MAIA,IAAAqI,gBAAA;QACA;QACArL,OAAA,OAAAH,kBAAA,CAAAkC,WAAA;QACA7B,QAAA;QACAa,UAAA;QACAkI,QAAA,OAAA7F,YAAA,CAAA6F,QAAA;QACAqC,YAAA,OAAAlI,YAAA,CAAAkI,YAAA;QACArL,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;QACAiJ,UAAA,OAAA9F,YAAA,CAAA8F,UAAA;QACAqC,GAAA,OAAAnI,YAAA,CAAAmI,GAAA;QACAC,WAAA,OAAApI,YAAA,CAAAoI,WAAA;QACAC,QAAA,OAAArI,YAAA,CAAAqI,QAAA;QACAzK,MAAA,OAAAoC,YAAA,CAAApC,MAAA;QACA0K,wBAAA,OAAAtI,YAAA,CAAAsI,wBAAA;QACAC,OAAA,OAAAvI,YAAA,CAAAuI,OAAA;QACAC,iBAAA,OAAAxI,YAAA,CAAAwI,iBAAA;QACAC,gBAAA,OAAAzI,YAAA,CAAAyI,gBAAA;QACA/L,WAAA,OAAAsD,YAAA,CAAAtD,WAAA;QACAoL,cAAA;MACA;MAEA,IAAAY,4BAAA,QAAA9I,aAAA;MAEA,SAAAnD,kBAAA,CAAAiC,YAAA,iBAAAjC,kBAAA,CAAAkC,WAAA,iBAAAlC,kBAAA,CAAAkC,WAAA;QACAkJ,UAAA,CAAAE,SAAA,CAAAD,cAAA;QACAD,UAAA,CAAAI,gBAAA,GAAAA,gBAAA;QACAJ,UAAA,CAAAa,4BAAA,GAAAA,4BAAA;MACA;MAEA,IAAAC,kBAAA,EAAAd,UAAA,EAAAjE,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,iBAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA8D,MAAA,CAAA5D,QAAA,CAAA4E,OAAA;UACAhB,MAAA,CAAApL,2BAAA;UACAoL,MAAA,CAAA3C,cAAA,CAAA2C,MAAA,CAAAjL,MAAA;UACAiL,MAAA,CAAA/C,WAAA;QACA;UACA;UACA+C,MAAA,CAAA5D,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,8BAAAa,GAAA;QACAyD,MAAA,CAAA5D,QAAA,CAAAV,KAAA;MACA;IACA;IAEAuF,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAEA;MACA,IAAAvE,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAwC,QAAA;QACA,KAAA/C,QAAA,CAAAV,KAAA;QACA;MACA;MACA;MACA,IAAAuE,UAAA;QACAE,SAAA;UACA;UACA5I,EAAA,OAAAY,UAAA;UACApD,MAAA,OAAAA,MAAA;UACAC,OAAA,OAAAA,OAAA;UACAC,MAAA,OAAAmD,YAAA,CAAAnD,MAAA;UACA;UACAgB,mBAAA,OAAApB,kBAAA,CAAAoB,mBAAA;UACAC,kBAAA,OAAArB,kBAAA,CAAAqB,kBAAA;UACAC,cAAA,OAAAtB,kBAAA,CAAAwB,aAAA,QAAAxB,kBAAA,CAAAuB,iBAAA;UACAE,mBAAA,OAAAzB,kBAAA,CAAAyB,mBAAA;UACAC,cAAA,OAAA1B,kBAAA,CAAA0B,cAAA;UACAC,kBAAA,OAAA3B,kBAAA,CAAA2B,kBAAA;UACAC,aAAA,OAAA5B,kBAAA,CAAA4B,aAAA;UACAC,cAAA,OAAA7B,kBAAA,CAAA6B,cAAA;UAEA;UACAX,UAAA;UACAC,MAAA,OAAAoC,YAAA,CAAApC;QACA;QACA4I,SAAA,OAAApG,QAAA;QACA4H,iBAAA,OAAApI,aAAA;MACA;MAEA,IAAAmJ,oBAAA,EAAAlB,UAAA,EAAAjE,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,mBAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAgF,MAAA,CAAA9E,QAAA,CAAA4E,OAAA;UACAE,MAAA,CAAAtM,2BAAA;UACAsM,MAAA,CAAA7D,cAAA,CAAA6D,MAAA,CAAAnM,MAAA;UACAmM,MAAA,CAAAjE,WAAA;QACA;UACA;UACAiE,MAAA,CAAA9E,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,8BAAAa,GAAA;QACA2E,MAAA,CAAA9E,QAAA,CAAAV,KAAA;MACA;IACA;IAEA0F,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAhK,iBAAA;QACA,KAAA+E,QAAA,CAAAkF,OAAA;QACA;MACA;;MAGA;MACA;MACA,IAAAC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAxM,MAAA,QAAAA,MAAA;MACAwM,YAAA,CAAAvM,OAAA,QAAAA,OAAA;MACAuM,YAAA,CAAAE,IAAA;MAGA,IAAAC,eAAA;MACA;MACAA,eAAA,CAAAnK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACAmK,eAAA,CAAAvM,eAAA;MACAuM,eAAA,CAAAtM,aAAA,OAAAgJ,IAAA;MACAsD,eAAA,CAAA3L,UAAA;MAEA,IAAA2E,KAAA;MACAA,KAAA,CAAAiH,gBAAA,QAAA3J,aAAA;MACA0C,KAAA,CAAAkH,QAAA,GAAAL,YAAA;MACA7G,KAAA,CAAAyF,SAAA,GAAAuB,eAAA;MACAhH,KAAA,CAAArC,WAAA,QAAAA,WAAA;MAEA,KAAAoC,uBAAA,CAAAC,KAAA,EAAAsB,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,iDAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAmF,MAAA,CAAAjF,QAAA,CAAA4E,OAAA;UACAK,MAAA,CAAAhE,cAAA,CAAAgE,MAAA,CAAAtM,MAAA;UACAsM,MAAA,CAAApE,WAAA;QACA;UACA;UACAoE,MAAA,CAAAjF,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,gCAAAa,GAAA;QACA8E,MAAA,CAAAjF,QAAA,CAAAV,KAAA;MACA;IACA;IAGAmG,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,SAAA1K,iBAAA;QACA,KAAAgF,QAAA,CAAAkF,OAAA;QACA;MACA;MAEA,IAAAC,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAxM,MAAA,QAAAA,MAAA;MACAwM,YAAA,CAAAvM,OAAA,QAAAA,OAAA;MACAuM,YAAA,CAAAE,IAAA;MAIA,IAAAM,eAAA;MACAA,eAAA,CAAAxK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACA6M,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAC,SAAA,OAAA5D,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACA0J,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACA0J,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAC,SAAA,OAAA5D,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAzH,KAAA;MACAA,KAAA,CAAAiH,gBAAA,QAAA3J,aAAA;MACA0C,KAAA,CAAAkH,QAAA,GAAAL,YAAA;MACA7G,KAAA,CAAAyF,SAAA,GAAA4B,eAAA;MACArH,KAAA,CAAArC,WAAA,QAAAA,WAAA;MAEA,KAAAoC,uBAAA,CAAAC,KAAA,EAAAsB,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,iDAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA4F,OAAA,CAAA1F,QAAA,CAAA4E,OAAA;UACAc,OAAA,CAAAzE,cAAA,CAAAyE,OAAA,CAAA/M,MAAA;UACA+M,OAAA,CAAA7E,WAAA;QACA;UACA;UACA6E,OAAA,CAAA1F,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,gCAAAa,GAAA;QACAuF,OAAA,CAAA1F,QAAA,CAAAV,KAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA6F,YAAA,CAAAxM,MAAA,QAAAA,MAAA;MACAwM,YAAA,CAAAvM,OAAA,QAAAA,OAAA;MACAuM,YAAA,CAAAE,IAAA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;IAEA;IAEAY,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAA3F,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAwC,QAAA;QACA,KAAA/C,QAAA,CAAAV,KAAA;QACA;MACA;MAEA,IAAA6F,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAxM,MAAA,QAAAA,MAAA;MACAwM,YAAA,CAAAvM,OAAA,QAAAA,OAAA;MACA,SAAAoD,YAAA,CAAArC,UAAA;QACAwL,YAAA,CAAAE,IAAA,yBAAAzJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;QACA+H,YAAA,CAAAE,IAAA,yBAAAzJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;MAEA,IAAAuI,eAAA;MACAA,eAAA,CAAAxK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACAwK,eAAA,CAAA7M,QAAA,QAAAkD,YAAA,CAAAlD,QAAA;MACA6M,eAAA,CAAA/L,MAAA,QAAAoC,YAAA,CAAApC,MAAA;MACA,SAAAoC,YAAA,CAAAlD,QAAA;QACA6M,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAC,SAAA,OAAA5D,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACA0J,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACA0J,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAC,SAAA,OAAA5D,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAzH,KAAA;MACAA,KAAA,CAAAiH,gBAAA,QAAA3J,aAAA;MACA0C,KAAA,CAAAkH,QAAA,GAAAL,YAAA;MACA7G,KAAA,CAAAyF,SAAA,GAAA4B,eAAA;MACArH,KAAA,CAAArC,WAAA,QAAAA,WAAA;MAEA,KAAAoC,uBAAA,CAAAC,KAAA,EAAAsB,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,iDAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAoG,OAAA,CAAAlG,QAAA,CAAA4E,OAAA;UACAsB,OAAA,CAAAjF,cAAA,CAAAiF,OAAA,CAAAvN,MAAA;UACAuN,OAAA,CAAArF,WAAA;QACA;UACA;UACAqF,OAAA,CAAAlG,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,gCAAAa,GAAA;QACA+F,OAAA,CAAAlG,QAAA,CAAAV,KAAA;MACA;MACA;IAEA;IACA;IACA6G,WAAA,WAAAA,YAAA;MACA,SAAAnK,YAAA,CAAAoK,aAAA;QACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,SAAA;QACA,IAAAC,QAAA,OAAAC,iBAAA,MAAAJ,KAAA,CAAAC,MAAA;UACAI,IAAA,OAAA1K,YAAA,CAAAoK,aAAA;UAAA;UACAO,KAAA;UACAC,MAAA;UACAC,SAAA;UACAC,UAAA;UACAC,YAAA,EAAAN,iBAAA,CAAAO,YAAA,CAAAC;QACA;MACA;IACA;IACAhG,cAAA,WAAAA,eAAAtI,MAAA;MAAA,IAAAuO,OAAA;MACA,IAAAC,OAAA;MACAA,OAAA,CAAAxO,MAAA,GAAAA,MAAA;MACA,IAAAyO,iBAAA,EAAAD,OAAA,EAAAvH,IAAA,WAAA0D,QAAA;QACA9F,OAAA,CAAAC,GAAA,gBAAA6F,QAAA;QACA;QACA,IAAA+D,IAAA,GAAA/D,QAAA,CAAAvD,IAAA;QACA;QACA,IAAAuH,YAAA,GAAAD,IAAA,CAAAxE,MAAA,WAAApF,GAAA;UAAA,OAAAA,GAAA,CAAA4H,IAAA,IAAA5H,GAAA,CAAA4H,IAAA,CAAAtC,QAAA;QAAA;QACA,IAAAwE,SAAA,GAAAF,IAAA,CAAAxE,MAAA,WAAApF,GAAA;UAAA,SAAAA,GAAA,CAAA4H,IAAA,IAAA5H,GAAA,CAAA4H,IAAA,CAAAtC,QAAA;QAAA;QACA;QACAmE,OAAA,CAAArL,QAAA,MAAA2L,MAAA,KAAAC,mBAAA,CAAAjJ,OAAA,EAAA8I,YAAA,OAAAG,mBAAA,CAAAjJ,OAAA,EAAA+I,SAAA;MACA;IAEA;IACAzG,mBAAA,WAAAA,oBAAAnI,MAAA;MAAA,IAAA+O,OAAA;MAAA,WAAAnJ,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAiJ,SAAA;QAAA,IAAAC,aAAA,EAAAtE,QAAA,EAAAuE,GAAA;QAAA,WAAApJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAgJ,SAAA;UAAA,kBAAAA,SAAA,CAAA9I,CAAA;YAAA;cAAA8I,SAAA,CAAA7I,CAAA;cAEAzB,OAAA,CAAAC,GAAA;cACAmK,aAAA;cACAA,aAAA,CAAAjP,MAAA,GAAAA,MAAA;cAAAmP,SAAA,CAAA9I,CAAA;cAAA,OACA,IAAA+I,sBAAA,EAAAH,aAAA;YAAA;cAAAtE,QAAA,GAAAwE,SAAA,CAAA3I,CAAA;cACAuI,OAAA,CAAA9L,aAAA,GAAA0H,QAAA,CAAAvD,IAAA;cACA;cACA2H,OAAA,CAAA9L,aAAA,CAAA4E,OAAA,WAAAtD,IAAA;gBACAA,IAAA,CAAAuE,iBAAA,GAAAvE,IAAA,CAAAwE,OAAA;gBACAlE,OAAA,CAAAC,GAAA,kBAAAiK,OAAA,CAAAtL,QAAA,CAAA6B,QAAA;gBACA,IAAAyJ,OAAA,CAAAtL,QAAA,CAAA6B,QAAA,SAAAyJ,OAAA,CAAAtL,QAAA,CAAA6B,QAAA;kBACAf,IAAA,CAAAyE,mBAAA,GAAAzE,IAAA,CAAAwE,OAAA;gBACA;cACA;cACAlE,OAAA,CAAAC,GAAA,kBAAAiK,OAAA,CAAA9L,aAAA;cAAA,OAAAkM,SAAA,CAAAzI,CAAA,IACAiE,QAAA;YAAA;cAAAwE,SAAA,CAAA7I,CAAA;cAAA4I,GAAA,GAAAC,SAAA,CAAA3I,CAAA;cAEA3B,OAAA,CAAA8B,KAAA,+BAAAuI,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAzI,CAAA;UAAA;QAAA,GAAAsI,QAAA;MAAA;IAGA;IACAK,cAAA,WAAAA,eAAAC,GAAA;MACAA,GAAA,CAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAJ,GAAA;MACA,KAAAxL,UAAA,GAAAwL,GAAA;MACA,KAAAjN,iBAAA;MACAwC,OAAA,CAAAC,GAAA,wBAAAwK,GAAA;IACA;IACAK,cAAA,WAAAA,eAAA;MACA,KAAAC,eAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAzM,aAAA;MACA,KAAAX,iBAAA;IACA;IACAuN,iBAAA,WAAAA,kBAAAP,GAAA;MACA;MACA,IAAAA,GAAA,CAAAC,OAAA;QACA;QACAO,MAAA,CAAAC,MAAA,CAAAT,GAAA,EAAAA,GAAA,CAAAC,OAAA;QACA,OAAAD,GAAA,CAAAC,OAAA;MACA;MAAA;MACA,KAAAzL,UAAA;MACA,KAAAzB,iBAAA;IACA;IACA2N,iBAAA,WAAAA,kBAAA;MACA,KAAA/M,aAAA,GAAAuM,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAE,eAAA;MACA/K,OAAA,CAAAC,GAAA,4BAAA7B,aAAA;MACA,KAAAX,iBAAA;IACA;IAEA2N,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAtI,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAwC,QAAA;QACA,KAAA/C,QAAA,CAAAV,KAAA;QACA;MACA;MAEA,SAAA1D,aAAA,CAAAmB,MAAA;QACAS,OAAA,CAAAC,GAAA,uBAAA7B,aAAA;QACA,KAAAoE,QAAA,CAAAkF,OAAA;QACA;MACA;;MAEA;MAAA,IAAA4D,SAAA,OAAAC,2BAAA,CAAAvK,OAAA,EACA,KAAA5C,aAAA;QAAAoN,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAA9J,CAAA,IAAAkK,IAAA;UAAA,IAAAhM,IAAA,GAAA8L,KAAA,CAAAlG,KAAA;UACA,IAAA5F,IAAA,CAAAyE,mBAAA,KAAAzE,IAAA,CAAAwE,OAAA;YACA,KAAA1B,QAAA,CAAAkF,OAAA,kBAAAsC,MAAA,CAAAtK,IAAA,CAAAC,YAAA,+DAAAqK,MAAA,CAAAtK,IAAA,CAAAyE,mBAAA,sCAAA6F,MAAA,CAAAtK,IAAA,CAAAwE,OAAA;YACA;UACA;QACA;MAAA,SAAAvB,GAAA;QAAA2I,SAAA,CAAAK,CAAA,CAAAhJ,GAAA;MAAA;QAAA2I,SAAA,CAAAM,CAAA;MAAA;MAEA,IAAAjE,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAxM,MAAA,QAAAA,MAAA;MACAwM,YAAA,CAAAvM,OAAA,QAAAA,OAAA;MACAuM,YAAA,CAAAE,IAAA,yBAAAzJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MAEA,IAAAuI,eAAA;MACAA,eAAA,CAAAxK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACA6M,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAC,SAAA,OAAA5D,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACA0J,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACA0J,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAC,SAAA,OAAA5D,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAzH,KAAA;QACAiH,gBAAA,OAAA3J,aAAA;QACA4J,QAAA,EAAAL,YAAA;QACApB,SAAA,EAAA4B,eAAA;QACA1J,WAAA,OAAAA;MACA;MAEAuB,OAAA,CAAAC,GAAA,iDAAAa,KAAA,OAAAtC,YAAA,CAAAlD,QAAA;MAGA,KAAAuF,uBAAA,CAAAC,KAAA,EAAAsB,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,iDAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA+I,OAAA,CAAA7I,QAAA,CAAA4E,OAAA;UACAiE,OAAA,CAAA5H,cAAA,CAAA4H,OAAA,CAAAlQ,MAAA;UACAkQ,OAAA,CAAAhI,WAAA;QACA;UACA;UACAgI,OAAA,CAAA7I,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,gCAAAa,GAAA;QACA0I,OAAA,CAAA7I,QAAA,CAAAV,KAAA;MACA;MAEA,KAAAtE,iBAAA;IACA;IAEAqO,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAA/I,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA/C,OAAA,CAAAC,GAAA,UAAA8C,KAAA;MACA,KAAAA,KAAA,CAAAwC,QAAA;QACA,KAAA/C,QAAA,CAAAV,KAAA;QACA;MACA;MAEA,SAAA1D,aAAA,CAAAmB,MAAA;QACAS,OAAA,CAAAC,GAAA,uBAAA7B,aAAA;QACA,KAAAoE,QAAA,CAAAkF,OAAA;QACA;MACA;;MAEA;MAAA,IAAAqE,UAAA,OAAAR,2BAAA,CAAAvK,OAAA,EACA,KAAA5C,aAAA;QAAA4N,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAN,CAAA,MAAAO,MAAA,GAAAD,UAAA,CAAAvK,CAAA,IAAAkK,IAAA;UAAA,IAAAhM,IAAA,GAAAsM,MAAA,CAAA1G,KAAA;UACA,IAAA5F,IAAA,CAAAuE,iBAAA,KAAAvE,IAAA,CAAAwE,OAAA;YACA,KAAA1B,QAAA,CAAAkF,OAAA,kBAAAsC,MAAA,CAAAtK,IAAA,CAAAC,YAAA,mDAAAqK,MAAA,CAAAtK,IAAA,CAAAuE,iBAAA,sCAAA+F,MAAA,CAAAtK,IAAA,CAAAwE,OAAA;YACA;UACA;QACA;MAAA,SAAAvB,GAAA;QAAAoJ,UAAA,CAAAJ,CAAA,CAAAhJ,GAAA;MAAA;QAAAoJ,UAAA,CAAAH,CAAA;MAAA;MAEA,IAAAjE,YAAA;MACAA,YAAA,CAAAC,OAAA;MACAD,YAAA,CAAAxM,MAAA,QAAAA,MAAA;MACAwM,YAAA,CAAAvM,OAAA,QAAAA,OAAA;MACAuM,YAAA,CAAAE,IAAA,yBAAAzJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,YAAA;MAAA,GAAAC,IAAA;MAEA,IAAAuI,eAAA;MACAA,eAAA,CAAAxK,EAAA,QAAAa,YAAA,CAAAb,EAAA;MACA,SAAAa,YAAA,CAAAlD,QAAA;QACA6M,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAC,SAAA,OAAA5D,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACA0J,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA;QACA0J,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAkD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAC,SAAA,OAAA5D,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA,gBAAA/J,YAAA,CAAAlD,QAAA,cAAAmD,WAAA,cAAAD,YAAA,CAAArC,UAAA;QACAgM,eAAA,CAAAhM,UAAA;QACAgM,eAAA,CAAAK,SAAA,OAAAhE,IAAA,GAAA6D,WAAA,GAAAC,KAAA,QAAAC,OAAA;QACA;MACA;MAEA,IAAAzH,KAAA;QACAiH,gBAAA,OAAA3J,aAAA;QACA4J,QAAA,EAAAL,YAAA;QACApB,SAAA,EAAA4B,eAAA;QACA1J,WAAA,OAAAA;MACA;MAEAuB,OAAA,CAAAC,GAAA,iDAAAa,KAAA,OAAAtC,YAAA,CAAAlD,QAAA;MAGA,KAAAuF,uBAAA,CAAAC,KAAA,EAAAsB,IAAA,WAAAC,GAAA;QACArC,OAAA,CAAAC,GAAA,iDAAAoC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAwJ,OAAA,CAAAtJ,QAAA,CAAA4E,OAAA;UACA0E,OAAA,CAAArI,cAAA,CAAAqI,OAAA,CAAA3Q,MAAA;UACA2Q,OAAA,CAAAzI,WAAA;QACA;UACA;UACAyI,OAAA,CAAAtJ,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,gCAAAa,GAAA;QACAmJ,OAAA,CAAAtJ,QAAA,CAAAV,KAAA;MACA;MAEA,KAAAtE,iBAAA;IACA;IAGAyO,cAAA,WAAAA,eAAA;MAEA,KAAAxO,iBAAA;IACA;IAEAyC,iBAAA,WAAAA,kBAAA;MACA,KAAA1B,YAAA;IACA;IAEA6E,WAAA,WAAAA,YAAA;MAAA,IAAA6I,OAAA;MAAA,WAAAnL,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAiL,SAAA;QAAA,IAAArG,QAAA,EAAAsG,GAAA;QAAA,WAAAnL,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA+K,SAAA;UAAA,kBAAAA,SAAA,CAAA7K,CAAA;YAAA;cAAA6K,SAAA,CAAA5K,CAAA;cAAA4K,SAAA,CAAA7K,CAAA;cAAA,OAEA,IAAA8K,aAAA,EAAAJ,OAAA,CAAA3N,UAAA;YAAA;cAAAuH,QAAA,GAAAuG,SAAA,CAAA1K,CAAA;cACAuK,OAAA,CAAA1N,YAAA,GAAAsH,QAAA,CAAA/K,IAAA;cACAiF,OAAA,CAAAC,GAAA,sBAAAiM,OAAA,CAAA1N,YAAA;cACA,IAAA0N,OAAA,CAAA1N,YAAA,CAAA+N,iBAAA;gBACAL,OAAA,CAAA1N,YAAA,CAAA+N,iBAAA;cACA,WAAAL,OAAA,CAAA1N,YAAA,CAAA+N,iBAAA;gBACAL,OAAA,CAAA1N,YAAA,CAAA+N,iBAAA;cACA,WAAAL,OAAA,CAAA1N,YAAA,CAAA+N,iBAAA;gBACAL,OAAA,CAAA1N,YAAA,CAAA+N,iBAAA;cACA,WAAAL,OAAA,CAAA1N,YAAA,CAAA+N,iBAAA;gBACAL,OAAA,CAAA1N,YAAA,CAAA+N,iBAAA;cACA;cACAvM,OAAA,CAAAC,GAAA,sBAAAiM,OAAA,CAAA1N,YAAA;cACA;cACA0N,OAAA,CAAAM,SAAA;gBACAN,OAAA,CAAAvD,WAAA;cACA;cAAA,OAAA0D,SAAA,CAAAxK,CAAA,IACAiE,QAAA;YAAA;cAAAuG,SAAA,CAAA5K,CAAA;cAAA2K,GAAA,GAAAC,SAAA,CAAA1K,CAAA;cAEA3B,OAAA,CAAA8B,KAAA,uBAAAsK,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAxK,CAAA;UAAA;QAAA,GAAAsK,QAAA;MAAA;IAGA;IAEApI,mBAAA,WAAAA,oBAAA;MAAA,IAAA0I,OAAA;MAAA,WAAA1L,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAwL,SAAA;QAAA,IAAA5G,QAAA,EAAA6G,GAAA;QAAA,WAAA1L,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAsL,SAAA;UAAA,kBAAAA,SAAA,CAAApL,CAAA;YAAA;cAAAoL,SAAA,CAAAnL,CAAA;cAAAmL,SAAA,CAAApL,CAAA;cAAA,OAEA,IAAAqL,qBAAA,EAAAJ,OAAA,CAAAtR,MAAA;YAAA;cAAA2K,QAAA,GAAA8G,SAAA,CAAAjL,CAAA;cACA8K,OAAA,CAAAjO,YAAA,GAAAsH,QAAA,CAAA/K,IAAA;cACAiF,OAAA,CAAAC,GAAA,sBAAAwM,OAAA,CAAAjO,YAAA;;cAEA;cACAiO,OAAA,CAAAlO,UAAA,GAAAkO,OAAA,CAAAjO,YAAA,CAAAb,EAAA;cACA8O,OAAA,CAAArR,OAAA,GAAAqR,OAAA,CAAAjO,YAAA,CAAApD,OAAA;cAEA,IAAAqR,OAAA,CAAAjO,YAAA,CAAA+N,iBAAA;gBACAE,OAAA,CAAAjO,YAAA,CAAA+N,iBAAA;cACA,WAAAE,OAAA,CAAAjO,YAAA,CAAA+N,iBAAA;gBACAE,OAAA,CAAAjO,YAAA,CAAA+N,iBAAA;cACA,WAAAE,OAAA,CAAAjO,YAAA,CAAA+N,iBAAA;gBACAE,OAAA,CAAAjO,YAAA,CAAA+N,iBAAA;cACA,WAAAE,OAAA,CAAAjO,YAAA,CAAA+N,iBAAA;gBACAE,OAAA,CAAAjO,YAAA,CAAA+N,iBAAA;cACA;cACAvM,OAAA,CAAAC,GAAA,sBAAAwM,OAAA,CAAAjO,YAAA;cACA;cACAiO,OAAA,CAAAD,SAAA;gBACAC,OAAA,CAAA9D,WAAA;cACA;cAAA,OAAAiE,SAAA,CAAA/K,CAAA,IACAiE,QAAA;YAAA;cAAA8G,SAAA,CAAAnL,CAAA;cAAAkL,GAAA,GAAAC,SAAA,CAAAjL,CAAA;cAEA3B,OAAA,CAAA8B,KAAA,+BAAA6K,GAAA;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA/K,CAAA;UAAA;QAAA,GAAA6K,QAAA;MAAA;IAGA;IAGAI,aAAA,WAAAA,cAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAE,iBAAA,WAAAA,kBAAAF,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IACA;IACAG,wBAAA,WAAAA,yBAAAH,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IAEA;IACAI,2BAAA,WAAAA,4BAAAJ,QAAA;MACA,IAAAK,OAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAL,QAAA;IACA;IAEA;IACAM,qBAAA,WAAAA,sBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAE,qBAAA,WAAAA,sBAAAF,MAAA;MACA,IAAAF,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAE,MAAA;IACA;IAEA;IACAG,WAAA,WAAAA,YAAAxN,GAAA;MACA,IAAAyN,eAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,eAAA,CAAAzN,GAAA,CAAA0N,IAAA;IACA;IAEA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAxP,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACA;IACAyP,wBAAA,WAAAA,yBAAAC,GAAA;MACA,KAAAA,GAAA;QACA,KAAAhT,kBAAA,CAAAkC,WAAA;MACA;IACA;IACA+Q,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAA9Q,mBAAA;MACA,KAAA+Q,WAAA;MACA;MACA,KAAAzP,cAAA;MACA,KAAA6N,SAAA;QACA,IAAA2B,OAAA,CAAAtF,KAAA,CAAAwF,WAAA;UACAF,OAAA,CAAAtF,KAAA,CAAAwF,WAAA,CAAAC,cAAA;QACA;MACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA;MACA,IAAAA,SAAA,CAAAjP,MAAA;QACA,IAAAkP,YAAA,GAAAD,SAAA,CAAAA,SAAA,CAAAjP,MAAA;QACA,KAAAsJ,KAAA,CAAAwF,WAAA,CAAAC,cAAA;QACA,KAAAzF,KAAA,CAAAwF,WAAA,CAAAK,kBAAA,CAAAD,YAAA;QACA,KAAA9P,cAAA,GAAA8P,YAAA;MACA;QACA,KAAA9P,cAAA,GAAA6P,SAAA;MACA;IACA;IACAG,sBAAA,WAAAA,uBAAA;MACA,UAAAhQ,cAAA;QACA,KAAA6D,QAAA,CAAAkF,OAAA;QACA;MACA;MAEA,KAAAzM,kBAAA,CAAAkC,WAAA,QAAAwB,cAAA,CAAAvD,OAAA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,KAAAiC,mBAAA;MACA,KAAAmF,QAAA,CAAA4E,OAAA;IAIA;IACAgH,WAAA,WAAAA,YAAA;MAAA,IAAAQ,OAAA;MACA;MACA,KAAArR,UAAA,QAAAyB,oBAAA;MACA,KAAAzB,UAAA,CAAAyF,OAAA,WAAAtD,IAAA;QACAA,IAAA,CAAAmP,UAAA,GAAAD,OAAA,CAAA3B,iBAAA,CAAAvN,IAAA,CAAAmP,UAAA;MACA;MACA7O,OAAA,CAAAC,GAAA,oBAAA1C,UAAA;IACA;IACAuR,uBAAA,WAAAA,wBAAAC,QAAA;MACA,IAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAD,QAAA;IACA;IACAE,aAAA,WAAAA,cAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAC,YAAA,SAAA7R,UAAA,CAAAjC,MAAA,QAAA+T,WAAA;MACA,IAAAC,aAAA,SAAA/R,UAAA,CAAAlC,OAAA,QAAAgU,WAAA;MACA,IAAAE,oBAAA,SAAAhS,UAAA,CAAAL,cAAA,QAAAmS,WAAA;;MAEA;MACA,KAAA7R,UAAA,QAAAyB,oBAAA,CAAAqG,MAAA,WAAA3F,IAAA;QACA,IAAArE,MAAA,IAAAqE,IAAA,CAAArE,MAAA,QAAAkU,QAAA,GAAAH,WAAA;QACA,IAAAhU,OAAA,IAAAsE,IAAA,CAAAtE,OAAA,QAAAmU,QAAA,GAAAH,WAAA;QACA,IAAAnS,cAAA,IAAAyC,IAAA,CAAAzC,cAAA,QAAAsS,QAAA,GAAAH,WAAA;;QAEA;QACA,IAAAI,WAAA,IAAAL,YAAA,IAAA9T,MAAA,CAAAkK,QAAA,CAAA4J,YAAA;QACA,IAAAM,YAAA,IAAAJ,aAAA,IAAAjU,OAAA,CAAAmK,QAAA,CAAA8J,aAAA;QACA,IAAAK,mBAAA,IAAAJ,oBAAA,IAAArS,cAAA,CAAAsI,QAAA,CAAA+J,oBAAA;QAEA,OAAAE,WAAA,IAAAC,YAAA,IAAAC,mBAAA;MACA;;MAEA;MACA,KAAAnS,UAAA,CAAAyF,OAAA,WAAAtD,IAAA;QACAA,IAAA,CAAAmP,UAAA,GAAAK,OAAA,CAAAjC,iBAAA,CAAAvN,IAAA,CAAAmP,UAAA;MACA;IACA;IACAc,WAAA,WAAAA,YAAA;MACA,KAAArS,UAAA;QACAjC,MAAA;QACAD,OAAA;QACA6B,cAAA;MACA;MACA,KAAAmR,WAAA;IACA;IACAwB,eAAA,WAAAA,gBAAAjC,IAAA;MACA,IAAAP,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAO,IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACAkC,qBAAA,WAAAA,sBAAArB,SAAA;MACA,KAAAtP,YAAA,GAAAsP,SAAA;IACA;IAEA;IACAsB,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,OAAA;MACA,IAAAhN,KAAA,QAAAF,MAAA,CAAAC,OAAA,CAAAC,KAAA;MACA,KAAAA,KAAA,CAAAwC,QAAA;QACA,KAAA/C,QAAA,CAAAV,KAAA;QACA;MACA;MACA,IAAAkO,SAAA;MACA,KAAA9Q,YAAA,CAAA8D,OAAA,WAAAtD,IAAA;QACA,IAAAA,IAAA,CAAAuE,iBAAA,KAAAvE,IAAA,CAAAwE,OAAA;UACA6L,OAAA,CAAAvN,QAAA,CAAAkF,OAAA;UACAsI,SAAA;QACA;MACA;MAEA,IAAAA,SAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA,IAAArI,YAAA;QACAC,OAAA;QACAzM,MAAA,OAAAA,MAAA;QACAC,OAAA,OAAAA,OAAA;QACAyM,IAAA,wBAAAzJ,aAAA,CAAAqB,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,YAAA;QAAA,GAAAC,IAAA;MACA;;MAEA;MACA,IAAAkI,eAAA;QACAnK,EAAA,OAAAa,YAAA,CAAAb,EAAA;QACApC,eAAA;QAAA;QACAC,aAAA,MAAAgJ,IAAA;QACArI,UAAA;MACA;MAEA,KAAA+C,YAAA,CAAA8D,OAAA,WAAAtD,IAAA;QACA;QACAA,IAAA,CAAAuQ,iBAAA,GAAAvQ,IAAA,CAAAuE,iBAAA;MACA;;MAEA;MACA,IAAAnD,KAAA;QACAiH,gBAAA,OAAA7I,YAAA;QAAA;QACA8I,QAAA,EAAAL,YAAA;QACApB,SAAA,EAAAuB,eAAA;QACArJ,WAAA,OAAAA;MACA;;MAEA;MACA,KAAAoC,uBAAA,CAAAC,KAAA,EAAAsB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAyN,OAAA,CAAAvN,QAAA,CAAA4E,OAAA;UACA2I,OAAA,CAAAtM,cAAA,CAAAsM,OAAA,CAAA5U,MAAA;UACA4U,OAAA,CAAA1M,WAAA;UACA;UACA0M,OAAA,CAAA7Q,YAAA;QACA;UACA6Q,OAAA,CAAAvN,QAAA,CAAAV,KAAA,CAAAO,GAAA,CAAAI,OAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACA3C,OAAA,CAAA8B,KAAA,0CAAAa,GAAA;QACAoN,OAAA,CAAAvN,QAAA,CAAAV,KAAA;MACA;IACA;IACAoO,iBAAA,WAAAA,kBAAA;MACAlQ,OAAA,CAAAC,GAAA,2BAAAd,kBAAA;MACA,IAAAZ,UAAA,QAAAY,kBAAA,CAAAZ,UAAA;MACA,IAAAnD,OAAA,GAAA+U,MAAA,MAAAhR,kBAAA,CAAA/D,OAAA;MACA,IAAAqD,WAAA,QAAAU,kBAAA,CAAAV,WAAA;MACA,IAAAgC,QAAA,QAAAtB,kBAAA,CAAAsB,QAAA;MACA,IAAAtF,MAAA,GAAAgV,MAAA,MAAAhR,kBAAA,CAAAhE,MAAA;MACA,IAAAiV,GAAA,kDAAApG,MAAA,CAAAzL,UAAA,eAAAyL,MAAA,CAAA5O,OAAA,mBAAA4O,MAAA,CAAAvL,WAAA,gBAAAuL,MAAA,CAAAvJ,QAAA,cAAAuJ,MAAA,CAAA7O,MAAA;MACAwJ,MAAA,CAAAC,IAAA,CAAAwL,GAAA;IACA;EACA;AACA", "ignoreList": []}]}