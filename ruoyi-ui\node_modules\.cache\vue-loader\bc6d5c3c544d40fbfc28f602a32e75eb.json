{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue?vue&type=style&index=0&id=4b5796a9&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue", "mtime": 1756372569221}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5idG4td3JhcHBlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQouZGlzcGF0Y2gtYnRuIHsNCiAgbWFyZ2luLWxlZnQ6IDE1cHg7DQp9DQoNCi5hcHAtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLnFyY29kZS1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLnFyY29kZSB7DQogIG1hcmdpbjogMCBhdXRvOw0KfQ0KDQouYm94LWNhcmQgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBib3JkZXItcmFkaXVzOiA1cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQoNCi5jYXJkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLnNlY3Rpb24tY29udGFpbmVyIHsNCiAgbWFyZ2luLWJvdHRvbTogMzBweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjA1KTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgYm9yZGVyOiAxcHggc29saWQgI2ViZWVmNTsNCn0NCg0KLnNlY3Rpb24tY29udGFpbmVyOm50aC1jaGlsZCgxKSB7DQogIGJvcmRlci10b3A6IDRweCBzb2xpZCAjRjU2QzZDOw0KICAvKiDpgJrooYzor4Hkuoznu7TnoIHmqKHlnZcgLSDnuqLoibIgKi8NCn0NCg0KLnNlY3Rpb24tY29udGFpbmVyOm50aC1jaGlsZCgyKSB7DQogIGJvcmRlci10b3A6IDRweCBzb2xpZCAjNDA5RUZGOw0KICAvKiDlj7jmnLrkv6Hmga/mqKHlnZcgLSDok53oibIgKi8NCn0NCg0KLnNlY3Rpb24tY29udGFpbmVyOm50aC1jaGlsZCgzKSB7DQogIGJvcmRlci10b3A6IDRweCBzb2xpZCAjNjdDMjNBOw0KICAvKiDovabovobkv6Hmga/mqKHlnZcgLSDnu7/oibIgKi8NCn0NCg0KLnNlY3Rpb24tY29udGFpbmVyOm50aC1jaGlsZCg0KSB7DQogIGJvcmRlci10b3A6IDRweCBzb2xpZCAjRTZBMjNDOw0KICAvKiDnianotYTliJfooajmqKHlnZcgLSDmqZnoibIgKi8NCn0NCg0KLnNlY3Rpb24tY29udGFpbmVyOm50aC1jaGlsZCg1KSB7DQogIGJvcmRlci10b3A6IDRweCBzb2xpZCAjOTA5Mzk5Ow0KICAvKiDml6Xlv5fliJfooajmqKHlnZcgLSDngbDoibIgKi8NCn0NCg0KLnNlY3Rpb24tdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJhY2tncm91bmQ6ICNmYWZhZmE7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgcGFkZGluZy1sZWZ0OiAzMHB4Ow0KfQ0KDQouc2VjdGlvbi10aXRsZTo6YmVmb3JlIHsNCiAgY29udGVudDogJyc7DQogIHdpZHRoOiA0cHg7DQogIGhlaWdodDogMTZweDsNCiAgYmFja2dyb3VuZDogY3VycmVudENvbG9yOw0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIGxlZnQ6IDE1cHg7DQogIHRvcDogNTAlOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7DQogIGJvcmRlci1yYWRpdXM6IDJweDsNCn0NCg0KLnNlY3Rpb24tY29udGFpbmVyOm50aC1jaGlsZCgxKSAuc2VjdGlvbi10aXRsZSB7DQogIGNvbG9yOiAjRjU2QzZDOw0KfQ0KDQouc2VjdGlvbi1jb250YWluZXI6bnRoLWNoaWxkKDIpIC5zZWN0aW9uLXRpdGxlIHsNCiAgY29sb3I6ICM0MDlFRkY7DQp9DQoNCi5zZWN0aW9uLWNvbnRhaW5lcjpudGgtY2hpbGQoMykgLnNlY3Rpb24tdGl0bGUgew0KICBjb2xvcjogIzY3QzIzQTsNCn0NCg0KLnNlY3Rpb24tY29udGFpbmVyOm50aC1jaGlsZCg0KSAuc2VjdGlvbi10aXRsZSB7DQogIGNvbG9yOiAjRTZBMjNDOw0KfQ0KDQouc2VjdGlvbi1jb250YWluZXI6bnRoLWNoaWxkKDUpIC5zZWN0aW9uLXRpdGxlIHsNCiAgY29sb3I6ICM5MDkzOTk7DQp9DQoNCi5zZWN0aW9uLWNvbnRhaW5lciAuZWwtZGVzY3JpcHRpb25zLA0KLnNlY3Rpb24tY29udGFpbmVyIC5lbC10YWJsZSwNCi5zZWN0aW9uLWNvbnRhaW5lciAuZWwtdGltZWxpbmUgew0KICBwYWRkaW5nOiAwIDIwcHggMjBweDsNCn0NCg0KLmZvcm0tZm9vdGVyIHsNCiAgbWFyZ2luLXRvcDogMzBweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouZHJpdmVyLXBob3RvcyB7DQogIHBhZGRpbmc6IDAgMjBweCAyMHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDIwcHg7DQogIGZsZXgtd3JhcDogd3JhcDsNCn0NCg0KLnBob3RvLWl0ZW0gew0KICB3aWR0aDogMzAwcHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgYm94LXNoYWRvdzogMCAycHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wNSk7DQp9DQoNCi5waG90by1pdGVtIGg0IHsNCiAgcGFkZGluZzogMTBweDsNCiAgYmFja2dyb3VuZDogI2Y1ZjdmYTsNCiAgbWFyZ2luOiAwOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2ViZWVmNTsNCn0NCg0KLnBob3RvLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDEwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQoucGhvdG8tY29udGFpbmVyIGltZyB7DQogIG1heC13aWR0aDogMTAwJTsNCiAgbWF4LWhlaWdodDogMjAwcHg7DQogIG9iamVjdC1maXQ6IGNvbnRhaW47DQp9DQoNCi5idXR0b24tY29udGFpbmVyIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0K"}, {"version": 3, "sources": ["task.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA20EA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "task.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\" style=\"display: flex; align-items: center; justify-content: flex-start;\">\r\n        <h2>派车任务详情</h2>\r\n        <el-tag size=\"medium\" style=\"margin-left: 20px; margin-top: 10px;\">\r\n          任务状态： {{ getStatusText(taskInfoForm.taskStatus) }}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <!-- 任务流程图部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务流程</div>\r\n        <div class=\"process-flow-container\">\r\n          <!-- <img style=\"width: 100%; max-height: 400px; object-fit: contain;\" :src=\"require('@/assets/images/task-flow-chart.png')\" /> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 通行证二维码部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">通行证二维码</div>\r\n        <div class=\"qrcode-container\">\r\n          <div ref=\"qrCode\" class=\"qrcode\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 司机信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">司机信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"姓名\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 姓名</template>\r\n            {{ taskInfoForm.driverName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">\r\n            <template slot=\"label\"><i class=\"el-icon-mobile-phone\"></i> 手机号</template>\r\n            {{ taskInfoForm.mobilePhone }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"身份证号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 身份证号</template>\r\n            {{ taskInfoForm.idCardNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"性别\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 性别</template>\r\n            {{ taskInfoForm.sex === 1 ? '男' : '女' }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"所属单位\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 所属单位</template>\r\n            {{ taskInfoForm.companyName }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 司机照片和证件照片 -->\r\n        <div class=\"driver-photos\"\r\n          v-if=\"driverInfo.photo || driverInfo.driverLicenseImgs || driverInfo.vehicleLicenseImgs\">\r\n          <div class=\"photo-item\" v-if=\"driverInfo.photo\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 司机照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.faceImg\" alt=\"司机照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.faceImg\" fit=\"contain\" fallback=\"\"\r\n                :preview-src-list=\"[taskInfoForm.faceImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.driverLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 驾驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.driverLicenseImg\" alt=\"驾驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.driverLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.driverLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.vehicleLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 行驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.drivingLicenseImg\" alt=\"行驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.drivingLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.drivingLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 车辆信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">车辆信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"车牌号\" v-if=\"taskInfoForm.carNum != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 车牌号</template>\r\n            <el-tag type=\"primary\">{{ taskInfoForm.carNum }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车牌颜色\" v-if=\"taskInfoForm.licensePlateColor != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-takeaway-box\"></i> 车牌颜色</template>\r\n            {{ taskInfoForm.licensePlateColor }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆道路运输证号\" v-if=\"taskInfoForm.trailerId != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"挂车号牌\" v-if=\"taskInfoForm.trailerNumber\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 挂车号牌</template>\r\n            <el-tag type=\"info\">{{ taskInfoForm.trailerNumber }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"挂车道路运输证号\" v-if=\"taskInfoForm.trailerId\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 挂车运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"轴型\" v-if=\"taskInfoForm.axisType != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-data-line\"></i> 轴型</template>\r\n            {{ taskInfoForm.axisType }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"货车自重\" v-if=\"taskInfoForm.driverWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-heavy-rain\"></i> 货车自重</template>\r\n            {{ taskInfoForm.driverWeight }} kg\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车货总质量限值\" v-if=\"taskInfoForm.maxWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-opportunity\"></i> 总质量限值</template>\r\n            {{ taskInfoForm.maxWeight }} kg\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆排放标准\" v-if=\"taskInfoForm.vehicleEmissionStandards != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-magic-stick\"></i> 排放标准</template>\r\n            <el-tag :type=\"getEmissionStandardsTagType(taskInfoForm.vehicleEmissionStandards)\">\r\n              {{ getEmissionStandardsText(taskInfoForm.vehicleEmissionStandards) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"发动机号\" v-if=\"taskInfoForm.engineNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-set-up\"></i> 发动机号</template>\r\n            {{ taskInfoForm.engineNumber }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆识别代码\" v-if=\"taskInfoForm.vinNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document-checked\"></i> 车辆识别代码</template>\r\n            {{ taskInfoForm.vinNumber }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 任务物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"taskMaterials\" style=\"width: 100%\" border @selection-change=\"handleSelectionChange\">\r\n          <!-- <el-table-column type=\"selection\" width=\"55\" v-if=\"measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"150\">\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0\" -->\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫出厂确认数量\" width=\"230\"\r\n            v-if=\"taskInfoForm.taskStatus >= 4 && measureFlag == 0 && taskInfoForm.taskType != 2\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 4\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNumIn\" label=\"门卫入厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskType !== 1 && taskInfoForm.taskStatus >= 5\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNumIn\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 5\" />\r\n            </template>\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0 && taskInfoForm.taskType == 2\" -->\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"分厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskStatus > 7 && (taskInfoForm.taskType == 3 || taskInfoForm.taskType == 2)\">\r\n            <!-- <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\" disabled />\r\n            </template> -->\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\">\r\n          </el-table-column>\r\n\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5 && (measureFlag == 0 && taskInfoForm.taskType == 2 && taskInfoForm.taskStatus == 7)\" -->\r\n          <!-- <el-table-column v-if=\"measureFlag == 0 && (taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5)\"\r\n            label=\"操作\" width=\"200\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px;\">\r\n\r\n                <div v-if=\"editingRow === scope.row\">\r\n                  <el-button size=\"mini\" type=\"success\" @click=\"saveDoorManRow(scope.row)\">保存</el-button>\r\n                  <el-button size=\"mini\" @click=\"cancelDoorManEdit(scope.row)\">取消</el-button>\r\n                </div>\r\n\r\n                <div v-else>\r\n                  <el-button v-hasPermi=\"['leave:task:doorManConfirm']\" size=\"mini\" type=\"primary\"\r\n                    @click=\"editDoorManRow(scope.row)\">门卫编辑</el-button>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column> -->\r\n        </el-table>\r\n\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 4\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRow\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫出厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 5\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRowIn\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫入厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"button-container\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 7\">\r\n          <el-button type=\"primary\" @click=\"handleNonMeasureFactoryConfirm\">\r\n            分厂确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1\">\r\n        <div class=\"section-title\">计量信息</div>\r\n        <div class=\"info-footer\" style=\"margin-top: 20px;\" v-if=\"measureFlag == 1\">\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"皮重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tare != null\">\r\n              {{ taskInfoForm.tare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.gross != null\">\r\n              {{ taskInfoForm.gross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.netWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tareTime != null\">\r\n              {{ taskInfoForm.tareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.grossTime != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secTare != null\">\r\n              {{ taskInfoForm.secTare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secGross != null\">\r\n              {{ taskInfoForm.secGross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeight != null\">\r\n              {{ taskInfoForm.secNetWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secTareTime != null\">\r\n              {{ taskInfoForm.secTareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secGrossTime != null\">\r\n              {{ taskInfoForm.secGrossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeightTime != null\">\r\n              {{ taskInfoForm.secNetWeightTime }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5\" -->\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 4\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫出厂确认\r\n            </el-button>\r\n          </div>\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 5\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫入厂确认\r\n            </el-button>\r\n          </div>\r\n          <!-- 新增分厂确认按钮 -->\r\n          <!-- <div class=\"btn-wrapper\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"openFactoryConfirmDialog\" class=\"dispatch-btn\">\r\n              分厂确认\r\n            </el-button>\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可编辑的出库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 2\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialName).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialSpec).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\" v-if=\"taskInfoForm.taskType\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSpec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutTotal\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutTotalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutProcessType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutHeatNo\" placeholder=\"请输入炉号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSteelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutAxles\" placeholder=\"请选择轴数\">\r\n                  <el-option label=\"2\" value=\"2\"></el-option>\r\n                  <el-option label=\"3\" value=\"3\"></el-option>\r\n                  <el-option label=\"4\" value=\"4\"></el-option>\r\n                  <el-option label=\"5\" value=\"5\"></el-option>\r\n                  <el-option label=\"6轴标准\" value=\"6轴标准\"></el-option>\r\n                  <el-option label=\"6轴非标准\" value=\"6轴非标准\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.stockOutRemark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitStockOutConfirm\" size=\"medium\" class=\"dispatch-btn\">确认出库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的出库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 2 && taskInfoForm.taskType !== 2 && taskInfoForm.isDirectSupply != 3\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"materialSpecs\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.stockOutSpec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.stockOutTotals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.stockOutProcessType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.stockOutHeatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.stockOutSteelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-input :value=\"taskInfoForm.stockOutAxles\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.stockOutRemark\" disabled></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 可编辑的入库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 7\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.secGross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.gross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.processType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.steelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input v-model=\"factoryConfirmForm.spec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.total\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.totalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.heatNo\" placeholder=\"请输入炉号/批号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重(t)\">\r\n                <el-input v-model=\"factoryConfirmForm.deductWeight\" placeholder=\"请输入扣重\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.remark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"是否直供\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-checkbox v-model=\"factoryConfirmForm.showDropdown\" @change=\"handleShowDropdownChange\">是否直供</el-checkbox>\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"factoryConfirmForm.showDropdown\" label=\"直供申请单号\">\r\n            <el-input v-model=\"factoryConfirmForm.extraOption\" placeholder=\"请选择直供申请单号\" readonly style=\"width: 300px;\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openOptionDialog\"></el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitFactoryConfirm\" size=\"medium\" class=\"dispatch-btn\">确认入库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的入库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 7 && taskInfoForm.taskType !== 1\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.gross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.processType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.steelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.spec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.totals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.heatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重\">\r\n                <el-input :value=\"taskInfoForm.deductWeight\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.remark\" disabled></el-input>\r\n          </el-form-item>\r\n\r\n\r\n          <el-form-item v-if=\"taskInfoForm.directSupplyTaskNo\" label=\"直供对应任务单号\">\r\n            <el-input :value=\"taskInfoForm.directSupplyTaskNo\" disabled style=\"width: 300px;\"></el-input>\r\n            <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n              @click=\"openNewTaskWindow\">前往任务单号</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务日志</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in taskLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <div class=\"form-footer\">\r\n        <el-button @click=\"cancel\">返 回</el-button>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 选项弹窗 -->\r\n    <el-dialog title=\"选择直供申请单号\" :visible.sync=\"optionDialogVisible\" width=\"1600px\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"计划号\">\r\n          <el-input v-model=\"searchForm.planNo\" placeholder=\"请输入计划号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请编号\">\r\n          <el-input v-model=\"searchForm.applyNo\" placeholder=\"请输入申请编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位\">\r\n          <el-input v-model=\"searchForm.receiveCompany\" placeholder=\"请输入收货单位\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"searchOptions\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewWindow\">直供对应任务号\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table :data=\"optionList\" style=\"width: 100%\" @selection-change=\"handleOptionSelection\" ref=\"optionTable\">\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        <el-table-column prop=\"planNo\" label=\"计划号\" width=\"150\" />\r\n        <el-table-column prop=\"applyNo\" label=\"申请编号\" width=\"150\" />\r\n        <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\" />\r\n        <el-table-column prop=\"materialSpec\" label=\"物料规格\" width=\"120\" />\r\n        <el-table-column prop=\"sourceCompany\" label=\"申请单位\" width=\"150\" />\r\n        <el-table-column prop=\"receiveCompany\" label=\"收货单位\" width=\"150\" />\r\n        <el-table-column prop=\"plannedAmount\" label=\"计划量/t\" width=\"150\" />\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"planStatus\" label=\"状态\" width=\"150\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"optionDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOptionSelection\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getTask, getTaskByTaskNo, getTaskmaterials, getProcessList, getDirectSupplyPlans, getDirectSupplyPlanAndTaskDetail, handleUnload, handleStockOut, isAllowDispatch, getPlanMaterials, editTaskmaterials, getTaskLogs, addLeaveLog, updateTask, addLeaveLogAndEditTaskMaterialsAndUpdateTask } from \"@/api/leave/task\";\r\nimport { detailPlan } from \"@/api/leave/plan\";\r\nimport { listXctgDriverCar, getXctgDriverCar, delXctgDriverCar, addXctgDriverCar, updateXctgDriverCar, exportXctgDriverCar } from \"@/api/truck/common/xctgDriverCar\";\r\nimport { Message } from \"element-ui\";\r\nimport QRCode from \"qrcodejs2\";\r\nimport { getClientIpSilent } from \"@/utils/fileReader\";\r\n\r\n\r\nexport default {\r\n  name: \"DispatchTaskDetail\",\r\n  data() {\r\n    return {\r\n      factoryConfirmDialogVisible: false,\r\n      factoryConfirmForm: {\r\n        companyName: '',\r\n        taskNo: '',\r\n        applyNo: '',\r\n        planNo: '',\r\n        taskType: null,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: null,\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9, // 完成状态\r\n        carNum: '', // 车牌号\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        handledMaterialName: '',\r\n        sourceCompany: '',\r\n        receiveCompany: '',\r\n        showDropdown: false,\r\n        extraOption: '',\r\n        deductWeight: null, // 添加扣重字段\r\n      },\r\n      optionDialogVisible: false,\r\n      searchForm: {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      },\r\n      optionList: [],\r\n      editDoorManStatus: false,\r\n      editFactoryStatus: false,\r\n      // 司机信息\r\n      driverInfo: {\r\n        id: 1,\r\n        name: '王小明',\r\n        idCard: '110101199001010001',\r\n        phone: '13800138000',\r\n        gender: '1',\r\n        company: '北京运输有限公司',\r\n        photo: 'https://via.placeholder.com/150',\r\n        driverLicenseImgs: 'https://via.placeholder.com/300x200',\r\n        vehicleLicenseImgs: 'https://via.placeholder.com/300x200'\r\n      },\r\n\r\n      // 车辆信息\r\n      carInfo: {},\r\n\r\n      // 任务物资列表\r\n      taskMaterials: [],\r\n\r\n      // 任务日志列表\r\n      taskLogs: [],\r\n\r\n      // 申请编号\r\n      applyNo: null,\r\n\r\n      isdoorMan: false,\r\n\r\n      // 派车任务ID\r\n      dispatchId: null,\r\n\r\n      taskInfoForm: {},\r\n\r\n      measureFlag: null,\r\n\r\n      backupTaskMaterials: null,\r\n      taskNo: null,\r\n\r\n      selectedOption: null,\r\n\r\n      planForm: {},\r\n\r\n      processTypeOptions: [], // 动态加载的加工类型选项\r\n\r\n      filteredProcessTypeOptions: [], // 过滤后的加工类型选项\r\n\r\n      searchProcessTypeQuery: '',// 搜索框的值\r\n\r\n      directSupplyPlanList: [], // 直供计划列表\r\n\r\n      editingRow: null,\r\n\r\n      selectedRows: [], // 添加选中行数据数组\r\n\r\n      directSupplyParams: {}\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    displayProcessTypeOptions() {\r\n      return this.searchProcessTypeQuery ? this.filteredProcessTypeOptions : this.processTypeOptions;\r\n    },\r\n\r\n    // 是否有选中的项\r\n    hasSelectedItems() {\r\n      return this.selectedRows.length > 0;\r\n    },\r\n\r\n    // 添加计算属性\r\n    materialNames() {\r\n      return this.taskMaterials.map(item => item.materialName).join(' ');\r\n    },\r\n\r\n    materialSpecs() {\r\n      return this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n    }\r\n  },\r\n\r\n  activated() {\r\n    console.log(\"activated执行\");\r\n    this.resetTaskInfoForm();\r\n\r\n    // 获取路由参数 - 支持两种方式：query参数和路径参数\r\n    let taskNo = this.$route.params.taskNo || this.$route.query.taskNo;\r\n\r\n    if (taskNo) {\r\n      // 新的方式：通过taskNo获取所有参数\r\n      this.taskNo = taskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeDataByTaskNo();\r\n    } else {\r\n      // 兼容旧的方式：从query参数获取\r\n      const { dispatchId, applyNo, measureFlag, planType, taskNo: queryTaskNo } = this.$route.query;\r\n      this.dispatchId = dispatchId;\r\n      this.applyNo = applyNo;\r\n      this.measureFlag = measureFlag;\r\n      console.log(\"this.measureFlag\", this.measureFlag)\r\n      this.planType = planType;\r\n      this.taskNo = queryTaskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeData();\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    /**\r\n     * 调用带客户端IP信息的接口\r\n     * @param {Object} param 接口参数\r\n     * @returns {Promise} 接口调用结果\r\n     */\r\n    async callApiWithGateLocation(param) {\r\n      try {\r\n        // 获取客户端IP地址\r\n        const clientIp = await getClientIpSilent();\r\n\r\n        // 将客户端IP添加到参数中\r\n        param.clientIp = clientIp;\r\n        console.log('获取到客户端IP地址:', clientIp, '已添加到请求参数中');\r\n\r\n        // 调用接口\r\n        return await addLeaveLogAndEditTaskMaterialsAndUpdateTask(param);\r\n      } catch (error) {\r\n        console.error('调用接口失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    getDirectSupplyPlanAndTask() {\r\n\r\n\r\n      let leaveTask0 = {\r\n        taskNo: this.taskInfoForm.directSupplyTaskNo\r\n      }\r\n\r\n      getDirectSupplyPlanAndTaskDetail(leaveTask0).then(res => {\r\n        console.log(\"getDirectSupplyPlanAndTaskDetail\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyParams.dispatchId = res.rows[0].id;\r\n          this.directSupplyParams.applyNo = res.rows[0].applyNo;\r\n          this.directSupplyParams.taskNo = res.rows[0].taskNo;\r\n          this.directSupplyParams.measureFlag = res.rows[1].measureFlag;\r\n          this.directSupplyParams.planType = res.rows[1].planType;\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getDirectSupplyPlanAndTaskDetail error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      });\r\n\r\n    },\r\n\r\n    validDoorMan() {\r\n      this.$store.getters.roles.forEach(item => {\r\n        if (item == 'leave.quard') {\r\n          this.isdoorMan = true;\r\n        }\r\n      });\r\n      console.log(\"isdoorMan\", this.isdoorMan)\r\n    },\r\n    async initializeData() {\r\n      try {\r\n        // 等待所有异步操作完成\r\n        await this.getTaskInfo();\r\n        await this.getTaskmaterialList(this.taskNo);\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    async initializeDataByTaskNo() {\r\n      try {\r\n        // 通过taskNo获取任务信息\r\n        await this.getTaskInfoByTaskNo();\r\n\r\n        // 通过applyNo获取计划信息\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 获取任务物资列表\r\n        await this.getTaskmaterialList(this.taskNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data by taskNo:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    uploadFactoryConfirmForm() {\r\n      // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n      this.taskMaterials.forEach(item => {\r\n        item.doormanReceiveNum = item.planNum;\r\n        console.log(\"item.planType\", this.planForm.planType);\r\n        if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n          item.doormanReceiveNumIn = item.planNum;\r\n        }\r\n      });\r\n\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      let materialSpecs = this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        driverName: this.taskInfoForm.driverName,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        materialSpecs: materialSpecs,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n    },\r\n\r\n    openNewWindow() {\r\n      const newWindowUrl = 'http://localhost/leave/leavePlanList'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    //获取可以直供的计划\r\n    async getDirectSupplyList() {\r\n      try {\r\n        let leavePlan = {\r\n          sourceCompany: this.planForm.sourceCompany,\r\n          planType: 3,\r\n        }\r\n        console.log(\"获取可以直供的计划\", leavePlan)\r\n\r\n        const res = await getDirectSupplyPlans(leavePlan);\r\n        console.log(\"getDirectSupplyPlans\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyPlanList = res.rows;\r\n          // //查询每个计划的物资\r\n          // for (const item of this.directSupplyPlanList) {\r\n          //   console.log(\"item\", item)\r\n          //   let leavePlanMaterial = {\r\n          //     applyNo: item.applyNo\r\n          //   };\r\n          //   const response = await getPlanMaterials(leavePlanMaterial);\r\n          //   if (response.code == 200) {\r\n          //     console.log(\"getPlanMaterials\", response)\r\n          //     item.materialName = response.rows[0].materialName;\r\n          //     item.materialSpec = response.rows[0].materialSpec;\r\n          //   } else {\r\n          //     this.$message.error(response.message || '获取计划物资失败');\r\n          //   }\r\n          // }\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      } catch (err) {\r\n        console.error('getDirectSupplyPlans error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      }\r\n    },\r\n    filterProcessType(query) {\r\n      this.searchProcessTypeQuery = query;\r\n\r\n      if (this.searchProcessTypeQuery) {\r\n        console.log(\"processTypeOptions\", this.processTypeOptions)\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions.filter(item =>\r\n          item.value.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions;\r\n      }\r\n    },\r\n    getProcessType() {\r\n      getProcessList().then(res => {\r\n        console.log(\"getProcessList\", res)\r\n        if (res.code == 200) {\r\n          this.processTypeOptions = res.rows.map(item => ({\r\n            value: item.processname,\r\n            label: item.processname\r\n          }));\r\n          this.filteredProcessTypeOptions = this.processTypeOptions; // 初始化过滤后的选项\r\n        } else {\r\n          this.$message.error(res.message || '获取加工类型失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getProcessList error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    async getPlanInfo(applyNo) {\r\n      try {\r\n        const response = await detailPlan(applyNo);\r\n        console.log(\"detailPlan\", response);\r\n        this.planForm = response.data;\r\n\r\n        // 从计划信息中获取planType和measureFlag\r\n        this.planType = this.planForm.planType;\r\n        this.measureFlag = this.planForm.measureFlag;\r\n        console.log(\"this.planType\", this.planType);\r\n        console.log(\"this.measureFlag\", this.measureFlag);\r\n\r\n        await this.getDirectSupplyList();\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getPlanInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    openFactoryConfirmDialog() {\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n      this.factoryConfirmDialogVisible = true;\r\n    },\r\n    submitFactoryConfirm() {\r\n      if (this.factoryConfirmForm.showDropdown == true) {\r\n        if (this.factoryConfirmForm.extraOption == null || this.factoryConfirmForm.extraOption == '') {\r\n          this.$message.error('请选择额外选项');\r\n          return;\r\n        }\r\n      }\r\n\r\n      let submitData = {};\r\n      if (this.taskInfoForm.isDirectSupply == 3) {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 3,\r\n            planNo: this.taskInfoForm.planNo,\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      } else {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            planNo: this.taskInfoForm.planNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 0, // 默认不是直供\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            directSupplyTaskNo: this.factoryConfirmForm.extraOption,\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      }\r\n\r\n\r\n\r\n      let directSupplyTask = {\r\n        //taskNo后台雪花生成\r\n        applyNo: this.factoryConfirmForm.extraOption,\r\n        taskType: 3,\r\n        taskStatus: 7,\r\n        secGross: this.taskInfoForm.secGross,\r\n        secGrossTime: this.taskInfoForm.secGrossTime,\r\n        planNo: this.taskInfoForm.planNo,\r\n        driverName: this.taskInfoForm.driverName,\r\n        sex: this.taskInfoForm.sex,\r\n        mobilePhone: this.taskInfoForm.mobilePhone,\r\n        idCardNo: this.taskInfoForm.idCardNo,\r\n        carNum: this.taskInfoForm.carNum,\r\n        vehicleEmissionStandards: this.taskInfoForm.vehicleEmissionStandards,\r\n        faceImg: this.taskInfoForm.faceImg,\r\n        drivingLicenseImg: this.taskInfoForm.drivingLicenseImg,\r\n        driverLicenseImg: this.taskInfoForm.driverLicenseImg,\r\n        companyName: this.taskInfoForm.companyName,\r\n        isDirectSupply: 3\r\n      };\r\n\r\n      let directSupplyTaskMaterialList = this.taskMaterials;\r\n\r\n      if (this.factoryConfirmForm.showDropdown == true && this.factoryConfirmForm.extraOption != null && this.factoryConfirmForm.extraOption != '') {\r\n        submitData.leaveTask.isDirectSupply = 1; // 设置为直供\r\n        submitData.directSupplyTask = directSupplyTask;\r\n        submitData.directSupplyTaskMaterialList = directSupplyTaskMaterialList;\r\n      }\r\n\r\n      handleUnload(submitData).then(res => {\r\n        console.log(\"handleUnload\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认入库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认入库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    submitStockOutConfirm() {\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有确认出库权限');\r\n        return;\r\n      }\r\n      // 构建提交数据\r\n      let submitData = {\r\n        leaveTask: {\r\n          //todo 计量系统补充信息待完善\r\n          id: this.dispatchId,\r\n          taskNo: this.taskNo,\r\n          applyNo: this.applyNo,\r\n          planNo: this.taskInfoForm.planNo,\r\n          // 出库信息\r\n          stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n          stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n          stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n          stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n          stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n          stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n          stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n          stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n\r\n          // 更改任务状态: 9\r\n          taskStatus: 3,\r\n          carNum: this.taskInfoForm.carNum,\r\n        },\r\n        leavePlan: this.planForm,\r\n        leaveTaskMaterial: this.taskMaterials[0],\r\n      };\r\n\r\n      handleStockOut(submitData).then(res => {\r\n        console.log(\"handleStockOut\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认出库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认出库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    handleFactoryConfirm() {\r\n      if (this.editFactoryStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n\r\n      //todo\r\n      //生成派车日志\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '分厂确认数量';\r\n\r\n\r\n      let factoryTaskInfo = {}\r\n      //todo 出入场\r\n      factoryTaskInfo.id = this.taskInfoForm.id\r\n      factoryTaskInfo.unloadingWorkNo = '卸货人占位符'\r\n      factoryTaskInfo.unloadingTime = new Date()\r\n      factoryTaskInfo.taskStatus = 9\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = factoryTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '分厂确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n\r\n    handleDoorManConfirm() {\r\n      if (this.editDoorManStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n\r\n\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      // this.taskMaterials.map(item => {\r\n      //   editTaskmaterials(item);\r\n      // })\r\n      //todo\r\n      // let leaveTaskLog = {};\r\n      // leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n      // addLeaveLog(leaveTaskLog);\r\n      // this.getTaskLogList(this.taskNo);\r\n\r\n      // let doorManTaskInfo = {}\r\n      // doorManTaskInfo.id = this.taskInfoForm.id\r\n      // if (this.taskInfoForm.taskType == 1) {\r\n      //   doorManTaskInfo.taskStatus = 9\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n      //   doorManTaskInfo.taskStatus = 5\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // }\r\n      // updateTask(doorManTaskInfo);\r\n      // this.$message.success('门卫确认成功');\r\n\r\n      // setTimeout(() => {\r\n      //   this.getTaskInfo();\r\n      // }, 500)\r\n\r\n    },\r\n\r\n    handleDoorManMeasureConfirm() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      if (this.taskInfoForm.taskStatus == 4) {\r\n        leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      } else {\r\n        leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      }\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n      //todo\r\n\r\n    },\r\n    // 生成二维码\r\n    creatQrCode() {\r\n      if (this.taskInfoForm.qrCodeContent) {\r\n        this.$refs.qrCode.innerHTML = \"\";\r\n        var YSqrCode = new QRCode(this.$refs.qrCode, {\r\n          text: this.taskInfoForm.qrCodeContent, // 需要转换为二维码的内容\r\n          width: 120,\r\n          height: 120,\r\n          colorDark: \"#000000\",\r\n          colorLight: \"#ffffff\",\r\n          correctLevel: QRCode.CorrectLevel.H,\r\n        });\r\n      }\r\n    },\r\n    getTaskLogList(taskNo) {\r\n      let taskLog = {};\r\n      taskLog.taskNo = taskNo\r\n      getTaskLogs(taskLog).then(response => {\r\n        console.log(\"getTaskLogs\", response);\r\n        // this.taskLogs = response.rows;\r\n        let logs = response.rows || [];\r\n        // 找出包含\"任务完成\"的日志\r\n        const finishedLogs = logs.filter(log => log.info && log.info.includes('任务完成'));\r\n        const otherLogs = logs.filter(log => !(log.info && log.info.includes('任务完成')));\r\n        // 先放\"任务完成\"，再放其他\r\n        this.taskLogs = [...finishedLogs, ...otherLogs];\r\n      })\r\n\r\n    },\r\n    async getTaskmaterialList(taskNo) {\r\n      try {\r\n        console.log(\"getTaskmaterialList\");\r\n        let leaveMaterial = {};\r\n        leaveMaterial.taskNo = taskNo;\r\n        const response = await getTaskmaterials(leaveMaterial);\r\n        this.taskMaterials = response.rows;\r\n        // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n        this.taskMaterials.forEach(item => {\r\n          item.doormanReceiveNum = item.planNum;\r\n          console.log(\"item.planType\", this.planForm.planType);\r\n          if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n            item.doormanReceiveNumIn = item.planNum;\r\n          }\r\n        });\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskmaterialList error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    editDoorManRow(row) {\r\n      row._backup = JSON.parse(JSON.stringify(row));//深拷贝\r\n      this.editingRow = row;\r\n      this.editDoorManStatus = true;\r\n      console.log(\"this.editDoorManRow\", row);\r\n    },\r\n    editFactoryRow() {\r\n      this.backupMaterials = JSON.parse(JSON.stringify(this.taskMaterials));//深拷贝\r\n      this.editFactoryStatus = true;\r\n    },\r\n    cancelDoorManEdit(row) {\r\n      //深拷贝\r\n      if (row._backup) {\r\n        // 恢复备份数据\r\n        Object.assign(row, row._backup);\r\n        delete row._backup; // 删除备份数据\r\n      };\r\n      this.editingRow = null; // 清空当前编辑行\r\n      this.editDoorManStatus = false;\r\n    },\r\n    cancelFactoryEdit() {\r\n      this.taskMaterials = JSON.parse(JSON.stringify(this.backupMaterials));//深拷贝\r\n      console.log(\"this.taskMaterials\", this.taskMaterials);\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    saveDoorManRowIn() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNumIn是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNumIn !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫入厂确认数量(${item.doormanReceiveNumIn})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id;\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n    saveDoorManRow() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNum是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫确认数量(${item.doormanReceiveNum})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n\r\n    saveFactoryRow() {\r\n\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    resetTaskInfoForm() {\r\n      this.taskInfoForm = {};\r\n    },\r\n\r\n    async getTaskInfo() {\r\n      try {\r\n        const response = await getTask(this.dispatchId);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async getTaskInfoByTaskNo() {\r\n      try {\r\n        const response = await getTaskByTaskNo(this.taskNo);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n\r\n        // 从返回的数据中获取所需的参数\r\n        this.dispatchId = this.taskInfoForm.id;\r\n        this.applyNo = this.taskInfoForm.applyNo;\r\n\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfoByTaskNo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    //计划状态\r\n    getPlanStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期',\r\n        '待分厂审批': '待分厂审批',\r\n        '待分厂复审': '待分厂复审',\r\n        '待生产指挥中心审批': '待生产指挥中心审批',\r\n        '审批完成': '审批完成',\r\n        '已出厂': '已出厂',\r\n        '部分收货': '部分收货',\r\n        '已完成': '已完成',\r\n        '驳回': '驳回',\r\n        '废弃': '废弃',\r\n        '过期': '过期',\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n    // 获取排放标准文本\r\n    getEmissionStandardsText(standard) {\r\n      const standardMap = {\r\n        1: '国五',\r\n        2: '国六',\r\n        3: '新能源'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取排放标准标签类型\r\n    getEmissionStandardsTagType(standard) {\r\n      const typeMap = {\r\n        1: 'warning',  // 国五\r\n        2: 'success',  // 国六\r\n        3: 'primary'   // 新能源\r\n      };\r\n      return typeMap[standard] || 'info';\r\n    },\r\n\r\n    // 获取物资状态文本\r\n    getMaterialStatusText(status) {\r\n      const statusMap = {\r\n        1: '待装载',\r\n        2: '已装载',\r\n        3: '已签收',\r\n        4: '异常'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取物资状态标签类型\r\n    getMaterialStatusType(status) {\r\n      const typeMap = {\r\n        1: 'info',     // 待装载\r\n        2: 'warning',  // 已装载\r\n        3: 'success',  // 已签收\r\n        4: 'danger'    // 异常\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#E6A23C', // 更新\r\n        3: '#67C23A', // 完成\r\n        4: '#F56C6C', // 异常\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.type] || '#409EFF';\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    // 获取任务详情数据\r\n    getTaskDetail(dispatchId) {\r\n      // 实际项目中这里需要调用API获取数据\r\n      // getDispatchTaskDetail(dispatchId).then(response => {\r\n      //   const { driverInfo, carInfo, taskMaterials, taskLogs } = response.data;\r\n      //   this.driverInfo = driverInfo;\r\n      //   this.carInfo = carInfo;\r\n      //   this.taskMaterials = taskMaterials;\r\n      //   this.taskLogs = taskLogs;\r\n      // });\r\n    },\r\n    handleShowDropdownChange(val) {\r\n      if (!val) {\r\n        this.factoryConfirmForm.extraOption = '';\r\n      }\r\n    },\r\n    openOptionDialog() {\r\n      this.optionDialogVisible = true;\r\n      this.loadOptions();\r\n      // 重置选中状态\r\n      this.selectedOption = null;\r\n      this.$nextTick(() => {\r\n        if (this.$refs.optionTable) {\r\n          this.$refs.optionTable.clearSelection();\r\n        }\r\n      });\r\n    },\r\n    handleOptionSelection(selection) {\r\n      // 只保留最后选中的一项\r\n      if (selection.length > 1) {\r\n        const lastSelected = selection[selection.length - 1];\r\n        this.$refs.optionTable.clearSelection();\r\n        this.$refs.optionTable.toggleRowSelection(lastSelected, true);\r\n        this.selectedOption = lastSelected;\r\n      } else {\r\n        this.selectedOption = selection[0];\r\n      }\r\n    },\r\n    confirmOptionSelection() {\r\n      if (!this.selectedOption) {\r\n        this.$message.warning('请选择一个选项');\r\n        return;\r\n      }\r\n\r\n      this.factoryConfirmForm.extraOption = this.selectedOption.applyNo;\r\n\r\n      // let dispatchInfo = {};\r\n      // dispatchInfo.carNum = this.taskInfoForm.carNum;\r\n      // dispatchInfo.isDirectSupply = 1;\r\n\r\n      // isAllowDispatch(dispatchInfo).then(response => {\r\n      //   let row = response.data;\r\n      //   if (row > 0) {\r\n      //     this.$message.error(\"当前车有正在执行的任务\")\r\n      //     return;\r\n      //   } else {\r\n      //     this.optionDialogVisible = false;\r\n      //     this.$message.success('选项已确认');\r\n      //   }\r\n      //   console.log(\"this.isAllowDispatch\", response);\r\n      // }).catch(err => {\r\n      //   console.error('dispatch error:', err);\r\n      //   this.$message.error('网络异常，稍后重试');\r\n      // });\r\n\r\n      this.optionDialogVisible = false;\r\n      this.$message.success('选项已确认');\r\n\r\n\r\n\r\n    },\r\n    loadOptions() {\r\n      // 这里应该调用API获取leave_plan表的数据\r\n      this.optionList = this.directSupplyPlanList; // 使用直供计划列表作为选项数据\\\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n      console.log(\"optionList\", this.optionList)\r\n    },\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用（出厂不返回）',\r\n        11: '通用（出厂返回）',\r\n        12: '委外加工（出厂返回）',\r\n        21: '有计划量计量（跨区调拨）',\r\n        22: '短期（跨区调拨）',\r\n        23: '钢板（圆钢）（跨区调拨）',\r\n        31: '通用（退货申请）'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n    searchOptions() {\r\n      // 取出并转小写\r\n      const searchPlanNo = (this.searchForm.planNo || '').toLowerCase();\r\n      const searchApplyNo = (this.searchForm.applyNo || '').toLowerCase();\r\n      const searchReceiveCompany = (this.searchForm.receiveCompany || '').toLowerCase();\r\n\r\n      // 过滤\r\n      this.optionList = this.directSupplyPlanList.filter(item => {\r\n        const planNo = (item.planNo || '').toString().toLowerCase();\r\n        const applyNo = (item.applyNo || '').toString().toLowerCase();\r\n        const receiveCompany = (item.receiveCompany || '').toString().toLowerCase();\r\n\r\n        // 为空不作为条件\r\n        const matchPlanNo = !searchPlanNo || planNo.includes(searchPlanNo);\r\n        const matchApplyNo = !searchApplyNo || applyNo.includes(searchApplyNo);\r\n        const matchReceiveCompany = !searchReceiveCompany || receiveCompany.includes(searchReceiveCompany);\r\n\r\n        return matchPlanNo && matchApplyNo && matchReceiveCompany;\r\n      });\r\n\r\n      // 更新状态显示\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n    },\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      };\r\n      this.loadOptions(); // 重新加载所有数据\r\n    },\r\n    getTaskTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return typeMap[type] || '未知';\r\n    },\r\n    // // 判断行是否可选\r\n    // isSelectable(row) {\r\n    //   // 当门卫确认数量不为0时，该行可选\r\n    //   return row.doormanReceiveNum > 0 && this.taskInfoForm.taskStatus !== 9;\r\n    // },\r\n\r\n    // 表格选择变化时的处理函数\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    // 处理非计量分厂确认\r\n    handleNonMeasureFactoryConfirm() {\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n      let isHandled = false;\r\n      this.selectedRows.forEach(item => {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning('门卫确认数量和计划数量不一致，请检查');\r\n          isHandled = true;\r\n        }\r\n      });\r\n\r\n      if (isHandled) {\r\n        return;\r\n      }\r\n\r\n      // if (this.selectedRows.length === 0) {\r\n      //   this.$message.warning('请选择需要确认的物资');\r\n      //   return;\r\n      // }\r\n\r\n      // 生成派车日志\r\n      let leaveTaskLog = {\r\n        logType: 2,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        info: '分厂接收确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ')\r\n      };\r\n\r\n      // 构建任务信息\r\n      let factoryTaskInfo = {\r\n        id: this.taskInfoForm.id,\r\n        unloadingWorkNo: '卸货人占位符',//后端updateLeaveTask方法\r\n        unloadingTime: new Date(),\r\n        taskStatus: 9\r\n      };\r\n\r\n      this.selectedRows.forEach(item => {\r\n        // 设置非计量分厂确认数量\r\n        item.factoryReceiveNum = item.doormanReceiveNum;\r\n      });\r\n\r\n      // 构建请求参数\r\n      let param = {\r\n        taskMaterialList: this.selectedRows, // 使用选中的行数据\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: factoryTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      // 发送请求\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        if (res.code == 200) {\r\n          this.$message.success('非计量分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n          // 清空选中状态\r\n          this.selectedRows = [];\r\n        } else {\r\n          this.$message.error(res.message || '非计量分厂确认失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleNonMeasureFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    openNewTaskWindow() {\r\n      console.log(\"openNewTaskWindow\", this.directSupplyParams);\r\n      let dispatchId = this.directSupplyParams.dispatchId;\r\n      let applyNo = BigInt(this.directSupplyParams.applyNo);\r\n      let measureFlag = this.directSupplyParams.measureFlag;\r\n      let planType = this.directSupplyParams.planType;\r\n      let taskNo = BigInt(this.directSupplyParams.taskNo);\r\n      const url = `http://localhost/leave/plan/task?dispatchId=${dispatchId}&applyNo=${applyNo}&measureFlag=${measureFlag}&planType=${planType}&taskNo=${taskNo}`;\r\n      window.open(url, '_blank');\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.btn-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode {\r\n  margin: 0 auto;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 通行证二维码模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 司机信息模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 车辆信息模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 物资列表模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.driver-photos {\r\n  padding: 0 20px 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.photo-item {\r\n  width: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.photo-item h4 {\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.photo-container {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.photo-container img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  object-fit: contain;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 25px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n</style>\r\n"]}]}