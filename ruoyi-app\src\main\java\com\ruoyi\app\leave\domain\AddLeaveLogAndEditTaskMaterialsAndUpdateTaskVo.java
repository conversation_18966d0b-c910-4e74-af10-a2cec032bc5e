package com.ruoyi.app.leave.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

@Data
public class AddLeaveLogAndEditTaskMaterialsAndUpdateTaskVo extends BaseEntity {

    private List<LeaveTaskMaterial> taskMaterialList;

    private LeaveLog leaveLog;

    private LeaveTask leaveTask;

    private Integer measureFlag;

    /**
     * 客户端IP地址（用于识别门号）
     */
    private String clientIp;
}
