{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\task.vue", "mtime": 1756371522889}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRUYXNrLCBnZXRUYXNrQnlUYXNrTm8sIGdldFRhc2ttYXRlcmlhbHMsIGdldFByb2Nlc3NMaXN0LCBnZXREaXJlY3RTdXBwbHlQbGFucywgZ2V0RGlyZWN0U3VwcGx5UGxhbkFuZFRhc2tEZXRhaWwsIGhhbmRsZVVubG9hZCwgaGFuZGxlU3RvY2tPdXQsIGlzQWxsb3dEaXNwYXRjaCwgZ2V0UGxhbk1hdGVyaWFscywgZWRpdFRhc2ttYXRlcmlhbHMsIGdldFRhc2tMb2dzLCBhZGRMZWF2ZUxvZywgdXBkYXRlVGFzaywgYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2sgfSBmcm9tICJAL2FwaS9sZWF2ZS90YXNrIjsNCmltcG9ydCB7IGRldGFpbFBsYW4gfSBmcm9tICJAL2FwaS9sZWF2ZS9wbGFuIjsNCmltcG9ydCB7IGxpc3RYY3RnRHJpdmVyQ2FyLCBnZXRYY3RnRHJpdmVyQ2FyLCBkZWxYY3RnRHJpdmVyQ2FyLCBhZGRYY3RnRHJpdmVyQ2FyLCB1cGRhdGVYY3RnRHJpdmVyQ2FyLCBleHBvcnRYY3RnRHJpdmVyQ2FyIH0gZnJvbSAiQC9hcGkvdHJ1Y2svY29tbW9uL3hjdGdEcml2ZXJDYXIiOw0KaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gImVsZW1lbnQtdWkiOw0KaW1wb3J0IFFSQ29kZSBmcm9tICJxcmNvZGVqczIiOw0KaW1wb3J0IHsgcmVhZEdhdGVMb2NhdGlvbkZpbGVTaWxlbnQgfSBmcm9tICJAL3V0aWxzL2ZpbGVSZWFkZXIiOw0KDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRpc3BhdGNoVGFza0RldGFpbCIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGZhY3RvcnlDb25maXJtRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBmYWN0b3J5Q29uZmlybUZvcm06IHsNCiAgICAgICAgY29tcGFueU5hbWU6ICcnLA0KICAgICAgICB0YXNrTm86ICcnLA0KICAgICAgICBhcHBseU5vOiAnJywNCiAgICAgICAgcGxhbk5vOiAnJywNCiAgICAgICAgdGFza1R5cGU6IG51bGwsDQogICAgICAgIHVubG9hZGluZ1dvcmtObzogJycsDQogICAgICAgIHVubG9hZGluZ1RpbWU6IG51bGwsDQogICAgICAgIHNwZWMxTGVuZ3RoOiBudWxsLA0KICAgICAgICBzcGVjMldpZHRoOiBudWxsLA0KICAgICAgICB0b3RhbHM6ICcnLA0KICAgICAgICB0b3RhbDogJycsDQogICAgICAgIHRvdGFsVW5pdDogJycsDQogICAgICAgIHByb2Nlc3NUeXBlOiAnJywNCiAgICAgICAgaGVhdE5vOiAnJywNCiAgICAgICAgc3RlZWxHcmFkZTogJycsDQogICAgICAgIGF4bGVzOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJywNCiAgICAgICAgdGFza1N0YXR1czogOSwgLy8g5a6M5oiQ54q25oCBDQogICAgICAgIGNhck51bTogJycsIC8vIOi9pueJjOWPtw0KICAgICAgICAvLyDlh7rlupPkv6Hmga8NCiAgICAgICAgc3RvY2tPdXRTcGVjMUxlbmd0aDogbnVsbCwNCiAgICAgICAgc3RvY2tPdXRTcGVjMldpZHRoOiBudWxsLA0KICAgICAgICBzdG9ja091dFRvdGFsczogJycsDQogICAgICAgIHN0b2NrT3V0VG90YWxVbml0OiAnJywNCiAgICAgICAgc3RvY2tPdXRUb3RhbDogJycsDQogICAgICAgIHN0b2NrT3V0UHJvY2Vzc1R5cGU6ICcnLA0KICAgICAgICBzdG9ja091dEhlYXRObzogJycsDQogICAgICAgIHN0b2NrT3V0U3RlZWxHcmFkZTogJycsDQogICAgICAgIHN0b2NrT3V0QXhsZXM6ICcnLA0KICAgICAgICBzdG9ja091dFJlbWFyazogJycsDQogICAgICAgIGhhbmRsZWRNYXRlcmlhbE5hbWU6ICcnLA0KICAgICAgICBzb3VyY2VDb21wYW55OiAnJywNCiAgICAgICAgcmVjZWl2ZUNvbXBhbnk6ICcnLA0KICAgICAgICBzaG93RHJvcGRvd246IGZhbHNlLA0KICAgICAgICBleHRyYU9wdGlvbjogJycsDQogICAgICAgIGRlZHVjdFdlaWdodDogbnVsbCwgLy8g5re75Yqg5omj6YeN5a2X5q61DQogICAgICB9LA0KICAgICAgb3B0aW9uRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBzZWFyY2hGb3JtOiB7DQogICAgICAgIHBsYW5ObzogJycsDQogICAgICAgIGFwcGx5Tm86ICcnLA0KICAgICAgICByZWNlaXZlQ29tcGFueTogJycNCiAgICAgIH0sDQogICAgICBvcHRpb25MaXN0OiBbXSwNCiAgICAgIGVkaXREb29yTWFuU3RhdHVzOiBmYWxzZSwNCiAgICAgIGVkaXRGYWN0b3J5U3RhdHVzOiBmYWxzZSwNCiAgICAgIC8vIOWPuOacuuS/oeaBrw0KICAgICAgZHJpdmVySW5mbzogew0KICAgICAgICBpZDogMSwNCiAgICAgICAgbmFtZTogJ+eOi+Wwj+aYjicsDQogICAgICAgIGlkQ2FyZDogJzExMDEwMTE5OTAwMTAxMDAwMScsDQogICAgICAgIHBob25lOiAnMTM4MDAxMzgwMDAnLA0KICAgICAgICBnZW5kZXI6ICcxJywNCiAgICAgICAgY29tcGFueTogJ+WMl+S6rOi/kOi+k+aciemZkOWFrOWPuCcsDQogICAgICAgIHBob3RvOiAnaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzE1MCcsDQogICAgICAgIGRyaXZlckxpY2Vuc2VJbWdzOiAnaHR0cHM6Ly92aWEucGxhY2Vob2xkZXIuY29tLzMwMHgyMDAnLA0KICAgICAgICB2ZWhpY2xlTGljZW5zZUltZ3M6ICdodHRwczovL3ZpYS5wbGFjZWhvbGRlci5jb20vMzAweDIwMCcNCiAgICAgIH0sDQoNCiAgICAgIC8vIOi9pui+huS/oeaBrw0KICAgICAgY2FySW5mbzoge30sDQoNCiAgICAgIC8vIOS7u+WKoeeJqei1hOWIl+ihqA0KICAgICAgdGFza01hdGVyaWFsczogW10sDQoNCiAgICAgIC8vIOS7u+WKoeaXpeW/l+WIl+ihqA0KICAgICAgdGFza0xvZ3M6IFtdLA0KDQogICAgICAvLyDnlLPor7fnvJblj7cNCiAgICAgIGFwcGx5Tm86IG51bGwsDQoNCiAgICAgIGlzZG9vck1hbjogZmFsc2UsDQoNCiAgICAgIC8vIOa0vui9puS7u+WKoUlEDQogICAgICBkaXNwYXRjaElkOiBudWxsLA0KDQogICAgICB0YXNrSW5mb0Zvcm06IHt9LA0KDQogICAgICBtZWFzdXJlRmxhZzogbnVsbCwNCg0KICAgICAgYmFja3VwVGFza01hdGVyaWFsczogbnVsbCwNCiAgICAgIHRhc2tObzogbnVsbCwNCg0KICAgICAgc2VsZWN0ZWRPcHRpb246IG51bGwsDQoNCiAgICAgIHBsYW5Gb3JtOiB7fSwNCg0KICAgICAgcHJvY2Vzc1R5cGVPcHRpb25zOiBbXSwgLy8g5Yqo5oCB5Yqg6L2955qE5Yqg5bel57G75Z6L6YCJ6aG5DQoNCiAgICAgIGZpbHRlcmVkUHJvY2Vzc1R5cGVPcHRpb25zOiBbXSwgLy8g6L+H5ruk5ZCO55qE5Yqg5bel57G75Z6L6YCJ6aG5DQoNCiAgICAgIHNlYXJjaFByb2Nlc3NUeXBlUXVlcnk6ICcnLC8vIOaQnOe0ouahhueahOWAvA0KDQogICAgICBkaXJlY3RTdXBwbHlQbGFuTGlzdDogW10sIC8vIOebtOS+m+iuoeWIkuWIl+ihqA0KDQogICAgICBlZGl0aW5nUm93OiBudWxsLA0KDQogICAgICBzZWxlY3RlZFJvd3M6IFtdLCAvLyDmt7vliqDpgInkuK3ooYzmlbDmja7mlbDnu4QNCg0KICAgICAgZGlyZWN0U3VwcGx5UGFyYW1zOiB7fQ0KICAgIH07DQogIH0sDQoNCiAgY29tcHV0ZWQ6IHsNCiAgICBkaXNwbGF5UHJvY2Vzc1R5cGVPcHRpb25zKCkgew0KICAgICAgcmV0dXJuIHRoaXMuc2VhcmNoUHJvY2Vzc1R5cGVRdWVyeSA/IHRoaXMuZmlsdGVyZWRQcm9jZXNzVHlwZU9wdGlvbnMgOiB0aGlzLnByb2Nlc3NUeXBlT3B0aW9uczsNCiAgICB9LA0KDQogICAgLy8g5piv5ZCm5pyJ6YCJ5Lit55qE6aG5DQogICAgaGFzU2VsZWN0ZWRJdGVtcygpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGggPiAwOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDorqHnrpflsZ7mgKcNCiAgICBtYXRlcmlhbE5hbWVzKCkgew0KICAgICAgcmV0dXJuIHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsTmFtZSkuam9pbignICcpOw0KICAgIH0sDQoNCiAgICBtYXRlcmlhbFNwZWNzKCkgew0KICAgICAgcmV0dXJuIHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsU3BlYykuam9pbignICcpOw0KICAgIH0NCiAgfSwNCg0KICBhY3RpdmF0ZWQoKSB7DQogICAgY29uc29sZS5sb2coImFjdGl2YXRlZOaJp+ihjCIpOw0KICAgIHRoaXMucmVzZXRUYXNrSW5mb0Zvcm0oKTsNCg0KICAgIC8vIOiOt+WPlui3r+eUseWPguaVsCAtIOaUr+aMgeS4pOenjeaWueW8j++8mnF1ZXJ55Y+C5pWw5ZKM6Lev5b6E5Y+C5pWwDQogICAgbGV0IHRhc2tObyA9IHRoaXMuJHJvdXRlLnBhcmFtcy50YXNrTm8gfHwgdGhpcy4kcm91dGUucXVlcnkudGFza05vOw0KDQogICAgaWYgKHRhc2tObykgew0KICAgICAgLy8g5paw55qE5pa55byP77ya6YCa6L+HdGFza05v6I635Y+W5omA5pyJ5Y+C5pWwDQogICAgICB0aGlzLnRhc2tObyA9IHRhc2tObzsNCiAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTm8iLCB0aGlzLnRhc2tObyk7DQogICAgICB0aGlzLnZhbGlkRG9vck1hbigpOw0KDQogICAgICAvLyDkvb/nlKggYXN5bmMvYXdhaXQg56Gu5L+d5oyJ6aG65bqP5omn6KGMDQogICAgICB0aGlzLmluaXRpYWxpemVEYXRhQnlUYXNrTm8oKTsNCiAgICB9IGVsc2Ugew0KICAgICAgLy8g5YW85a655pen55qE5pa55byP77ya5LuOcXVlcnnlj4LmlbDojrflj5YNCiAgICAgIGNvbnN0IHsgZGlzcGF0Y2hJZCwgYXBwbHlObywgbWVhc3VyZUZsYWcsIHBsYW5UeXBlLCB0YXNrTm86IHF1ZXJ5VGFza05vIH0gPSB0aGlzLiRyb3V0ZS5xdWVyeTsNCiAgICAgIHRoaXMuZGlzcGF0Y2hJZCA9IGRpc3BhdGNoSWQ7DQogICAgICB0aGlzLmFwcGx5Tm8gPSBhcHBseU5vOw0KICAgICAgdGhpcy5tZWFzdXJlRmxhZyA9IG1lYXN1cmVGbGFnOw0KICAgICAgY29uc29sZS5sb2coInRoaXMubWVhc3VyZUZsYWciLCB0aGlzLm1lYXN1cmVGbGFnKQ0KICAgICAgdGhpcy5wbGFuVHlwZSA9IHBsYW5UeXBlOw0KICAgICAgdGhpcy50YXNrTm8gPSBxdWVyeVRhc2tObzsNCiAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTm8iLCB0aGlzLnRhc2tObyk7DQogICAgICB0aGlzLnZhbGlkRG9vck1hbigpOw0KDQogICAgICAvLyDkvb/nlKggYXN5bmMvYXdhaXQg56Gu5L+d5oyJ6aG65bqP5omn6KGMDQogICAgICB0aGlzLmluaXRpYWxpemVEYXRhKCk7DQogICAgfQ0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICAvKioNCiAgICAgKiDosIPnlKjluKblpKfpl6jkvY3nva7kv6Hmga/nmoTmjqXlj6MNCiAgICAgKiBAcGFyYW0ge09iamVjdH0gcGFyYW0g5o6l5Y+j5Y+C5pWwDQogICAgICogQHJldHVybnMge1Byb21pc2V9IOaOpeWPo+iwg+eUqOe7k+aenA0KICAgICAqLw0KICAgIGFzeW5jIGNhbGxBcGlXaXRoR2F0ZUxvY2F0aW9uKHBhcmFtKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDlsJ3or5Xor7vlj5blpKfpl6jkvY3nva7mlofku7YNCiAgICAgICAgY29uc3QgZ2F0ZUxvY2F0aW9uRGF0YSA9IGF3YWl0IHJlYWRHYXRlTG9jYXRpb25GaWxlU2lsZW50KCk7DQoNCiAgICAgICAgLy8g5aaC5p6c5oiQ5Yqf6K+75Y+W5Yiw5aSn6Zeo5L2N572u5L+h5oGv77yM5re75Yqg5Yiw5Y+C5pWw5LitDQogICAgICAgIGlmIChnYXRlTG9jYXRpb25EYXRhKSB7DQogICAgICAgICAgcGFyYW0uZ2F0ZUxvY2F0aW9uSnNvbiA9IEpTT04uc3RyaW5naWZ5KGdhdGVMb2NhdGlvbkRhdGEpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCfmiJDlip/or7vlj5blpKfpl6jkvY3nva7mlofku7bvvIzlt7Lmt7vliqDliLDor7fmsYLlj4LmlbDkuK0nKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oJ+acquiDveivu+WPluWkp+mXqOS9jee9ruaWh+S7tu+8jOWwhuS9v+eUqOacjeWKoeWZqOm7mOiupOmFjee9ricpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6LCD55So5o6l5Y+jDQogICAgICAgIHJldHVybiBhd2FpdCBhZGRMZWF2ZUxvZ0FuZEVkaXRUYXNrTWF0ZXJpYWxzQW5kVXBkYXRlVGFzayhwYXJhbSk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfosIPnlKjmjqXlj6PlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aHJvdyBlcnJvcjsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgZ2V0RGlyZWN0U3VwcGx5UGxhbkFuZFRhc2soKSB7DQoNCg0KICAgICAgbGV0IGxlYXZlVGFzazAgPSB7DQogICAgICAgIHRhc2tObzogdGhpcy50YXNrSW5mb0Zvcm0uZGlyZWN0U3VwcGx5VGFza05vDQogICAgICB9DQoNCiAgICAgIGdldERpcmVjdFN1cHBseVBsYW5BbmRUYXNrRGV0YWlsKGxlYXZlVGFzazApLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImdldERpcmVjdFN1cHBseVBsYW5BbmRUYXNrRGV0YWlsIiwgcmVzKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5kaXJlY3RTdXBwbHlQYXJhbXMuZGlzcGF0Y2hJZCA9IHJlcy5yb3dzWzBdLmlkOw0KICAgICAgICAgIHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zLmFwcGx5Tm8gPSByZXMucm93c1swXS5hcHBseU5vOw0KICAgICAgICAgIHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zLnRhc2tObyA9IHJlcy5yb3dzWzBdLnRhc2tObzsNCiAgICAgICAgICB0aGlzLmRpcmVjdFN1cHBseVBhcmFtcy5tZWFzdXJlRmxhZyA9IHJlcy5yb3dzWzFdLm1lYXN1cmVGbGFnOw0KICAgICAgICAgIHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zLnBsYW5UeXBlID0gcmVzLnJvd3NbMV0ucGxhblR5cGU7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn6I635Y+W6K6h5YiS5YiX6KGo5aSx6LSlJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldERpcmVjdFN1cHBseVBsYW5BbmRUYXNrRGV0YWlsIGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgICB0aHJvdyBlcnI7DQogICAgICB9KTsNCg0KICAgIH0sDQoNCiAgICB2YWxpZERvb3JNYW4oKSB7DQogICAgICB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGlmIChpdGVtID09ICdsZWF2ZS5xdWFyZCcpIHsNCiAgICAgICAgICB0aGlzLmlzZG9vck1hbiA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgY29uc29sZS5sb2coImlzZG9vck1hbiIsIHRoaXMuaXNkb29yTWFuKQ0KICAgIH0sDQogICAgYXN5bmMgaW5pdGlhbGl6ZURhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDnrYnlvoXmiYDmnInlvILmraXmk43kvZzlrozmiJANCiAgICAgICAgYXdhaXQgdGhpcy5nZXRUYXNrSW5mbygpOw0KICAgICAgICBhd2FpdCB0aGlzLmdldFRhc2ttYXRlcmlhbExpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICBhd2FpdCB0aGlzLmdldFBsYW5JbmZvKHRoaXMuYXBwbHlObyk7DQoNCiAgICAgICAgLy8g5Zyo5omA5pyJ5pWw5o2u5Yqg6L295a6M5oiQ5ZCO5omn6KGMDQogICAgICAgIHRoaXMudXBsb2FkRmFjdG9yeUNvbmZpcm1Gb3JtKCk7DQoNCiAgICAgICAgLy8g5YW25LuW5Yid5aeL5YyW5pON5L2cDQogICAgICAgIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICB0aGlzLmdldFByb2Nlc3NUeXBlKCk7DQoNCiAgICAgICAgLy/mn6Xor6Lnm7Tkvpvlr7nlupTorqHliJLjgIHku7vliqHor6bmg4UNCiAgICAgICAgdGhpcy5nZXREaXJlY3RTdXBwbHlQbGFuQW5kVGFzaygpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW5pdGlhbGl6aW5nIGRhdGE6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlbDmja7liqDovb3lpLHotKXvvIzor7fliLfmlrDpobXpnaLph43or5UnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXN5bmMgaW5pdGlhbGl6ZURhdGFCeVRhc2tObygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOmAmui/h3Rhc2tOb+iOt+WPluS7u+WKoeS/oeaBrw0KICAgICAgICBhd2FpdCB0aGlzLmdldFRhc2tJbmZvQnlUYXNrTm8oKTsNCg0KICAgICAgICAvLyDpgJrov4dhcHBseU5v6I635Y+W6K6h5YiS5L+h5oGvDQogICAgICAgIGF3YWl0IHRoaXMuZ2V0UGxhbkluZm8odGhpcy5hcHBseU5vKTsNCg0KICAgICAgICAvLyDojrflj5bku7vliqHnianotYTliJfooagNCiAgICAgICAgYXdhaXQgdGhpcy5nZXRUYXNrbWF0ZXJpYWxMaXN0KHRoaXMudGFza05vKTsNCg0KICAgICAgICAvLyDlnKjmiYDmnInmlbDmja7liqDovb3lrozmiJDlkI7miafooYwNCiAgICAgICAgdGhpcy51cGxvYWRGYWN0b3J5Q29uZmlybUZvcm0oKTsNCg0KICAgICAgICAvLyDlhbbku5bliJ3lp4vljJbmk43kvZwNCiAgICAgICAgdGhpcy5nZXRUYXNrTG9nTGlzdCh0aGlzLnRhc2tObyk7DQogICAgICAgIHRoaXMuZ2V0UHJvY2Vzc1R5cGUoKTsNCg0KICAgICAgICAvL+afpeivouebtOS+m+WvueW6lOiuoeWIkuOAgeS7u+WKoeivpuaDhQ0KICAgICAgICB0aGlzLmdldERpcmVjdFN1cHBseVBsYW5BbmRUYXNrKCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbml0aWFsaXppbmcgZGF0YSBieSB0YXNrTm86JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlbDmja7liqDovb3lpLHotKXvvIzor7fliLfmlrDpobXpnaLph43or5UnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgdXBsb2FkRmFjdG9yeUNvbmZpcm1Gb3JtKCkgew0KICAgICAgLy8g6LWL5YC85ZCO77yM5Yid5aeL5YyW5q+P5Liq5YWD57Sg55qEIGRvb3JtYW5SZWNlaXZlTnVtIOWSjCBkb29ybWFuUmVjZWl2ZU51bUluDQogICAgICB0aGlzLnRhc2tNYXRlcmlhbHMuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5kb29ybWFuUmVjZWl2ZU51bSA9IGl0ZW0ucGxhbk51bTsNCiAgICAgICAgY29uc29sZS5sb2coIml0ZW0ucGxhblR5cGUiLCB0aGlzLnBsYW5Gb3JtLnBsYW5UeXBlKTsNCiAgICAgICAgaWYgKHRoaXMucGxhbkZvcm0ucGxhblR5cGUgPT0gMiB8fCB0aGlzLnBsYW5Gb3JtLnBsYW5UeXBlID09IDMpIHsNCiAgICAgICAgICBpdGVtLmRvb3JtYW5SZWNlaXZlTnVtSW4gPSBpdGVtLnBsYW5OdW07DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICBsZXQgaGFuZGxlZE1hdGVyaWFsTmFtZSA9IHRoaXMudGFza01hdGVyaWFscy5tYXAoaXRlbSA9PiBpdGVtLm1hdGVyaWFsTmFtZSkuam9pbignICcpOw0KICAgICAgbGV0IG1hdGVyaWFsU3BlY3MgPSB0aGlzLnRhc2tNYXRlcmlhbHMubWFwKGl0ZW0gPT4gaXRlbS5tYXRlcmlhbFNwZWMpLmpvaW4oJyAnKTsNCiAgICAgIC8vIOWIneWni+WMluihqOWNleaVsOaNrg0KICAgICAgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0gPSB7DQogICAgICAgIGNvbXBhbnlOYW1lOiB0aGlzLnRhc2tJbmZvRm9ybS5jb21wYW55TmFtZSwNCiAgICAgICAgZ3Jvc3M6IHRoaXMudGFza0luZm9Gb3JtLmdyb3NzLA0KICAgICAgICBzZWNHcm9zczogdGhpcy50YXNrSW5mb0Zvcm0uc2VjR3Jvc3MsDQogICAgICAgIGRyaXZlck5hbWU6IHRoaXMudGFza0luZm9Gb3JtLmRyaXZlck5hbWUsDQogICAgICAgIHRhcmU6IHRoaXMudGFza0luZm9Gb3JtLnRhcmUsDQogICAgICAgIHRhc2tObzogdGhpcy50YXNrTm8sDQogICAgICAgIGFwcGx5Tm86IHRoaXMuYXBwbHlObywNCiAgICAgICAgcGxhbk5vOiB0aGlzLnRhc2tJbmZvRm9ybS5wbGFuTm8sDQogICAgICAgIHVubG9hZGluZ1dvcmtObzogJycsDQogICAgICAgIHVubG9hZGluZ1RpbWU6IG5ldyBEYXRlKCksDQogICAgICAgIHNwZWMxTGVuZ3RoOiBudWxsLA0KICAgICAgICBzcGVjMldpZHRoOiBudWxsLA0KICAgICAgICB0b3RhbHM6ICcnLA0KICAgICAgICB0b3RhbDogJycsDQogICAgICAgIHRvdGFsVW5pdDogJycsDQogICAgICAgIHByb2Nlc3NUeXBlOiAnJywNCiAgICAgICAgaGVhdE5vOiAnJywNCiAgICAgICAgc3RlZWxHcmFkZTogJycsDQogICAgICAgIGF4bGVzOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJywNCiAgICAgICAgdGFza1N0YXR1czogOSwNCiAgICAgICAgY2FyTnVtOiB0aGlzLnRhc2tJbmZvRm9ybS5jYXJOdW0sIC8vIOWIneWni+WMlui9pueJjOWPtw0KICAgICAgICBoYW5kbGVkTWF0ZXJpYWxOYW1lOiBoYW5kbGVkTWF0ZXJpYWxOYW1lLA0KICAgICAgICBtYXRlcmlhbFNwZWNzOiBtYXRlcmlhbFNwZWNzLA0KICAgICAgICBzb3VyY2VDb21wYW55OiB0aGlzLnBsYW5Gb3JtLnNvdXJjZUNvbXBhbnksDQogICAgICAgIHJlY2VpdmVDb21wYW55OiB0aGlzLnBsYW5Gb3JtLnJlY2VpdmVDb21wYW55LA0KICAgICAgICBzaG93RHJvcGRvd246IGZhbHNlLCAvLyDmmK/lkKblkK/nlKjpop3lpJbpgInpobkNCiAgICAgICAgZXh0cmFPcHRpb246ICcnLCAvLyDpop3lpJbpgInpobnnmoTlgLwNCiAgICAgICAgLy8g5Ye65bqT5L+h5oGvDQogICAgICAgIHN0b2NrT3V0U3BlYzFMZW5ndGg6IG51bGwsDQogICAgICAgIHN0b2NrT3V0U3BlYzJXaWR0aDogbnVsbCwNCiAgICAgICAgc3RvY2tPdXRUb3RhbHM6ICcnLA0KICAgICAgICBzdG9ja091dFRvdGFsVW5pdDogJycsDQogICAgICAgIHN0b2NrT3V0VG90YWw6ICcnLA0KICAgICAgICBzdG9ja091dFByb2Nlc3NUeXBlOiAnJywNCiAgICAgICAgc3RvY2tPdXRIZWF0Tm86ICcnLA0KICAgICAgICBzdG9ja091dFN0ZWVsR3JhZGU6ICcnLA0KICAgICAgICBzdG9ja091dEF4bGVzOiAnJywNCiAgICAgICAgc3RvY2tPdXRSZW1hcms6ICcnLA0KICAgICAgICBkZWR1Y3RXZWlnaHQ6IG51bGwsIC8vIOa3u+WKoOaJo+mHjeWtl+auteWIneWni+WMlg0KICAgICAgfTsNCiAgICB9LA0KDQogICAgb3Blbk5ld1dpbmRvdygpIHsNCiAgICAgIGNvbnN0IG5ld1dpbmRvd1VybCA9ICdodHRwOi8vbG9jYWxob3N0L2xlYXZlL2xlYXZlUGxhbkxpc3QnOyAvLyDmm7/mjaLkuLrlrp7pmYXopoHot7PovaznmoTpobXpnaIgVVJMDQogICAgICB3aW5kb3cub3BlbihuZXdXaW5kb3dVcmwsICdfYmxhbmsnKTsgLy8g5omT5byA5paw56qX5Y+j5bm26Lez6L2s6Iez5oyH5a6aIFVSTA0KICAgIH0sDQogICAgLy/ojrflj5blj6/ku6Xnm7TkvpvnmoTorqHliJINCiAgICBhc3luYyBnZXREaXJlY3RTdXBwbHlMaXN0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgbGV0IGxlYXZlUGxhbiA9IHsNCiAgICAgICAgICBzb3VyY2VDb21wYW55OiB0aGlzLnBsYW5Gb3JtLnNvdXJjZUNvbXBhbnksDQogICAgICAgICAgcGxhblR5cGU6IDMsDQogICAgICAgIH0NCiAgICAgICAgY29uc29sZS5sb2coIuiOt+WPluWPr+S7peebtOS+m+eahOiuoeWIkiIsIGxlYXZlUGxhbikNCg0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBnZXREaXJlY3RTdXBwbHlQbGFucyhsZWF2ZVBsYW4pOw0KICAgICAgICBjb25zb2xlLmxvZygiZ2V0RGlyZWN0U3VwcGx5UGxhbnMiLCByZXMpDQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmRpcmVjdFN1cHBseVBsYW5MaXN0ID0gcmVzLnJvd3M7DQogICAgICAgICAgLy8gLy/mn6Xor6Lmr4/kuKrorqHliJLnmoTnianotYQNCiAgICAgICAgICAvLyBmb3IgKGNvbnN0IGl0ZW0gb2YgdGhpcy5kaXJlY3RTdXBwbHlQbGFuTGlzdCkgew0KICAgICAgICAgIC8vICAgY29uc29sZS5sb2coIml0ZW0iLCBpdGVtKQ0KICAgICAgICAgIC8vICAgbGV0IGxlYXZlUGxhbk1hdGVyaWFsID0gew0KICAgICAgICAgIC8vICAgICBhcHBseU5vOiBpdGVtLmFwcGx5Tm8NCiAgICAgICAgICAvLyAgIH07DQogICAgICAgICAgLy8gICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFBsYW5NYXRlcmlhbHMobGVhdmVQbGFuTWF0ZXJpYWwpOw0KICAgICAgICAgIC8vICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgLy8gICAgIGNvbnNvbGUubG9nKCJnZXRQbGFuTWF0ZXJpYWxzIiwgcmVzcG9uc2UpDQogICAgICAgICAgLy8gICAgIGl0ZW0ubWF0ZXJpYWxOYW1lID0gcmVzcG9uc2Uucm93c1swXS5tYXRlcmlhbE5hbWU7DQogICAgICAgICAgLy8gICAgIGl0ZW0ubWF0ZXJpYWxTcGVjID0gcmVzcG9uc2Uucm93c1swXS5tYXRlcmlhbFNwZWM7DQogICAgICAgICAgLy8gICB9IGVsc2Ugew0KICAgICAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+iOt+WPluiuoeWIkueJqei1hOWksei0pScpOw0KICAgICAgICAgIC8vICAgfQ0KICAgICAgICAgIC8vIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfojrflj5borqHliJLliJfooajlpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldERpcmVjdFN1cHBseVBsYW5zIGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgICB0aHJvdyBlcnI7DQogICAgICB9DQogICAgfSwNCiAgICBmaWx0ZXJQcm9jZXNzVHlwZShxdWVyeSkgew0KICAgICAgdGhpcy5zZWFyY2hQcm9jZXNzVHlwZVF1ZXJ5ID0gcXVlcnk7DQoNCiAgICAgIGlmICh0aGlzLnNlYXJjaFByb2Nlc3NUeXBlUXVlcnkpIHsNCiAgICAgICAgY29uc29sZS5sb2coInByb2Nlc3NUeXBlT3B0aW9ucyIsIHRoaXMucHJvY2Vzc1R5cGVPcHRpb25zKQ0KDQogICAgICAgIHRoaXMuZmlsdGVyZWRQcm9jZXNzVHlwZU9wdGlvbnMgPSB0aGlzLnByb2Nlc3NUeXBlT3B0aW9ucy5maWx0ZXIoaXRlbSA9Pg0KICAgICAgICAgIGl0ZW0udmFsdWUuaW5jbHVkZXMocXVlcnkpDQogICAgICAgICk7DQogICAgICB9IGVsc2Ugew0KDQogICAgICAgIHRoaXMuZmlsdGVyZWRQcm9jZXNzVHlwZU9wdGlvbnMgPSB0aGlzLnByb2Nlc3NUeXBlT3B0aW9uczsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldFByb2Nlc3NUeXBlKCkgew0KICAgICAgZ2V0UHJvY2Vzc0xpc3QoKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCJnZXRQcm9jZXNzTGlzdCIsIHJlcykNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMucHJvY2Vzc1R5cGVPcHRpb25zID0gcmVzLnJvd3MubWFwKGl0ZW0gPT4gKHsNCiAgICAgICAgICAgIHZhbHVlOiBpdGVtLnByb2Nlc3NuYW1lLA0KICAgICAgICAgICAgbGFiZWw6IGl0ZW0ucHJvY2Vzc25hbWUNCiAgICAgICAgICB9KSk7DQogICAgICAgICAgdGhpcy5maWx0ZXJlZFByb2Nlc3NUeXBlT3B0aW9ucyA9IHRoaXMucHJvY2Vzc1R5cGVPcHRpb25zOyAvLyDliJ3lp4vljJbov4fmu6TlkI7nmoTpgInpobkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfojrflj5bliqDlt6XnsbvlnovlpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignZ2V0UHJvY2Vzc0xpc3QgZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGFzeW5jIGdldFBsYW5JbmZvKGFwcGx5Tm8pIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGV0YWlsUGxhbihhcHBseU5vKTsNCiAgICAgICAgY29uc29sZS5sb2coImRldGFpbFBsYW4iLCByZXNwb25zZSk7DQogICAgICAgIHRoaXMucGxhbkZvcm0gPSByZXNwb25zZS5kYXRhOw0KDQogICAgICAgIC8vIOS7juiuoeWIkuS/oeaBr+S4reiOt+WPlnBsYW5UeXBl5ZKMbWVhc3VyZUZsYWcNCiAgICAgICAgdGhpcy5wbGFuVHlwZSA9IHRoaXMucGxhbkZvcm0ucGxhblR5cGU7DQogICAgICAgIHRoaXMubWVhc3VyZUZsYWcgPSB0aGlzLnBsYW5Gb3JtLm1lYXN1cmVGbGFnOw0KICAgICAgICBjb25zb2xlLmxvZygidGhpcy5wbGFuVHlwZSIsIHRoaXMucGxhblR5cGUpOw0KICAgICAgICBjb25zb2xlLmxvZygidGhpcy5tZWFzdXJlRmxhZyIsIHRoaXMubWVhc3VyZUZsYWcpOw0KDQogICAgICAgIGF3YWl0IHRoaXMuZ2V0RGlyZWN0U3VwcGx5TGlzdCgpOw0KICAgICAgICByZXR1cm4gcmVzcG9uc2U7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCdnZXRQbGFuSW5mbyBlcnJvcjonLCBlcnJvcik7DQogICAgICAgIHRocm93IGVycm9yOw0KICAgICAgfQ0KICAgIH0sDQogICAgb3BlbkZhY3RvcnlDb25maXJtRGlhbG9nKCkgew0KICAgICAgbGV0IGhhbmRsZWRNYXRlcmlhbE5hbWUgPSB0aGlzLnRhc2tNYXRlcmlhbHMubWFwKGl0ZW0gPT4gaXRlbS5tYXRlcmlhbE5hbWUpLmpvaW4oJyAnKTsNCiAgICAgIC8vIOWIneWni+WMluihqOWNleaVsOaNrg0KICAgICAgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0gPSB7DQogICAgICAgIGNvbXBhbnlOYW1lOiB0aGlzLnRhc2tJbmZvRm9ybS5jb21wYW55TmFtZSwNCiAgICAgICAgZ3Jvc3M6IHRoaXMudGFza0luZm9Gb3JtLmdyb3NzLA0KICAgICAgICBzZWNHcm9zczogdGhpcy50YXNrSW5mb0Zvcm0uc2VjR3Jvc3MsDQogICAgICAgIHRhcmU6IHRoaXMudGFza0luZm9Gb3JtLnRhcmUsDQogICAgICAgIHRhc2tObzogdGhpcy50YXNrTm8sDQogICAgICAgIGFwcGx5Tm86IHRoaXMuYXBwbHlObywNCiAgICAgICAgcGxhbk5vOiB0aGlzLnRhc2tJbmZvRm9ybS5wbGFuTm8sDQogICAgICAgIHVubG9hZGluZ1dvcmtObzogJycsDQogICAgICAgIHVubG9hZGluZ1RpbWU6IG5ldyBEYXRlKCksDQogICAgICAgIHNwZWMxTGVuZ3RoOiBudWxsLA0KICAgICAgICBzcGVjMldpZHRoOiBudWxsLA0KICAgICAgICB0b3RhbHM6ICcnLA0KICAgICAgICB0b3RhbDogJycsDQogICAgICAgIHRvdGFsVW5pdDogJycsDQogICAgICAgIHByb2Nlc3NUeXBlOiAnJywNCiAgICAgICAgaGVhdE5vOiAnJywNCiAgICAgICAgc3RlZWxHcmFkZTogJycsDQogICAgICAgIGF4bGVzOiAnJywNCiAgICAgICAgcmVtYXJrOiAnJywNCiAgICAgICAgdGFza1N0YXR1czogOSwNCiAgICAgICAgY2FyTnVtOiB0aGlzLnRhc2tJbmZvRm9ybS5jYXJOdW0sIC8vIOWIneWni+WMlui9pueJjOWPtw0KICAgICAgICBoYW5kbGVkTWF0ZXJpYWxOYW1lOiBoYW5kbGVkTWF0ZXJpYWxOYW1lLA0KICAgICAgICBzb3VyY2VDb21wYW55OiB0aGlzLnBsYW5Gb3JtLnNvdXJjZUNvbXBhbnksDQogICAgICAgIHJlY2VpdmVDb21wYW55OiB0aGlzLnBsYW5Gb3JtLnJlY2VpdmVDb21wYW55LA0KICAgICAgICBzaG93RHJvcGRvd246IGZhbHNlLCAvLyDmmK/lkKblkK/nlKjpop3lpJbpgInpobkNCiAgICAgICAgZXh0cmFPcHRpb246ICcnLCAvLyDpop3lpJbpgInpobnnmoTlgLwNCiAgICAgICAgLy8g5Ye65bqT5L+h5oGvDQogICAgICAgIHN0b2NrT3V0U3BlYzFMZW5ndGg6IG51bGwsDQogICAgICAgIHN0b2NrT3V0U3BlYzJXaWR0aDogbnVsbCwNCiAgICAgICAgc3RvY2tPdXRUb3RhbHM6ICcnLA0KICAgICAgICBzdG9ja091dFRvdGFsVW5pdDogJycsDQogICAgICAgIHN0b2NrT3V0VG90YWw6ICcnLA0KICAgICAgICBzdG9ja091dFByb2Nlc3NUeXBlOiAnJywNCiAgICAgICAgc3RvY2tPdXRIZWF0Tm86ICcnLA0KICAgICAgICBzdG9ja091dFN0ZWVsR3JhZGU6ICcnLA0KICAgICAgICBzdG9ja091dEF4bGVzOiAnJywNCiAgICAgICAgc3RvY2tPdXRSZW1hcms6ICcnLA0KICAgICAgICBkZWR1Y3RXZWlnaHQ6IG51bGwsIC8vIOa3u+WKoOaJo+mHjeWtl+auteWIneWni+WMlg0KICAgICAgfTsNCiAgICAgIHRoaXMuZmFjdG9yeUNvbmZpcm1EaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIHN1Ym1pdEZhY3RvcnlDb25maXJtKCkgew0KICAgICAgaWYgKHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnNob3dEcm9wZG93biA9PSB0cnVlKSB7DQogICAgICAgIGlmICh0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5leHRyYU9wdGlvbiA9PSBudWxsIHx8IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmV4dHJhT3B0aW9uID09ICcnKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36YCJ5oup6aKd5aSW6YCJ6aG5Jyk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGxldCBzdWJtaXREYXRhID0ge307DQogICAgICBpZiAodGhpcy50YXNrSW5mb0Zvcm0uaXNEaXJlY3RTdXBwbHkgPT0gMykgew0KICAgICAgICAvLyDmnoTlu7rmj5DkuqTmlbDmja4NCiAgICAgICAgc3VibWl0RGF0YSA9IHsNCiAgICAgICAgICBsZWF2ZVRhc2s6IHsNCiAgICAgICAgICAgIGlkOiB0aGlzLmRpc3BhdGNoSWQsDQogICAgICAgICAgICB0YXNrTm86IHRoaXMudGFza05vLA0KICAgICAgICAgICAgYXBwbHlObzogdGhpcy5hcHBseU5vLA0KICAgICAgICAgICAgLy/lhaXlupPkv6Hmga8NCiAgICAgICAgICAgIHNwZWMxTGVuZ3RoOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zcGVjMUxlbmd0aCwNCiAgICAgICAgICAgIHNwZWMyV2lkdGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnNwZWMyV2lkdGgsDQogICAgICAgICAgICB0b3RhbHM6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnRvdGFsICsgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0udG90YWxVbml0LA0KICAgICAgICAgICAgcHJvY2Vzc1R5cGU6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnByb2Nlc3NUeXBlLA0KICAgICAgICAgICAgaGVhdE5vOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5oZWF0Tm8sDQogICAgICAgICAgICBzdGVlbEdyYWRlOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdGVlbEdyYWRlLA0KICAgICAgICAgICAgYXhsZXM6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmF4bGVzLA0KICAgICAgICAgICAgcmVtYXJrOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5yZW1hcmssDQogICAgICAgICAgICBjYXJOdW06IHRoaXMudGFza0luZm9Gb3JtLmNhck51bSwNCiAgICAgICAgICAgIGRyaXZlck5hbWU6IHRoaXMudGFza0luZm9Gb3JtLmRyaXZlck5hbWUsDQogICAgICAgICAgICBpc0RpcmVjdFN1cHBseTogMywNCiAgICAgICAgICAgIHBsYW5ObzogdGhpcy50YXNrSW5mb0Zvcm0ucGxhbk5vLA0KICAgICAgICAgICAgZGVkdWN0V2VpZ2h0OiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5kZWR1Y3RXZWlnaHQsIC8vIOa3u+WKoOaJo+mHjeWtl+autQ0KDQogICAgICAgICAgICAvLyDlh7rlupPkv6Hmga8NCiAgICAgICAgICAgIHN0b2NrT3V0U3BlYzFMZW5ndGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0U3BlYzFMZW5ndGgsDQogICAgICAgICAgICBzdG9ja091dFNwZWMyV2lkdGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0U3BlYzJXaWR0aCwNCiAgICAgICAgICAgIHN0b2NrT3V0VG90YWxzOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFRvdGFsICsgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRUb3RhbFVuaXQsDQogICAgICAgICAgICBzdG9ja091dFByb2Nlc3NUeXBlOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFByb2Nlc3NUeXBlLA0KICAgICAgICAgICAgc3RvY2tPdXRIZWF0Tm86IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0SGVhdE5vLA0KICAgICAgICAgICAgc3RvY2tPdXRTdGVlbEdyYWRlOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFN0ZWVsR3JhZGUsDQogICAgICAgICAgICBzdG9ja091dEF4bGVzOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dEF4bGVzLA0KICAgICAgICAgICAgc3RvY2tPdXRSZW1hcms6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0UmVtYXJrLA0KICAgICAgICAgICAgLy8g5pu05pS55Lu75Yqh54q25oCBOiA5DQogICAgICAgICAgICAvLyB0b2RvIOS7u+WKoeeKtuaAgeWmguS9leWPmOWMlg0KICAgICAgICAgICAgdGFza1N0YXR1czogOCwNCiAgICAgICAgICAgIHRhc2tUeXBlOiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIGxlYXZlUGxhbjogdGhpcy5wbGFuRm9ybSwNCiAgICAgICAgICBsZWF2ZVRhc2tNYXRlcmlhbDogdGhpcy50YXNrTWF0ZXJpYWxzWzBdLA0KICAgICAgICB9Ow0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5p6E5bu65o+Q5Lqk5pWw5o2uDQogICAgICAgIHN1Ym1pdERhdGEgPSB7DQogICAgICAgICAgbGVhdmVUYXNrOiB7DQogICAgICAgICAgICBpZDogdGhpcy5kaXNwYXRjaElkLA0KICAgICAgICAgICAgdGFza05vOiB0aGlzLnRhc2tObywNCiAgICAgICAgICAgIGFwcGx5Tm86IHRoaXMuYXBwbHlObywNCiAgICAgICAgICAgIHBsYW5ObzogdGhpcy50YXNrSW5mb0Zvcm0ucGxhbk5vLA0KICAgICAgICAgICAgLy/lhaXlupPkv6Hmga8NCiAgICAgICAgICAgIHNwZWMxTGVuZ3RoOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zcGVjMUxlbmd0aCwNCiAgICAgICAgICAgIHNwZWMyV2lkdGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnNwZWMyV2lkdGgsDQogICAgICAgICAgICB0b3RhbHM6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnRvdGFsICsgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0udG90YWxVbml0LA0KICAgICAgICAgICAgcHJvY2Vzc1R5cGU6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnByb2Nlc3NUeXBlLA0KICAgICAgICAgICAgaGVhdE5vOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5oZWF0Tm8sDQogICAgICAgICAgICBzdGVlbEdyYWRlOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdGVlbEdyYWRlLA0KICAgICAgICAgICAgYXhsZXM6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmF4bGVzLA0KICAgICAgICAgICAgcmVtYXJrOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5yZW1hcmssDQogICAgICAgICAgICBjYXJOdW06IHRoaXMudGFza0luZm9Gb3JtLmNhck51bSwNCiAgICAgICAgICAgIGRyaXZlck5hbWU6IHRoaXMudGFza0luZm9Gb3JtLmRyaXZlck5hbWUsDQogICAgICAgICAgICBpc0RpcmVjdFN1cHBseTogMCwgLy8g6buY6K6k5LiN5piv55u05L6bDQogICAgICAgICAgICBkZWR1Y3RXZWlnaHQ6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmRlZHVjdFdlaWdodCwgLy8g5re75Yqg5omj6YeN5a2X5q61DQogICAgICAgICAgICBkaXJlY3RTdXBwbHlUYXNrTm86IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmV4dHJhT3B0aW9uLA0KICAgICAgICAgICAgLy8g5Ye65bqT5L+h5oGvDQogICAgICAgICAgICBzdG9ja091dFNwZWMxTGVuZ3RoOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFNwZWMxTGVuZ3RoLA0KICAgICAgICAgICAgc3RvY2tPdXRTcGVjMldpZHRoOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFNwZWMyV2lkdGgsDQogICAgICAgICAgICBzdG9ja091dFRvdGFsczogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRUb3RhbCArIHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0VG90YWxVbml0LA0KICAgICAgICAgICAgc3RvY2tPdXRQcm9jZXNzVHlwZTogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRQcm9jZXNzVHlwZSwNCiAgICAgICAgICAgIHN0b2NrT3V0SGVhdE5vOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dEhlYXRObywNCiAgICAgICAgICAgIHN0b2NrT3V0U3RlZWxHcmFkZTogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRTdGVlbEdyYWRlLA0KICAgICAgICAgICAgc3RvY2tPdXRBeGxlczogdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uc3RvY2tPdXRBeGxlcywNCiAgICAgICAgICAgIHN0b2NrT3V0UmVtYXJrOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFJlbWFyaywNCiAgICAgICAgICAgIC8vIOabtOaUueS7u+WKoeeKtuaAgTogOQ0KICAgICAgICAgICAgLy8gdG9kbyDku7vliqHnirbmgIHlpoLkvZXlj5jljJYNCiAgICAgICAgICAgIHRhc2tTdGF0dXM6IDgsDQogICAgICAgICAgICB0YXNrVHlwZTogdGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUsDQogICAgICAgICAgfSwNCiAgICAgICAgICBsZWF2ZVBsYW46IHRoaXMucGxhbkZvcm0sDQogICAgICAgICAgbGVhdmVUYXNrTWF0ZXJpYWw6IHRoaXMudGFza01hdGVyaWFsc1swXSwNCiAgICAgICAgfTsNCiAgICAgIH0NCg0KDQoNCiAgICAgIGxldCBkaXJlY3RTdXBwbHlUYXNrID0gew0KICAgICAgICAvL3Rhc2tOb+WQjuWPsOmbquiKseeUn+aIkA0KICAgICAgICBhcHBseU5vOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5leHRyYU9wdGlvbiwNCiAgICAgICAgdGFza1R5cGU6IDMsDQogICAgICAgIHRhc2tTdGF0dXM6IDcsDQogICAgICAgIHNlY0dyb3NzOiB0aGlzLnRhc2tJbmZvRm9ybS5zZWNHcm9zcywNCiAgICAgICAgc2VjR3Jvc3NUaW1lOiB0aGlzLnRhc2tJbmZvRm9ybS5zZWNHcm9zc1RpbWUsDQogICAgICAgIHBsYW5ObzogdGhpcy50YXNrSW5mb0Zvcm0ucGxhbk5vLA0KICAgICAgICBkcml2ZXJOYW1lOiB0aGlzLnRhc2tJbmZvRm9ybS5kcml2ZXJOYW1lLA0KICAgICAgICBzZXg6IHRoaXMudGFza0luZm9Gb3JtLnNleCwNCiAgICAgICAgbW9iaWxlUGhvbmU6IHRoaXMudGFza0luZm9Gb3JtLm1vYmlsZVBob25lLA0KICAgICAgICBpZENhcmRObzogdGhpcy50YXNrSW5mb0Zvcm0uaWRDYXJkTm8sDQogICAgICAgIGNhck51bTogdGhpcy50YXNrSW5mb0Zvcm0uY2FyTnVtLA0KICAgICAgICB2ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHM6IHRoaXMudGFza0luZm9Gb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcywNCiAgICAgICAgZmFjZUltZzogdGhpcy50YXNrSW5mb0Zvcm0uZmFjZUltZywNCiAgICAgICAgZHJpdmluZ0xpY2Vuc2VJbWc6IHRoaXMudGFza0luZm9Gb3JtLmRyaXZpbmdMaWNlbnNlSW1nLA0KICAgICAgICBkcml2ZXJMaWNlbnNlSW1nOiB0aGlzLnRhc2tJbmZvRm9ybS5kcml2ZXJMaWNlbnNlSW1nLA0KICAgICAgICBjb21wYW55TmFtZTogdGhpcy50YXNrSW5mb0Zvcm0uY29tcGFueU5hbWUsDQogICAgICAgIGlzRGlyZWN0U3VwcGx5OiAzDQogICAgICB9Ow0KDQogICAgICBsZXQgZGlyZWN0U3VwcGx5VGFza01hdGVyaWFsTGlzdCA9IHRoaXMudGFza01hdGVyaWFsczsNCg0KICAgICAgaWYgKHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnNob3dEcm9wZG93biA9PSB0cnVlICYmIHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLmV4dHJhT3B0aW9uICE9IG51bGwgJiYgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uZXh0cmFPcHRpb24gIT0gJycpIHsNCiAgICAgICAgc3VibWl0RGF0YS5sZWF2ZVRhc2suaXNEaXJlY3RTdXBwbHkgPSAxOyAvLyDorr7nva7kuLrnm7TkvpsNCiAgICAgICAgc3VibWl0RGF0YS5kaXJlY3RTdXBwbHlUYXNrID0gZGlyZWN0U3VwcGx5VGFzazsNCiAgICAgICAgc3VibWl0RGF0YS5kaXJlY3RTdXBwbHlUYXNrTWF0ZXJpYWxMaXN0ID0gZGlyZWN0U3VwcGx5VGFza01hdGVyaWFsTGlzdDsNCiAgICAgIH0NCg0KICAgICAgaGFuZGxlVW5sb2FkKHN1Ym1pdERhdGEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImhhbmRsZVVubG9hZCIsIHJlcykNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn56Gu6K6k5YWl5bqT5oiQ5YqfJyk7DQogICAgICAgICAgdGhpcy5mYWN0b3J5Q29uZmlybURpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tMb2dMaXN0KHRoaXMudGFza05vKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tJbmZvKCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5YW25LuW5aSx6LSl5Y6f5ZugDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn56Gu6K6k5YWl5bqT5aSx6LSlJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2hhbmRsZURpcmVjdFN1cHBseSBlcnJvcjonLCBlcnIpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvZHnu5zlvILluLjvvIznqI3lkI7ph43or5UnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBzdWJtaXRTdG9ja091dENvbmZpcm0oKSB7DQoNCiAgICAgIC8vIOWIpOaWreeUqOaIt+inkuiJsuadg+mZkA0KICAgICAgY29uc3Qgcm9sZXMgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzOw0KICAgICAgaWYgKCFyb2xlcy5pbmNsdWRlcygnbGVhdmUudW5sb2FkaW5nJykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5oKo5rKh5pyJ56Gu6K6k5Ye65bqT5p2D6ZmQJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIC8vIOaehOW7uuaPkOS6pOaVsOaNrg0KICAgICAgbGV0IHN1Ym1pdERhdGEgPSB7DQogICAgICAgIGxlYXZlVGFzazogew0KICAgICAgICAgIC8vdG9kbyDorqHph4/ns7vnu5/ooaXlhYXkv6Hmga/lvoXlrozlloQNCiAgICAgICAgICBpZDogdGhpcy5kaXNwYXRjaElkLA0KICAgICAgICAgIHRhc2tObzogdGhpcy50YXNrTm8sDQogICAgICAgICAgYXBwbHlObzogdGhpcy5hcHBseU5vLA0KICAgICAgICAgIHBsYW5ObzogdGhpcy50YXNrSW5mb0Zvcm0ucGxhbk5vLA0KICAgICAgICAgIC8vIOWHuuW6k+S/oeaBrw0KICAgICAgICAgIHN0b2NrT3V0U3BlYzFMZW5ndGg6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0U3BlYzFMZW5ndGgsDQogICAgICAgICAgc3RvY2tPdXRTcGVjMldpZHRoOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFNwZWMyV2lkdGgsDQogICAgICAgICAgc3RvY2tPdXRUb3RhbHM6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0VG90YWwgKyB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFRvdGFsVW5pdCwNCiAgICAgICAgICBzdG9ja091dFByb2Nlc3NUeXBlOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFByb2Nlc3NUeXBlLA0KICAgICAgICAgIHN0b2NrT3V0SGVhdE5vOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dEhlYXRObywNCiAgICAgICAgICBzdG9ja091dFN0ZWVsR3JhZGU6IHRoaXMuZmFjdG9yeUNvbmZpcm1Gb3JtLnN0b2NrT3V0U3RlZWxHcmFkZSwNCiAgICAgICAgICBzdG9ja091dEF4bGVzOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dEF4bGVzLA0KICAgICAgICAgIHN0b2NrT3V0UmVtYXJrOiB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5zdG9ja091dFJlbWFyaywNCg0KICAgICAgICAgIC8vIOabtOaUueS7u+WKoeeKtuaAgTogOQ0KICAgICAgICAgIHRhc2tTdGF0dXM6IDMsDQogICAgICAgICAgY2FyTnVtOiB0aGlzLnRhc2tJbmZvRm9ybS5jYXJOdW0sDQogICAgICAgIH0sDQogICAgICAgIGxlYXZlUGxhbjogdGhpcy5wbGFuRm9ybSwNCiAgICAgICAgbGVhdmVUYXNrTWF0ZXJpYWw6IHRoaXMudGFza01hdGVyaWFsc1swXSwNCiAgICAgIH07DQoNCiAgICAgIGhhbmRsZVN0b2NrT3V0KHN1Ym1pdERhdGEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImhhbmRsZVN0b2NrT3V0IiwgcmVzKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfnoa7orqTlh7rlupPmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmZhY3RvcnlDb25maXJtRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0luZm8oKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6ANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfnoa7orqTlh7rlupPlpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignaGFuZGxlRGlyZWN0U3VwcGx5IGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGhhbmRsZUZhY3RvcnlDb25maXJtKCkgew0KICAgICAgaWYgKHRoaXMuZWRpdEZhY3RvcnlTdGF0dXMpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjkv53lrZgnKTsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCg0KICAgICAgLy90b2RvDQogICAgICAvL+eUn+aIkOa0vui9puaXpeW/lw0KICAgICAgbGV0IGxlYXZlVGFza0xvZyA9IHt9Ow0KICAgICAgbGVhdmVUYXNrTG9nLmxvZ1R5cGUgPSAyOw0KICAgICAgbGVhdmVUYXNrTG9nLnRhc2tObyA9IHRoaXMudGFza05vOw0KICAgICAgbGVhdmVUYXNrTG9nLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfliIbljoLnoa7orqTmlbDph48nOw0KDQoNCiAgICAgIGxldCBmYWN0b3J5VGFza0luZm8gPSB7fQ0KICAgICAgLy90b2RvIOWHuuWFpeWcug0KICAgICAgZmFjdG9yeVRhc2tJbmZvLmlkID0gdGhpcy50YXNrSW5mb0Zvcm0uaWQNCiAgICAgIGZhY3RvcnlUYXNrSW5mby51bmxvYWRpbmdXb3JrTm8gPSAn5Y246LSn5Lq65Y2g5L2N56ymJw0KICAgICAgZmFjdG9yeVRhc2tJbmZvLnVubG9hZGluZ1RpbWUgPSBuZXcgRGF0ZSgpDQogICAgICBmYWN0b3J5VGFza0luZm8udGFza1N0YXR1cyA9IDkNCg0KICAgICAgbGV0IHBhcmFtID0ge307DQogICAgICBwYXJhbS50YXNrTWF0ZXJpYWxMaXN0ID0gdGhpcy50YXNrTWF0ZXJpYWxzOw0KICAgICAgcGFyYW0ubGVhdmVMb2cgPSBsZWF2ZVRhc2tMb2c7DQogICAgICBwYXJhbS5sZWF2ZVRhc2sgPSBmYWN0b3J5VGFza0luZm87DQogICAgICBwYXJhbS5tZWFzdXJlRmxhZyA9IHRoaXMubWVhc3VyZUZsYWc7DQoNCiAgICAgIHRoaXMuY2FsbEFwaVdpdGhHYXRlTG9jYXRpb24ocGFyYW0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImFkZExlYXZlTG9nQW5kRWRpdFRhc2tNYXRlcmlhbHNBbmRVcGRhdGVUYXNrIiwgcmVzKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliIbljoLnoa7orqTmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tMb2dMaXN0KHRoaXMudGFza05vKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tJbmZvKCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5YW25LuW5aSx6LSl5Y6f5ZugDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn5YiG5Y6C56Gu6K6k5oiQ5YqfJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2hhbmRsZUZhY3RvcnlDb25maXJtIGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KDQogICAgaGFuZGxlRG9vck1hbkNvbmZpcm0oKSB7DQogICAgICBpZiAodGhpcy5lZGl0RG9vck1hblN0YXR1cykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOS/neWtmCcpOw0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgbGV0IGxlYXZlVGFza0xvZyA9IHt9Ow0KICAgICAgbGVhdmVUYXNrTG9nLmxvZ1R5cGUgPSAyOw0KICAgICAgbGVhdmVUYXNrTG9nLnRhc2tObyA9IHRoaXMudGFza05vOw0KICAgICAgbGVhdmVUYXNrTG9nLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfpl6jljavnoa7orqTmlbDph48nOw0KDQoNCg0KICAgICAgbGV0IGRvb3JNYW5UYXNrSW5mbyA9IHt9DQogICAgICBkb29yTWFuVGFza0luZm8uaWQgPSB0aGlzLnRhc2tJbmZvRm9ybS5pZA0KICAgICAgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDEpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA5DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5sZWF2ZVRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v56a75Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDIgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAwKSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNw0KICAgICAgICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+WHuuWOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAyICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDYNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMyAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDQpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA1DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5sZWF2ZVRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v56a75Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAwICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDcNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMyAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDEgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA1KSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNg0KICAgICAgICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+WHuuWOguWkp+mXqA0KICAgICAgfQ0KDQogICAgICBsZXQgcGFyYW0gPSB7fTsNCiAgICAgIHBhcmFtLnRhc2tNYXRlcmlhbExpc3QgPSB0aGlzLnRhc2tNYXRlcmlhbHM7DQogICAgICBwYXJhbS5sZWF2ZUxvZyA9IGxlYXZlVGFza0xvZzsNCiAgICAgIHBhcmFtLmxlYXZlVGFzayA9IGRvb3JNYW5UYXNrSW5mbzsNCiAgICAgIHBhcmFtLm1lYXN1cmVGbGFnID0gdGhpcy5tZWFzdXJlRmxhZzsNCg0KICAgICAgdGhpcy5jYWxsQXBpV2l0aEdhdGVMb2NhdGlvbihwYXJhbSkudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2siLCByZXMpDQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mXqOWNq+ehruiupOaIkOWKnycpOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0luZm8oKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6ANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfpl6jljavnoa7orqTmiJDlip8nKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignaGFuZGxlRG9vck1hbkNvbmZpcm0gZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICB9KTsNCg0KICAgICAgLy8gdGhpcy50YXNrTWF0ZXJpYWxzLm1hcChpdGVtID0+IHsNCiAgICAgIC8vICAgZWRpdFRhc2ttYXRlcmlhbHMoaXRlbSk7DQogICAgICAvLyB9KQ0KICAgICAgLy90b2RvDQogICAgICAvLyBsZXQgbGVhdmVUYXNrTG9nID0ge307DQogICAgICAvLyBsZWF2ZVRhc2tMb2cubG9nVHlwZSA9IDI7DQogICAgICBsZWF2ZVRhc2tMb2cudGFza05vID0gdGhpcy50YXNrTm87DQogICAgICBsZWF2ZVRhc2tMb2cuYXBwbHlObyA9IHRoaXMuYXBwbHlObzsNCiAgICAgIGxlYXZlVGFza0xvZy5pbmZvID0gJ+mXqOWNq+ehruiupOaVsOmHjyc7DQogICAgICAvLyBhZGRMZWF2ZUxvZyhsZWF2ZVRhc2tMb2cpOw0KICAgICAgLy8gdGhpcy5nZXRUYXNrTG9nTGlzdCh0aGlzLnRhc2tObyk7DQoNCiAgICAgIC8vIGxldCBkb29yTWFuVGFza0luZm8gPSB7fQ0KICAgICAgLy8gZG9vck1hblRhc2tJbmZvLmlkID0gdGhpcy50YXNrSW5mb0Zvcm0uaWQNCiAgICAgIC8vIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAxKSB7DQogICAgICAvLyAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gOQ0KICAgICAgLy8gICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKQ0KICAgICAgLy8gICAvL+emu+WOguWkp+mXqA0KICAgICAgLy8gfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAyICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCkgew0KICAgICAgLy8gICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDcNCiAgICAgIC8vICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkNCiAgICAgIC8vICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIC8vIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMiAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDEpIHsNCiAgICAgIC8vICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA2DQogICAgICAvLyAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpDQogICAgICAvLyAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICAvLyB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA0KSB7DQogICAgICAvLyAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNQ0KICAgICAgLy8gICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKQ0KICAgICAgLy8gICAvL+emu+WOguWkp+mXqA0KICAgICAgLy8gfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDUpIHsNCiAgICAgIC8vICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA3DQogICAgICAvLyAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpDQogICAgICAvLyAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICAvLyB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAxICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNSkgew0KICAgICAgLy8gICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDYNCiAgICAgIC8vICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkNCiAgICAgIC8vICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIC8vIH0NCiAgICAgIC8vIHVwZGF0ZVRhc2soZG9vck1hblRhc2tJbmZvKTsNCiAgICAgIC8vIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6Zeo5Y2r56Gu6K6k5oiQ5YqfJyk7DQoNCiAgICAgIC8vIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgLy8gICB0aGlzLmdldFRhc2tJbmZvKCk7DQogICAgICAvLyB9LCA1MDApDQoNCiAgICB9LA0KDQogICAgaGFuZGxlRG9vck1hbk1lYXN1cmVDb25maXJtKCkgew0KICAgICAgLy8g5Yik5pat55So5oi36KeS6Imy5p2D6ZmQDQogICAgICBjb25zdCByb2xlcyA9IHRoaXMuJHN0b3JlLmdldHRlcnMucm9sZXM7DQogICAgICBpZiAoIXJvbGVzLmluY2x1ZGVzKCdsZWF2ZS5ndWFyZCcpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aCqOayoeaciemXqOWNq+WHuuWOguehruiupOadg+mZkCcpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGxldCBsZWF2ZVRhc2tMb2cgPSB7fTsNCiAgICAgIGxlYXZlVGFza0xvZy5sb2dUeXBlID0gMjsNCiAgICAgIGxlYXZlVGFza0xvZy50YXNrTm8gPSB0aGlzLnRhc2tObzsNCiAgICAgIGxlYXZlVGFza0xvZy5hcHBseU5vID0gdGhpcy5hcHBseU5vOw0KICAgICAgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNCkgew0KICAgICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfpl6jljavlh7rljoLnoa7orqTvvIznoa7orqTnianotYTvvJonICsgdGhpcy50YXNrTWF0ZXJpYWxzLm1hcChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxOYW1lKS5qb2luKCfjgIEgJyk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfpl6jljavlhaXljoLnoa7orqTvvIznoa7orqTnianotYTvvJonICsgdGhpcy50YXNrTWF0ZXJpYWxzLm1hcChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxOYW1lKS5qb2luKCfjgIEgJyk7DQogICAgICB9DQoNCiAgICAgIGxldCBkb29yTWFuVGFza0luZm8gPSB7fQ0KICAgICAgZG9vck1hblRhc2tJbmZvLmlkID0gdGhpcy50YXNrSW5mb0Zvcm0uaWQNCiAgICAgIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAxKSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gOQ0KICAgICAgICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+emu+WOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAyICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDcNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMiAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDEpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA2DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA0KSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNQ0KICAgICAgICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+emu+WOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDUpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA3DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAxICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDYNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0NCg0KICAgICAgbGV0IHBhcmFtID0ge307DQogICAgICBwYXJhbS50YXNrTWF0ZXJpYWxMaXN0ID0gdGhpcy50YXNrTWF0ZXJpYWxzOw0KICAgICAgcGFyYW0ubGVhdmVMb2cgPSBsZWF2ZVRhc2tMb2c7DQogICAgICBwYXJhbS5sZWF2ZVRhc2sgPSBkb29yTWFuVGFza0luZm87DQogICAgICBwYXJhbS5tZWFzdXJlRmxhZyA9IHRoaXMubWVhc3VyZUZsYWc7DQoNCiAgICAgIHRoaXMuY2FsbEFwaVdpdGhHYXRlTG9jYXRpb24ocGFyYW0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImFkZExlYXZlTG9nQW5kRWRpdFRhc2tNYXRlcmlhbHNBbmRVcGRhdGVUYXNrIiwgcmVzKQ0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfpl6jljavnoa7orqTmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tMb2dMaXN0KHRoaXMudGFza05vKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tJbmZvKCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5YW25LuW5aSx6LSl5Y6f5ZugDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn6Zeo5Y2r56Gu6K6k5oiQ5YqfJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2hhbmRsZURvb3JNYW5Db25maXJtIGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgfSk7DQogICAgICAvL3RvZG8NCg0KICAgIH0sDQogICAgLy8g55Sf5oiQ5LqM57u056CBDQogICAgY3JlYXRRckNvZGUoKSB7DQogICAgICBpZiAodGhpcy50YXNrSW5mb0Zvcm0ucXJDb2RlQ29udGVudCkgew0KICAgICAgICB0aGlzLiRyZWZzLnFyQ29kZS5pbm5lckhUTUwgPSAiIjsNCiAgICAgICAgdmFyIFlTcXJDb2RlID0gbmV3IFFSQ29kZSh0aGlzLiRyZWZzLnFyQ29kZSwgew0KICAgICAgICAgIHRleHQ6IHRoaXMudGFza0luZm9Gb3JtLnFyQ29kZUNvbnRlbnQsIC8vIOmcgOimgei9rOaNouS4uuS6jOe7tOeggeeahOWGheWuuQ0KICAgICAgICAgIHdpZHRoOiAxMjAsDQogICAgICAgICAgaGVpZ2h0OiAxMjAsDQogICAgICAgICAgY29sb3JEYXJrOiAiIzAwMDAwMCIsDQogICAgICAgICAgY29sb3JMaWdodDogIiNmZmZmZmYiLA0KICAgICAgICAgIGNvcnJlY3RMZXZlbDogUVJDb2RlLkNvcnJlY3RMZXZlbC5ILA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldFRhc2tMb2dMaXN0KHRhc2tObykgew0KICAgICAgbGV0IHRhc2tMb2cgPSB7fTsNCiAgICAgIHRhc2tMb2cudGFza05vID0gdGFza05vDQogICAgICBnZXRUYXNrTG9ncyh0YXNrTG9nKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coImdldFRhc2tMb2dzIiwgcmVzcG9uc2UpOw0KICAgICAgICAvLyB0aGlzLnRhc2tMb2dzID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgbGV0IGxvZ3MgPSByZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICAvLyDmib7lh7rljIXlkKsi5Lu75Yqh5a6M5oiQIueahOaXpeW/lw0KICAgICAgICBjb25zdCBmaW5pc2hlZExvZ3MgPSBsb2dzLmZpbHRlcihsb2cgPT4gbG9nLmluZm8gJiYgbG9nLmluZm8uaW5jbHVkZXMoJ+S7u+WKoeWujOaIkCcpKTsNCiAgICAgICAgY29uc3Qgb3RoZXJMb2dzID0gbG9ncy5maWx0ZXIobG9nID0+ICEobG9nLmluZm8gJiYgbG9nLmluZm8uaW5jbHVkZXMoJ+S7u+WKoeWujOaIkCcpKSk7DQogICAgICAgIC8vIOWFiOaUviLku7vliqHlrozmiJAi77yM5YaN5pS+5YW25LuWDQogICAgICAgIHRoaXMudGFza0xvZ3MgPSBbLi4uZmluaXNoZWRMb2dzLCAuLi5vdGhlckxvZ3NdOw0KICAgICAgfSkNCg0KICAgIH0sDQogICAgYXN5bmMgZ2V0VGFza21hdGVyaWFsTGlzdCh0YXNrTm8pIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCJnZXRUYXNrbWF0ZXJpYWxMaXN0Iik7DQogICAgICAgIGxldCBsZWF2ZU1hdGVyaWFsID0ge307DQogICAgICAgIGxlYXZlTWF0ZXJpYWwudGFza05vID0gdGFza05vOw0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFRhc2ttYXRlcmlhbHMobGVhdmVNYXRlcmlhbCk7DQogICAgICAgIHRoaXMudGFza01hdGVyaWFscyA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIC8vIOi1i+WAvOWQju+8jOWIneWni+WMluavj+S4quWFg+e0oOeahCBkb29ybWFuUmVjZWl2ZU51bSDlkowgZG9vcm1hblJlY2VpdmVOdW1Jbg0KICAgICAgICB0aGlzLnRhc2tNYXRlcmlhbHMuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLmRvb3JtYW5SZWNlaXZlTnVtID0gaXRlbS5wbGFuTnVtOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCJpdGVtLnBsYW5UeXBlIiwgdGhpcy5wbGFuRm9ybS5wbGFuVHlwZSk7DQogICAgICAgICAgaWYgKHRoaXMucGxhbkZvcm0ucGxhblR5cGUgPT0gMiB8fCB0aGlzLnBsYW5Gb3JtLnBsYW5UeXBlID09IDMpIHsNCiAgICAgICAgICAgIGl0ZW0uZG9vcm1hblJlY2VpdmVOdW1JbiA9IGl0ZW0ucGxhbk51bTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgICBjb25zb2xlLmxvZygidGFza01hdGVyaWFscyIsIHRoaXMudGFza01hdGVyaWFscyk7DQogICAgICAgIHJldHVybiByZXNwb25zZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldFRhc2ttYXRlcmlhbExpc3QgZXJyb3I6JywgZXJyb3IpOw0KICAgICAgICB0aHJvdyBlcnJvcjsNCiAgICAgIH0NCiAgICB9LA0KICAgIGVkaXREb29yTWFuUm93KHJvdykgew0KICAgICAgcm93Ll9iYWNrdXAgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJvdykpOy8v5rex5ou36LSdDQogICAgICB0aGlzLmVkaXRpbmdSb3cgPSByb3c7DQogICAgICB0aGlzLmVkaXREb29yTWFuU3RhdHVzID0gdHJ1ZTsNCiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLmVkaXREb29yTWFuUm93Iiwgcm93KTsNCiAgICB9LA0KICAgIGVkaXRGYWN0b3J5Um93KCkgew0KICAgICAgdGhpcy5iYWNrdXBNYXRlcmlhbHMgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMudGFza01hdGVyaWFscykpOy8v5rex5ou36LSdDQogICAgICB0aGlzLmVkaXRGYWN0b3J5U3RhdHVzID0gdHJ1ZTsNCiAgICB9LA0KICAgIGNhbmNlbERvb3JNYW5FZGl0KHJvdykgew0KICAgICAgLy/mt7Hmi7fotJ0NCiAgICAgIGlmIChyb3cuX2JhY2t1cCkgew0KICAgICAgICAvLyDmgaLlpI3lpIfku73mlbDmja4NCiAgICAgICAgT2JqZWN0LmFzc2lnbihyb3csIHJvdy5fYmFja3VwKTsNCiAgICAgICAgZGVsZXRlIHJvdy5fYmFja3VwOyAvLyDliKDpmaTlpIfku73mlbDmja4NCiAgICAgIH07DQogICAgICB0aGlzLmVkaXRpbmdSb3cgPSBudWxsOyAvLyDmuIXnqbrlvZPliY3nvJbovpHooYwNCiAgICAgIHRoaXMuZWRpdERvb3JNYW5TdGF0dXMgPSBmYWxzZTsNCiAgICB9LA0KICAgIGNhbmNlbEZhY3RvcnlFZGl0KCkgew0KICAgICAgdGhpcy50YXNrTWF0ZXJpYWxzID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmJhY2t1cE1hdGVyaWFscykpOy8v5rex5ou36LSdDQogICAgICBjb25zb2xlLmxvZygidGhpcy50YXNrTWF0ZXJpYWxzIiwgdGhpcy50YXNrTWF0ZXJpYWxzKTsNCiAgICAgIHRoaXMuZWRpdEZhY3RvcnlTdGF0dXMgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgc2F2ZURvb3JNYW5Sb3dJbigpIHsNCiAgICAgIC8vIOWIpOaWreeUqOaIt+inkuiJsuadg+mZkA0KICAgICAgY29uc3Qgcm9sZXMgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzOw0KICAgICAgaWYgKCFyb2xlcy5pbmNsdWRlcygnbGVhdmUuZ3VhcmQnKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmgqjmsqHmnInpl6jljavlh7rljoLnoa7orqTmnYPpmZAnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy50YXNrTWF0ZXJpYWxzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTWF0ZXJpYWxzIiwgdGhpcy50YXNrTWF0ZXJpYWxzKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfnianotYTlvILluLgnKTsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOagoemqjGRvb3JtYW5SZWNlaXZlTnVtSW7mmK/lkKbnrYnkuo5wbGFuTnVtDQogICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgdGhpcy50YXNrTWF0ZXJpYWxzKSB7DQogICAgICAgIGlmIChpdGVtLmRvb3JtYW5SZWNlaXZlTnVtSW4gIT09IGl0ZW0ucGxhbk51bSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZyhg54mp6LWEIiR7aXRlbS5tYXRlcmlhbE5hbWV9IueahOmXqOWNq+WFpeWOguehruiupOaVsOmHjygke2l0ZW0uZG9vcm1hblJlY2VpdmVOdW1Jbn0p5LiO6K6h5YiS5pWw6YePKCR7aXRlbS5wbGFuTnVtfSnkuI3kuIDoh7TvvIzor7fmo4Dmn6VgKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgbGV0IGxlYXZlVGFza0xvZyA9IHt9Ow0KICAgICAgbGVhdmVUYXNrTG9nLmxvZ1R5cGUgPSAyOw0KICAgICAgbGVhdmVUYXNrTG9nLnRhc2tObyA9IHRoaXMudGFza05vOw0KICAgICAgbGVhdmVUYXNrTG9nLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfpl6jljavlhaXljoLnoa7orqTvvIznoa7orqTnianotYTvvJonICsgdGhpcy50YXNrTWF0ZXJpYWxzLm1hcChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxOYW1lKS5qb2luKCfjgIEgJyk7DQoNCiAgICAgIGxldCBkb29yTWFuVGFza0luZm8gPSB7fQ0KICAgICAgZG9vck1hblRhc2tJbmZvLmlkID0gdGhpcy50YXNrSW5mb0Zvcm0uaWQ7DQogICAgICBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDkNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmxlYXZlVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/nprvljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMiAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDApIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA3DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDIgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAxKSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNg0KICAgICAgICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+WHuuWOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNCkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDUNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmxlYXZlVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/nprvljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMyAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDAgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA1KSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNw0KICAgICAgICBkb29yTWFuVGFza0luZm8uZW50ZXJUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+WHuuWOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMSAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDUpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA2DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9DQoNCiAgICAgIGxldCBwYXJhbSA9IHsNCiAgICAgICAgdGFza01hdGVyaWFsTGlzdDogdGhpcy50YXNrTWF0ZXJpYWxzLA0KICAgICAgICBsZWF2ZUxvZzogbGVhdmVUYXNrTG9nLA0KICAgICAgICBsZWF2ZVRhc2s6IGRvb3JNYW5UYXNrSW5mbywNCiAgICAgICAgbWVhc3VyZUZsYWc6IHRoaXMubWVhc3VyZUZsYWcNCiAgICAgIH07DQoNCiAgICAgIGNvbnNvbGUubG9nKCJhZGRMZWF2ZUxvZ0FuZEVkaXRUYXNrTWF0ZXJpYWxzQW5kVXBkYXRlVGFzayIsIHBhcmFtLCB0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSk7DQoNCg0KICAgICAgdGhpcy5jYWxsQXBpV2l0aEdhdGVMb2NhdGlvbihwYXJhbSkudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygiYWRkTGVhdmVMb2dBbmRFZGl0VGFza01hdGVyaWFsc0FuZFVwZGF0ZVRhc2siLCByZXMpDQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mXqOWNq+ehruiupOaIkOWKnycpOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0xvZ0xpc3QodGhpcy50YXNrTm8pOw0KICAgICAgICAgIHRoaXMuZ2V0VGFza0luZm8oKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlhbbku5blpLHotKXljp/lm6ANCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfpl6jljavnoa7orqTmiJDlip8nKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignaGFuZGxlRG9vck1hbkNvbmZpcm0gZXJyb3I6JywgZXJyKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICB9KTsNCg0KICAgICAgdGhpcy5lZGl0RG9vck1hblN0YXR1cyA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICBzYXZlRG9vck1hblJvdygpIHsNCiAgICAgIC8vIOWIpOaWreeUqOaIt+inkuiJsuadg+mZkA0KICAgICAgY29uc3Qgcm9sZXMgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLnJvbGVzOw0KICAgICAgY29uc29sZS5sb2coInJvbGVzIiwgcm9sZXMpOw0KICAgICAgaWYgKCFyb2xlcy5pbmNsdWRlcygnbGVhdmUuZ3VhcmQnKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmgqjmsqHmnInpl6jljavlh7rljoLnoa7orqTmnYPpmZAnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy50YXNrTWF0ZXJpYWxzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTWF0ZXJpYWxzIiwgdGhpcy50YXNrTWF0ZXJpYWxzKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfnianotYTlvILluLgnKTsNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOagoemqjGRvb3JtYW5SZWNlaXZlTnVt5piv5ZCm562J5LqOcGxhbk51bQ0KICAgICAgZm9yIChjb25zdCBpdGVtIG9mIHRoaXMudGFza01hdGVyaWFscykgew0KICAgICAgICBpZiAoaXRlbS5kb29ybWFuUmVjZWl2ZU51bSAhPT0gaXRlbS5wbGFuTnVtKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDnianotYQiJHtpdGVtLm1hdGVyaWFsTmFtZX0i55qE6Zeo5Y2r56Gu6K6k5pWw6YePKCR7aXRlbS5kb29ybWFuUmVjZWl2ZU51bX0p5LiO6K6h5YiS5pWw6YePKCR7aXRlbS5wbGFuTnVtfSnkuI3kuIDoh7TvvIzor7fmo4Dmn6VgKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgbGV0IGxlYXZlVGFza0xvZyA9IHt9Ow0KICAgICAgbGVhdmVUYXNrTG9nLmxvZ1R5cGUgPSAyOw0KICAgICAgbGVhdmVUYXNrTG9nLnRhc2tObyA9IHRoaXMudGFza05vOw0KICAgICAgbGVhdmVUYXNrTG9nLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICBsZWF2ZVRhc2tMb2cuaW5mbyA9ICfpl6jljavlh7rljoLnoa7orqTvvIznoa7orqTnianotYTvvJonICsgdGhpcy50YXNrTWF0ZXJpYWxzLm1hcChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxOYW1lKS5qb2luKCfjgIEgJyk7DQoNCiAgICAgIGxldCBkb29yTWFuVGFza0luZm8gPSB7fQ0KICAgICAgZG9vck1hblRhc2tJbmZvLmlkID0gdGhpcy50YXNrSW5mb0Zvcm0uaWQNCiAgICAgIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAxKSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gOQ0KICAgICAgICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+emu+WOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAyICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDcNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0udGFza1R5cGUgPT0gMiAmJiB0aGlzLm1lYXN1cmVGbGFnID09IDEpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA2DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyA9PSA0KSB7DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby50YXNrU3RhdHVzID0gNQ0KICAgICAgICBkb29yTWFuVGFza0luZm8ubGVhdmVUaW1lID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKQ0KICAgICAgICAvL+emu+WOguWkp+mXqA0KICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS50YXNrVHlwZSA9PSAzICYmIHRoaXMubWVhc3VyZUZsYWcgPT0gMCAmJiB0aGlzLnRhc2tJbmZvRm9ybS50YXNrU3RhdHVzID09IDUpIHsNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLnRhc2tTdGF0dXMgPSA3DQogICAgICAgIGRvb3JNYW5UYXNrSW5mby5lbnRlclRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpDQogICAgICAgIC8v5Ye65Y6C5aSn6ZeoDQogICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlID09IDMgJiYgdGhpcy5tZWFzdXJlRmxhZyA9PSAxICYmIHRoaXMudGFza0luZm9Gb3JtLnRhc2tTdGF0dXMgPT0gNSkgew0KICAgICAgICBkb29yTWFuVGFza0luZm8udGFza1N0YXR1cyA9IDYNCiAgICAgICAgZG9vck1hblRhc2tJbmZvLmVudGVyVGltZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxOSkucmVwbGFjZSgnVCcsICcgJykNCiAgICAgICAgLy/lh7rljoLlpKfpl6gNCiAgICAgIH0NCg0KICAgICAgbGV0IHBhcmFtID0gew0KICAgICAgICB0YXNrTWF0ZXJpYWxMaXN0OiB0aGlzLnRhc2tNYXRlcmlhbHMsDQogICAgICAgIGxlYXZlTG9nOiBsZWF2ZVRhc2tMb2csDQogICAgICAgIGxlYXZlVGFzazogZG9vck1hblRhc2tJbmZvLA0KICAgICAgICBtZWFzdXJlRmxhZzogdGhpcy5tZWFzdXJlRmxhZw0KICAgICAgfTsNCg0KICAgICAgY29uc29sZS5sb2coImFkZExlYXZlTG9nQW5kRWRpdFRhc2tNYXRlcmlhbHNBbmRVcGRhdGVUYXNrIiwgcGFyYW0sIHRoaXMudGFza0luZm9Gb3JtLnRhc2tUeXBlKTsNCg0KDQogICAgICB0aGlzLmNhbGxBcGlXaXRoR2F0ZUxvY2F0aW9uKHBhcmFtKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCJhZGRMZWF2ZUxvZ0FuZEVkaXRUYXNrTWF0ZXJpYWxzQW5kVXBkYXRlVGFzayIsIHJlcykNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6Zeo5Y2r56Gu6K6k5oiQ5YqfJyk7DQogICAgICAgICAgdGhpcy5nZXRUYXNrTG9nTGlzdCh0aGlzLnRhc2tObyk7DQogICAgICAgICAgdGhpcy5nZXRUYXNrSW5mbygpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWFtuS7luWksei0peWOn+WboA0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1lc3NhZ2UgfHwgJ+mXqOWNq+ehruiupOaIkOWKnycpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnIgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCdoYW5kbGVEb29yTWFuQ29uZmlybSBlcnJvcjonLCBlcnIpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvZHnu5zlvILluLjvvIznqI3lkI7ph43or5UnKTsNCiAgICAgIH0pOw0KDQogICAgICB0aGlzLmVkaXREb29yTWFuU3RhdHVzID0gZmFsc2U7DQogICAgfSwNCg0KDQogICAgc2F2ZUZhY3RvcnlSb3coKSB7DQoNCiAgICAgIHRoaXMuZWRpdEZhY3RvcnlTdGF0dXMgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgcmVzZXRUYXNrSW5mb0Zvcm0oKSB7DQogICAgICB0aGlzLnRhc2tJbmZvRm9ybSA9IHt9Ow0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRUYXNrSW5mbygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0VGFzayh0aGlzLmRpc3BhdGNoSWQpOw0KICAgICAgICB0aGlzLnRhc2tJbmZvRm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnRhc2tJbmZvRm9ybSIsIHRoaXMudGFza0luZm9Gb3JtKTsNCiAgICAgICAgaWYgKHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID09IDEpIHsNCiAgICAgICAgICB0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9ICfok53oibInDQogICAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPT0gMikgew0KICAgICAgICAgIHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID0gJ+e7v+iJsicNCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9PSAzKSB7DQogICAgICAgICAgdGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPSAn6buEJw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID09IDQpIHsNCiAgICAgICAgICB0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9ICfpu4Tnu7/oibInDQogICAgICAgIH0NCiAgICAgICAgY29uc29sZS5sb2coInRoaXMudGFza0luZm9Gb3JtIiwgdGhpcy50YXNrSW5mb0Zvcm0pOw0KICAgICAgICAvLyDnlJ/miJDkuoznu7TnoIENCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuY3JlYXRRckNvZGUoKTsNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybiByZXNwb25zZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ2dldFRhc2tJbmZvIGVycm9yOicsIGVycm9yKTsNCiAgICAgICAgdGhyb3cgZXJyb3I7DQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIGdldFRhc2tJbmZvQnlUYXNrTm8oKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFRhc2tCeVRhc2tObyh0aGlzLnRhc2tObyk7DQogICAgICAgIHRoaXMudGFza0luZm9Gb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgY29uc29sZS5sb2coInRoaXMudGFza0luZm9Gb3JtIiwgdGhpcy50YXNrSW5mb0Zvcm0pOw0KDQogICAgICAgIC8vIOS7jui/lOWbnueahOaVsOaNruS4reiOt+WPluaJgOmcgOeahOWPguaVsA0KICAgICAgICB0aGlzLmRpc3BhdGNoSWQgPSB0aGlzLnRhc2tJbmZvRm9ybS5pZDsNCiAgICAgICAgdGhpcy5hcHBseU5vID0gdGhpcy50YXNrSW5mb0Zvcm0uYXBwbHlObzsNCg0KICAgICAgICBpZiAodGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPT0gMSkgew0KICAgICAgICAgIHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID0gJ+iTneiJsicNCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9PSAyKSB7DQogICAgICAgICAgdGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPSAn57u/6ImyJw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID09IDMpIHsNCiAgICAgICAgICB0aGlzLnRhc2tJbmZvRm9ybS5saWNlbnNlUGxhdGVDb2xvciA9ICfpu4QnDQogICAgICAgIH0gZWxzZSBpZiAodGhpcy50YXNrSW5mb0Zvcm0ubGljZW5zZVBsYXRlQ29sb3IgPT0gNCkgew0KICAgICAgICAgIHRoaXMudGFza0luZm9Gb3JtLmxpY2Vuc2VQbGF0ZUNvbG9yID0gJ+m7hOe7v+iJsicNCiAgICAgICAgfQ0KICAgICAgICBjb25zb2xlLmxvZygidGhpcy50YXNrSW5mb0Zvcm0iLCB0aGlzLnRhc2tJbmZvRm9ybSk7DQogICAgICAgIC8vIOeUn+aIkOS6jOe7tOeggQ0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5jcmVhdFFyQ29kZSgpOw0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuIHJlc3BvbnNlOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignZ2V0VGFza0luZm9CeVRhc2tObyBlcnJvcjonLCBlcnJvcik7DQogICAgICAgIHRocm93IGVycm9yOw0KICAgICAgfQ0KICAgIH0sDQoNCg0KICAgIGdldFN0YXR1c1RleHQoc3RhbmRhcmQpIHsNCiAgICAgIGNvbnN0IHN0YW5kYXJkTWFwID0gew0KICAgICAgICAxOiAn5b6F6L+H55qu6YeNJywNCiAgICAgICAgMjogJ+W+heijhei0pycsDQogICAgICAgIDM6ICflvoXov4fmr5vph40nLA0KICAgICAgICA0OiAn5b6F5Ye65Y6CJywNCiAgICAgICAgNTogJ+W+hei/lOWOgicsDQogICAgICAgIDY6ICflvoXov4fmr5vph40o5aSN56OFKScsDQogICAgICAgIDc6ICflvoXljbjotKcnLA0KICAgICAgICA4OiAn5b6F6L+H55qu6YeNKOWkjeejhSknLA0KICAgICAgICA5OiAn5a6M5oiQJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGFuZGFyZE1hcFtzdGFuZGFyZF0gfHwgJ+acquefpSc7DQogICAgfSwNCg0KICAgIC8v6K6h5YiS54q25oCBDQogICAgZ2V0UGxhblN0YXR1c1RleHQoc3RhbmRhcmQpIHsNCiAgICAgIGNvbnN0IHN0YW5kYXJkTWFwID0gew0KICAgICAgICAxOiAn5b6F5YiG5Y6C5a6h5om5JywNCiAgICAgICAgMjogJ+W+heWIhuWOguWkjeWuoScsDQogICAgICAgIDM6ICflvoXnlJ/kuqfmjIfmjKXkuK3lv4PlrqHmibknLA0KICAgICAgICA0OiAn5a6h5om55a6M5oiQJywNCiAgICAgICAgNTogJ+W3suWHuuWOgicsDQogICAgICAgIDY6ICfpg6jliIbmlLbotKcnLA0KICAgICAgICA3OiAn5bey5a6M5oiQJywNCiAgICAgICAgMTE6ICfpqbPlm54nLA0KICAgICAgICAxMjogJ+W6n+W8gycsDQogICAgICAgIDEzOiAn6L+H5pyfJywNCiAgICAgICAgJ+W+heWIhuWOguWuoeaJuSc6ICflvoXliIbljoLlrqHmibknLA0KICAgICAgICAn5b6F5YiG5Y6C5aSN5a6hJzogJ+W+heWIhuWOguWkjeWuoScsDQogICAgICAgICflvoXnlJ/kuqfmjIfmjKXkuK3lv4PlrqHmibknOiAn5b6F55Sf5Lqn5oyH5oyl5Lit5b+D5a6h5om5JywNCiAgICAgICAgJ+WuoeaJueWujOaIkCc6ICflrqHmibnlrozmiJAnLA0KICAgICAgICAn5bey5Ye65Y6CJzogJ+W3suWHuuWOgicsDQogICAgICAgICfpg6jliIbmlLbotKcnOiAn6YOo5YiG5pS26LSnJywNCiAgICAgICAgJ+W3suWujOaIkCc6ICflt7LlrozmiJAnLA0KICAgICAgICAn6amz5ZueJzogJ+mps+WbnicsDQogICAgICAgICflup/lvIMnOiAn5bqf5byDJywNCiAgICAgICAgJ+i/h+acnyc6ICfov4fmnJ8nLA0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGFuZGFyZE1hcFtzdGFuZGFyZF0gfHwgJ+acquefpSc7DQogICAgfSwNCiAgICAvLyDojrflj5bmjpLmlL7moIflh4bmlofmnKwNCiAgICBnZXRFbWlzc2lvblN0YW5kYXJkc1RleHQoc3RhbmRhcmQpIHsNCiAgICAgIGNvbnN0IHN0YW5kYXJkTWFwID0gew0KICAgICAgICAxOiAn5Zu95LqUJywNCiAgICAgICAgMjogJ+WbveWFrScsDQogICAgICAgIDM6ICfmlrDog73mupAnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHN0YW5kYXJkTWFwW3N0YW5kYXJkXSB8fCAn5pyq55+lJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5o6S5pS+5qCH5YeG5qCH562+57G75Z6LDQogICAgZ2V0RW1pc3Npb25TdGFuZGFyZHNUYWdUeXBlKHN0YW5kYXJkKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAxOiAnd2FybmluZycsICAvLyDlm73kupQNCiAgICAgICAgMjogJ3N1Y2Nlc3MnLCAgLy8g5Zu95YWtDQogICAgICAgIDM6ICdwcmltYXJ5JyAgIC8vIOaWsOiDvea6kA0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3N0YW5kYXJkXSB8fCAnaW5mbyc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueJqei1hOeKtuaAgeaWh+acrA0KICAgIGdldE1hdGVyaWFsU3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgMTogJ+W+heijhei9vScsDQogICAgICAgIDI6ICflt7Loo4Xovb0nLA0KICAgICAgICAzOiAn5bey562+5pS2JywNCiAgICAgICAgNDogJ+W8guW4uCcNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ+acquefpeeKtuaAgSc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueJqei1hOeKtuaAgeagh+etvuexu+Weiw0KICAgIGdldE1hdGVyaWFsU3RhdHVzVHlwZShzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7DQogICAgICAgIDE6ICdpbmZvJywgICAgIC8vIOW+heijhei9vQ0KICAgICAgICAyOiAnd2FybmluZycsICAvLyDlt7Loo4Xovb0NCiAgICAgICAgMzogJ3N1Y2Nlc3MnLCAgLy8g5bey562+5pS2DQogICAgICAgIDQ6ICdkYW5nZXInICAgIC8vIOW8guW4uA0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3N0YXR1c10gfHwgJ2luZm8nOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bml6Xlv5fpopzoibINCiAgICBnZXRMb2dDb2xvcihsb2cpIHsNCiAgICAgIGNvbnN0IGxvZ1R5cGVDb2xvck1hcCA9IHsNCiAgICAgICAgMTogJyM0MDlFRkYnLCAvLyDliJvlu7oNCiAgICAgICAgMjogJyNFNkEyM0MnLCAvLyDmm7TmlrANCiAgICAgICAgMzogJyM2N0MyM0EnLCAvLyDlrozmiJANCiAgICAgICAgNDogJyNGNTZDNkMnLCAvLyDlvILluLgNCiAgICAgICAgNTogJyM5MDkzOTknICAvLyDlhbbku5YNCiAgICAgIH07DQogICAgICByZXR1cm4gbG9nVHlwZUNvbG9yTWFwW2xvZy50eXBlXSB8fCAnIzQwOUVGRic7DQogICAgfSwNCg0KICAgIC8vIOi/lOWbnuaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluS7u+WKoeivpuaDheaVsOaNrg0KICAgIGdldFRhc2tEZXRhaWwoZGlzcGF0Y2hJZCkgew0KICAgICAgLy8g5a6e6ZmF6aG555uu5Lit6L+Z6YeM6ZyA6KaB6LCD55SoQVBJ6I635Y+W5pWw5o2uDQogICAgICAvLyBnZXREaXNwYXRjaFRhc2tEZXRhaWwoZGlzcGF0Y2hJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAvLyAgIGNvbnN0IHsgZHJpdmVySW5mbywgY2FySW5mbywgdGFza01hdGVyaWFscywgdGFza0xvZ3MgfSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAvLyAgIHRoaXMuZHJpdmVySW5mbyA9IGRyaXZlckluZm87DQogICAgICAvLyAgIHRoaXMuY2FySW5mbyA9IGNhckluZm87DQogICAgICAvLyAgIHRoaXMudGFza01hdGVyaWFscyA9IHRhc2tNYXRlcmlhbHM7DQogICAgICAvLyAgIHRoaXMudGFza0xvZ3MgPSB0YXNrTG9nczsNCiAgICAgIC8vIH0pOw0KICAgIH0sDQogICAgaGFuZGxlU2hvd0Ryb3Bkb3duQ2hhbmdlKHZhbCkgew0KICAgICAgaWYgKCF2YWwpIHsNCiAgICAgICAgdGhpcy5mYWN0b3J5Q29uZmlybUZvcm0uZXh0cmFPcHRpb24gPSAnJzsNCiAgICAgIH0NCiAgICB9LA0KICAgIG9wZW5PcHRpb25EaWFsb2coKSB7DQogICAgICB0aGlzLm9wdGlvbkRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5sb2FkT3B0aW9ucygpOw0KICAgICAgLy8g6YeN572u6YCJ5Lit54q25oCBDQogICAgICB0aGlzLnNlbGVjdGVkT3B0aW9uID0gbnVsbDsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMub3B0aW9uVGFibGUpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLm9wdGlvblRhYmxlLmNsZWFyU2VsZWN0aW9uKCk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlT3B0aW9uU2VsZWN0aW9uKHNlbGVjdGlvbikgew0KICAgICAgLy8g5Y+q5L+d55WZ5pyA5ZCO6YCJ5Lit55qE5LiA6aG5DQogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgY29uc3QgbGFzdFNlbGVjdGVkID0gc2VsZWN0aW9uW3NlbGVjdGlvbi5sZW5ndGggLSAxXTsNCiAgICAgICAgdGhpcy4kcmVmcy5vcHRpb25UYWJsZS5jbGVhclNlbGVjdGlvbigpOw0KICAgICAgICB0aGlzLiRyZWZzLm9wdGlvblRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihsYXN0U2VsZWN0ZWQsIHRydWUpOw0KICAgICAgICB0aGlzLnNlbGVjdGVkT3B0aW9uID0gbGFzdFNlbGVjdGVkOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZE9wdGlvbiA9IHNlbGVjdGlvblswXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNvbmZpcm1PcHRpb25TZWxlY3Rpb24oKSB7DQogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRPcHRpb24pIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nkuIDkuKrpgInpobknKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmZhY3RvcnlDb25maXJtRm9ybS5leHRyYU9wdGlvbiA9IHRoaXMuc2VsZWN0ZWRPcHRpb24uYXBwbHlObzsNCg0KICAgICAgLy8gbGV0IGRpc3BhdGNoSW5mbyA9IHt9Ow0KICAgICAgLy8gZGlzcGF0Y2hJbmZvLmNhck51bSA9IHRoaXMudGFza0luZm9Gb3JtLmNhck51bTsNCiAgICAgIC8vIGRpc3BhdGNoSW5mby5pc0RpcmVjdFN1cHBseSA9IDE7DQoNCiAgICAgIC8vIGlzQWxsb3dEaXNwYXRjaChkaXNwYXRjaEluZm8pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgLy8gICBsZXQgcm93ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIC8vICAgaWYgKHJvdyA+IDApIHsNCiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlvZPliY3ovabmnInmraPlnKjmiafooYznmoTku7vliqEiKQ0KICAgICAgLy8gICAgIHJldHVybjsNCiAgICAgIC8vICAgfSBlbHNlIHsNCiAgICAgIC8vICAgICB0aGlzLm9wdGlvbkRpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mAiemhueW3suehruiupCcpOw0KICAgICAgLy8gICB9DQogICAgICAvLyAgIGNvbnNvbGUubG9nKCJ0aGlzLmlzQWxsb3dEaXNwYXRjaCIsIHJlc3BvbnNlKTsNCiAgICAgIC8vIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAvLyAgIGNvbnNvbGUuZXJyb3IoJ2Rpc3BhdGNoIGVycm9yOicsIGVycik7DQogICAgICAvLyAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgLy8gfSk7DQoNCiAgICAgIHRoaXMub3B0aW9uRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfpgInpobnlt7Lnoa7orqQnKTsNCg0KDQoNCiAgICB9LA0KICAgIGxvYWRPcHRpb25zKCkgew0KICAgICAgLy8g6L+Z6YeM5bqU6K+l6LCD55SoQVBJ6I635Y+WbGVhdmVfcGxhbuihqOeahOaVsOaNrg0KICAgICAgdGhpcy5vcHRpb25MaXN0ID0gdGhpcy5kaXJlY3RTdXBwbHlQbGFuTGlzdDsgLy8g5L2/55So55u05L6b6K6h5YiS5YiX6KGo5L2c5Li66YCJ6aG55pWw5o2uXA0KICAgICAgdGhpcy5vcHRpb25MaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGl0ZW0ucGxhblN0YXR1cyA9IHRoaXMuZ2V0UGxhblN0YXR1c1RleHQoaXRlbS5wbGFuU3RhdHVzKTsNCiAgICAgIH0pOw0KICAgICAgY29uc29sZS5sb2coIm9wdGlvbkxpc3QiLCB0aGlzLm9wdGlvbkxpc3QpDQogICAgfSwNCiAgICBnZXRCdXNpbmVzc0NhdGVnb3J5VGV4dChjYXRlZ29yeSkgew0KICAgICAgY29uc3QgY2F0ZWdvcnlNYXAgPSB7DQogICAgICAgIDE6ICfpgJrnlKjvvIjlh7rljoLkuI3ov5Tlm57vvIknLA0KICAgICAgICAxMTogJ+mAmueUqO+8iOWHuuWOgui/lOWbnu+8iScsDQogICAgICAgIDEyOiAn5aeU5aSW5Yqg5bel77yI5Ye65Y6C6L+U5Zue77yJJywNCiAgICAgICAgMjE6ICfmnInorqHliJLph4/orqHph4/vvIjot6jljLrosIPmi6jvvIknLA0KICAgICAgICAyMjogJ+efreacn++8iOi3qOWMuuiwg+aLqO+8iScsDQogICAgICAgIDIzOiAn6ZKi5p2/77yI5ZyG6ZKi77yJ77yI6Leo5Yy66LCD5ouo77yJJywNCiAgICAgICAgMzE6ICfpgJrnlKjvvIjpgIDotKfnlLPor7fvvIknDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGNhdGVnb3J5TWFwW2NhdGVnb3J5XSB8fCAn5pyq55+l57G75Z6LJzsNCiAgICB9LA0KICAgIHNlYXJjaE9wdGlvbnMoKSB7DQogICAgICAvLyDlj5blh7rlubbovazlsI/lhpkNCiAgICAgIGNvbnN0IHNlYXJjaFBsYW5ObyA9ICh0aGlzLnNlYXJjaEZvcm0ucGxhbk5vIHx8ICcnKS50b0xvd2VyQ2FzZSgpOw0KICAgICAgY29uc3Qgc2VhcmNoQXBwbHlObyA9ICh0aGlzLnNlYXJjaEZvcm0uYXBwbHlObyB8fCAnJykudG9Mb3dlckNhc2UoKTsNCiAgICAgIGNvbnN0IHNlYXJjaFJlY2VpdmVDb21wYW55ID0gKHRoaXMuc2VhcmNoRm9ybS5yZWNlaXZlQ29tcGFueSB8fCAnJykudG9Mb3dlckNhc2UoKTsNCg0KICAgICAgLy8g6L+H5rukDQogICAgICB0aGlzLm9wdGlvbkxpc3QgPSB0aGlzLmRpcmVjdFN1cHBseVBsYW5MaXN0LmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgY29uc3QgcGxhbk5vID0gKGl0ZW0ucGxhbk5vIHx8ICcnKS50b1N0cmluZygpLnRvTG93ZXJDYXNlKCk7DQogICAgICAgIGNvbnN0IGFwcGx5Tm8gPSAoaXRlbS5hcHBseU5vIHx8ICcnKS50b1N0cmluZygpLnRvTG93ZXJDYXNlKCk7DQogICAgICAgIGNvbnN0IHJlY2VpdmVDb21wYW55ID0gKGl0ZW0ucmVjZWl2ZUNvbXBhbnkgfHwgJycpLnRvU3RyaW5nKCkudG9Mb3dlckNhc2UoKTsNCg0KICAgICAgICAvLyDkuLrnqbrkuI3kvZzkuLrmnaHku7YNCiAgICAgICAgY29uc3QgbWF0Y2hQbGFuTm8gPSAhc2VhcmNoUGxhbk5vIHx8IHBsYW5Oby5pbmNsdWRlcyhzZWFyY2hQbGFuTm8pOw0KICAgICAgICBjb25zdCBtYXRjaEFwcGx5Tm8gPSAhc2VhcmNoQXBwbHlObyB8fCBhcHBseU5vLmluY2x1ZGVzKHNlYXJjaEFwcGx5Tm8pOw0KICAgICAgICBjb25zdCBtYXRjaFJlY2VpdmVDb21wYW55ID0gIXNlYXJjaFJlY2VpdmVDb21wYW55IHx8IHJlY2VpdmVDb21wYW55LmluY2x1ZGVzKHNlYXJjaFJlY2VpdmVDb21wYW55KTsNCg0KICAgICAgICByZXR1cm4gbWF0Y2hQbGFuTm8gJiYgbWF0Y2hBcHBseU5vICYmIG1hdGNoUmVjZWl2ZUNvbXBhbnk7DQogICAgICB9KTsNCg0KICAgICAgLy8g5pu05paw54q25oCB5pi+56S6DQogICAgICB0aGlzLm9wdGlvbkxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5wbGFuU3RhdHVzID0gdGhpcy5nZXRQbGFuU3RhdHVzVGV4dChpdGVtLnBsYW5TdGF0dXMpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICByZXNldFNlYXJjaCgpIHsNCiAgICAgIHRoaXMuc2VhcmNoRm9ybSA9IHsNCiAgICAgICAgcGxhbk5vOiAnJywNCiAgICAgICAgYXBwbHlObzogJycsDQogICAgICAgIHJlY2VpdmVDb21wYW55OiAnJw0KICAgICAgfTsNCiAgICAgIHRoaXMubG9hZE9wdGlvbnMoKTsgLy8g6YeN5paw5Yqg6L295omA5pyJ5pWw5o2uDQogICAgfSwNCiAgICBnZXRUYXNrVHlwZVRleHQodHlwZSkgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgMTogJ+WHuuWOgicsDQogICAgICAgIDI6ICfov5TljoInLA0KICAgICAgICAzOiAn6Leo5Yy66LCD5ouoJw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfmnKrnn6UnOw0KICAgIH0sDQogICAgLy8gLy8g5Yik5pat6KGM5piv5ZCm5Y+v6YCJDQogICAgLy8gaXNTZWxlY3RhYmxlKHJvdykgew0KICAgIC8vICAgLy8g5b2T6Zeo5Y2r56Gu6K6k5pWw6YeP5LiN5Li6MOaXtu+8jOivpeihjOWPr+mAiQ0KICAgIC8vICAgcmV0dXJuIHJvdy5kb29ybWFuUmVjZWl2ZU51bSA+IDAgJiYgdGhpcy50YXNrSW5mb0Zvcm0udGFza1N0YXR1cyAhPT0gOTsNCiAgICAvLyB9LA0KDQogICAgLy8g6KGo5qC86YCJ5oup5Y+Y5YyW5pe255qE5aSE55CG5Ye95pWwDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5zZWxlY3RlZFJvd3MgPSBzZWxlY3Rpb247DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhumdnuiuoemHj+WIhuWOguehruiupA0KICAgIGhhbmRsZU5vbk1lYXN1cmVGYWN0b3J5Q29uZmlybSgpIHsNCiAgICAgIGNvbnN0IHJvbGVzID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5yb2xlczsNCiAgICAgIGlmICghcm9sZXMuaW5jbHVkZXMoJ2xlYXZlLnVubG9hZGluZycpKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aCqOayoeaciemXqOWNq+WHuuWOguehruiupOadg+mZkCcpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBsZXQgaXNIYW5kbGVkID0gZmFsc2U7DQogICAgICB0aGlzLnNlbGVjdGVkUm93cy5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5kb29ybWFuUmVjZWl2ZU51bSAhPT0gaXRlbS5wbGFuTnVtKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfpl6jljavnoa7orqTmlbDph4/lkozorqHliJLmlbDph4/kuI3kuIDoh7TvvIzor7fmo4Dmn6UnKTsNCiAgICAgICAgICBpc0hhbmRsZWQgPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9KTsNCg0KICAgICAgaWYgKGlzSGFuZGxlZCkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIGlmICh0aGlzLnNlbGVjdGVkUm93cy5sZW5ndGggPT09IDApIHsNCiAgICAgIC8vICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6npnIDopoHnoa7orqTnmoTnianotYQnKTsNCiAgICAgIC8vICAgcmV0dXJuOw0KICAgICAgLy8gfQ0KDQogICAgICAvLyDnlJ/miJDmtL7ovabml6Xlv5cNCiAgICAgIGxldCBsZWF2ZVRhc2tMb2cgPSB7DQogICAgICAgIGxvZ1R5cGU6IDIsDQogICAgICAgIHRhc2tObzogdGhpcy50YXNrTm8sDQogICAgICAgIGFwcGx5Tm86IHRoaXMuYXBwbHlObywNCiAgICAgICAgaW5mbzogJ+WIhuWOguaOpeaUtuehruiupO+8jOehruiupOeJqei1hO+8micgKyB0aGlzLnRhc2tNYXRlcmlhbHMubWFwKGl0ZW0gPT4gaXRlbS5tYXRlcmlhbE5hbWUpLmpvaW4oJ+OAgSAnKQ0KICAgICAgfTsNCg0KICAgICAgLy8g5p6E5bu65Lu75Yqh5L+h5oGvDQogICAgICBsZXQgZmFjdG9yeVRhc2tJbmZvID0gew0KICAgICAgICBpZDogdGhpcy50YXNrSW5mb0Zvcm0uaWQsDQogICAgICAgIHVubG9hZGluZ1dvcmtObzogJ+WNuOi0p+S6uuWNoOS9jeespicsLy/lkI7nq691cGRhdGVMZWF2ZVRhc2vmlrnms5UNCiAgICAgICAgdW5sb2FkaW5nVGltZTogbmV3IERhdGUoKSwNCiAgICAgICAgdGFza1N0YXR1czogOQ0KICAgICAgfTsNCg0KICAgICAgdGhpcy5zZWxlY3RlZFJvd3MuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgLy8g6K6+572u6Z2e6K6h6YeP5YiG5Y6C56Gu6K6k5pWw6YePDQogICAgICAgIGl0ZW0uZmFjdG9yeVJlY2VpdmVOdW0gPSBpdGVtLmRvb3JtYW5SZWNlaXZlTnVtOw0KICAgICAgfSk7DQoNCiAgICAgIC8vIOaehOW7uuivt+axguWPguaVsA0KICAgICAgbGV0IHBhcmFtID0gew0KICAgICAgICB0YXNrTWF0ZXJpYWxMaXN0OiB0aGlzLnNlbGVjdGVkUm93cywgLy8g5L2/55So6YCJ5Lit55qE6KGM5pWw5o2uDQogICAgICAgIGxlYXZlTG9nOiBsZWF2ZVRhc2tMb2csDQogICAgICAgIGxlYXZlVGFzazogZmFjdG9yeVRhc2tJbmZvLA0KICAgICAgICBtZWFzdXJlRmxhZzogdGhpcy5tZWFzdXJlRmxhZw0KICAgICAgfTsNCg0KICAgICAgLy8g5Y+R6YCB6K+35rGCDQogICAgICBhZGRMZWF2ZUxvZ0FuZEVkaXRUYXNrTWF0ZXJpYWxzQW5kVXBkYXRlVGFzayhwYXJhbSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfpnZ7orqHph4/liIbljoLnoa7orqTmiJDlip8nKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tMb2dMaXN0KHRoaXMudGFza05vKTsNCiAgICAgICAgICB0aGlzLmdldFRhc2tJbmZvKCk7DQogICAgICAgICAgLy8g5riF56m66YCJ5Lit54q25oCBDQogICAgICAgICAgdGhpcy5zZWxlY3RlZFJvd3MgPSBbXTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tZXNzYWdlIHx8ICfpnZ7orqHph4/liIbljoLnoa7orqTlpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcignaGFuZGxlTm9uTWVhc3VyZUZhY3RvcnlDb25maXJtIGVycm9yOicsIGVycik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e9kee7nOW8guW4uO+8jOeojeWQjumHjeivlScpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBvcGVuTmV3VGFza1dpbmRvdygpIHsNCiAgICAgIGNvbnNvbGUubG9nKCJvcGVuTmV3VGFza1dpbmRvdyIsIHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zKTsNCiAgICAgIGxldCBkaXNwYXRjaElkID0gdGhpcy5kaXJlY3RTdXBwbHlQYXJhbXMuZGlzcGF0Y2hJZDsNCiAgICAgIGxldCBhcHBseU5vID0gQmlnSW50KHRoaXMuZGlyZWN0U3VwcGx5UGFyYW1zLmFwcGx5Tm8pOw0KICAgICAgbGV0IG1lYXN1cmVGbGFnID0gdGhpcy5kaXJlY3RTdXBwbHlQYXJhbXMubWVhc3VyZUZsYWc7DQogICAgICBsZXQgcGxhblR5cGUgPSB0aGlzLmRpcmVjdFN1cHBseVBhcmFtcy5wbGFuVHlwZTsNCiAgICAgIGxldCB0YXNrTm8gPSBCaWdJbnQodGhpcy5kaXJlY3RTdXBwbHlQYXJhbXMudGFza05vKTsNCiAgICAgIGNvbnN0IHVybCA9IGBodHRwOi8vbG9jYWxob3N0L2xlYXZlL3BsYW4vdGFzaz9kaXNwYXRjaElkPSR7ZGlzcGF0Y2hJZH0mYXBwbHlObz0ke2FwcGx5Tm99Jm1lYXN1cmVGbGFnPSR7bWVhc3VyZUZsYWd9JnBsYW5UeXBlPSR7cGxhblR5cGV9JnRhc2tObz0ke3Rhc2tOb31gOw0KICAgICAgd2luZG93Lm9wZW4odXJsLCAnX2JsYW5rJyk7DQogICAgfSwNCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["task.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy1BA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "task.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\" style=\"display: flex; align-items: center; justify-content: flex-start;\">\r\n        <h2>派车任务详情</h2>\r\n        <el-tag size=\"medium\" style=\"margin-left: 20px; margin-top: 10px;\">\r\n          任务状态： {{ getStatusText(taskInfoForm.taskStatus) }}\r\n        </el-tag>\r\n      </div>\r\n\r\n      <!-- 任务流程图部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务流程</div>\r\n        <div class=\"process-flow-container\">\r\n          <!-- <img style=\"width: 100%; max-height: 400px; object-fit: contain;\" :src=\"require('@/assets/images/task-flow-chart.png')\" /> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 通行证二维码部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">通行证二维码</div>\r\n        <div class=\"qrcode-container\">\r\n          <div ref=\"qrCode\" class=\"qrcode\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 司机信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">司机信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"姓名\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 姓名</template>\r\n            {{ taskInfoForm.driverName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">\r\n            <template slot=\"label\"><i class=\"el-icon-mobile-phone\"></i> 手机号</template>\r\n            {{ taskInfoForm.mobilePhone }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"身份证号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 身份证号</template>\r\n            {{ taskInfoForm.idCardNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"性别\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 性别</template>\r\n            {{ taskInfoForm.sex === 1 ? '男' : '女' }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"所属单位\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 所属单位</template>\r\n            {{ taskInfoForm.companyName }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <!-- 司机照片和证件照片 -->\r\n        <div class=\"driver-photos\"\r\n          v-if=\"driverInfo.photo || driverInfo.driverLicenseImgs || driverInfo.vehicleLicenseImgs\">\r\n          <div class=\"photo-item\" v-if=\"driverInfo.photo\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 司机照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.faceImg\" alt=\"司机照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.faceImg\" fit=\"contain\" fallback=\"\"\r\n                :preview-src-list=\"[taskInfoForm.faceImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.driverLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 驾驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.driverLicenseImg\" alt=\"驾驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.driverLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.driverLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n          <div class=\"photo-item\" v-if=\"driverInfo.vehicleLicenseImgs\">\r\n            <h4><i class=\"el-icon-picture-outline\"></i> 行驶证照片</h4>\r\n            <div class=\"photo-container\">\r\n              <!-- <img :src=\"taskInfoForm.drivingLicenseImg\" alt=\"行驶证照片\"> -->\r\n\r\n              <el-image style=\"width: 200px; height: 200px\" :src=\"taskInfoForm.drivingLicenseImg\" fit=\"contain\"\r\n                fallback=\"\" :preview-src-list=\"[taskInfoForm.drivingLicenseImg]\">\r\n                <template #error>\r\n                  <div style=\"width: 100%; height: 100%;\"></div> <!-- 空白区域 -->\r\n                </template>\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 车辆信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">车辆信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"车牌号\" v-if=\"taskInfoForm.carNum != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 车牌号</template>\r\n            <el-tag type=\"primary\">{{ taskInfoForm.carNum }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车牌颜色\" v-if=\"taskInfoForm.licensePlateColor != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-takeaway-box\"></i> 车牌颜色</template>\r\n            {{ taskInfoForm.licensePlateColor }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆道路运输证号\" v-if=\"taskInfoForm.trailerId != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"挂车号牌\" v-if=\"taskInfoForm.trailerNumber\">\r\n            <template slot=\"label\"><i class=\"el-icon-truck\"></i> 挂车号牌</template>\r\n            <el-tag type=\"info\">{{ taskInfoForm.trailerNumber }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"挂车道路运输证号\" v-if=\"taskInfoForm.trailerId\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 挂车运输证号</template>\r\n            {{ taskInfoForm.trailerId }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"轴型\" v-if=\"taskInfoForm.axisType != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-data-line\"></i> 轴型</template>\r\n            {{ taskInfoForm.axisType }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"货车自重\" v-if=\"taskInfoForm.driverWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-heavy-rain\"></i> 货车自重</template>\r\n            {{ taskInfoForm.driverWeight }} kg\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"车货总质量限值\" v-if=\"taskInfoForm.maxWeight != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-opportunity\"></i> 总质量限值</template>\r\n            {{ taskInfoForm.maxWeight }} kg\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆排放标准\" v-if=\"taskInfoForm.vehicleEmissionStandards != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-magic-stick\"></i> 排放标准</template>\r\n            <el-tag :type=\"getEmissionStandardsTagType(taskInfoForm.vehicleEmissionStandards)\">\r\n              {{ getEmissionStandardsText(taskInfoForm.vehicleEmissionStandards) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"发动机号\" v-if=\"taskInfoForm.engineNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-set-up\"></i> 发动机号</template>\r\n            {{ taskInfoForm.engineNumber }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"车辆识别代码\" v-if=\"taskInfoForm.vinNumber != null\">\r\n            <template slot=\"label\"><i class=\"el-icon-document-checked\"></i> 车辆识别代码</template>\r\n            {{ taskInfoForm.vinNumber }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 任务物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"taskMaterials\" style=\"width: 100%\" border @selection-change=\"handleSelectionChange\">\r\n          <!-- <el-table-column type=\"selection\" width=\"55\" v-if=\"measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"150\">\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0\" -->\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫出厂确认数量\" width=\"230\"\r\n            v-if=\"taskInfoForm.taskStatus >= 4 && measureFlag == 0 && taskInfoForm.taskType != 2\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 4\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNumIn\" label=\"门卫入厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskType !== 1 && taskInfoForm.taskStatus >= 5\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.doormanReceiveNumIn\" :min=\"0\" controls-position=\"right\"\r\n                :disabled=\"!isdoorMan && taskInfoForm.taskStatus !== 5\" />\r\n            </template>\r\n          </el-table-column>\r\n          <!-- v-if=\"measureFlag == 0 && taskInfoForm.taskType == 2\" -->\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"分厂确认数量\" width=\"230\"\r\n            v-if=\"measureFlag == 0 && taskInfoForm.taskStatus > 7 && (taskInfoForm.taskType == 3 || taskInfoForm.taskType == 2)\">\r\n            <!-- <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.doormanReceiveNum\" :min=\"0\" controls-position=\"right\" disabled />\r\n            </template> -->\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\">\r\n          </el-table-column>\r\n\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5 && (measureFlag == 0 && taskInfoForm.taskType == 2 && taskInfoForm.taskStatus == 7)\" -->\r\n          <!-- <el-table-column v-if=\"measureFlag == 0 && (taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5)\"\r\n            label=\"操作\" width=\"200\" fixed=\"right\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px;\">\r\n\r\n                <div v-if=\"editingRow === scope.row\">\r\n                  <el-button size=\"mini\" type=\"success\" @click=\"saveDoorManRow(scope.row)\">保存</el-button>\r\n                  <el-button size=\"mini\" @click=\"cancelDoorManEdit(scope.row)\">取消</el-button>\r\n                </div>\r\n\r\n                <div v-else>\r\n                  <el-button v-hasPermi=\"['leave:task:doorManConfirm']\" size=\"mini\" type=\"primary\"\r\n                    @click=\"editDoorManRow(scope.row)\">门卫编辑</el-button>\r\n                </div>\r\n              </div>\r\n            </template>\r\n          </el-table-column> -->\r\n        </el-table>\r\n\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 4\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRow\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫出厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"btn-wrapper\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 5\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"saveDoorManRowIn\" class=\"dispatch-btn\">\r\n            <!-- :disabled=\"!hasSelectedItems\" -->\r\n            门卫入厂确认\r\n          </el-button>\r\n        </div>\r\n        <div class=\"button-container\" v-if=\"measureFlag == 0 && taskInfoForm.taskStatus == 7\">\r\n          <el-button type=\"primary\" @click=\"handleNonMeasureFactoryConfirm\">\r\n            分厂确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1\">\r\n        <div class=\"section-title\">计量信息</div>\r\n        <div class=\"info-footer\" style=\"margin-top: 20px;\" v-if=\"measureFlag == 1\">\r\n          <el-descriptions :column=\"3\" border>\r\n            <el-descriptions-item label=\"皮重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tare != null\">\r\n              {{ taskInfoForm.tare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.gross != null\">\r\n              {{ taskInfoForm.gross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.netWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.tareTime != null\">\r\n              {{ taskInfoForm.tareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.grossTime != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.netWeight != null\">\r\n              {{ taskInfoForm.grossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secTare != null\">\r\n              {{ taskInfoForm.secTare + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重（复磅）\" :label-style=\"{ width: '200px' }\" v-if=\"taskInfoForm.secGross != null\">\r\n              {{ taskInfoForm.secGross + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeight != null\">\r\n              {{ taskInfoForm.secNetWeight + ' 吨' }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"皮重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secTareTime != null\">\r\n              {{ taskInfoForm.secTareTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"毛重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secGrossTime != null\">\r\n              {{ taskInfoForm.secGrossTime }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"净重时间（复磅）\" :label-style=\"{ width: '200px' }\"\r\n              v-if=\"taskInfoForm.secNetWeightTime != null\">\r\n              {{ taskInfoForm.secNetWeightTime }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <!-- v-if=\"taskInfoForm.taskStatus == 4 || taskInfoForm.taskStatus == 5\" -->\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 4\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫出厂确认\r\n            </el-button>\r\n          </div>\r\n          <div class=\"btn-wrapper\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 5\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"handleDoorManMeasureConfirm\" class=\"dispatch-btn\">\r\n              门卫入厂确认\r\n            </el-button>\r\n          </div>\r\n          <!-- 新增分厂确认按钮 -->\r\n          <!-- <div class=\"btn-wrapper\">\r\n            <el-button type=\"primary\" size=\"medium\" @click=\"openFactoryConfirmDialog\" class=\"dispatch-btn\">\r\n              分厂确认\r\n            </el-button>\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 可编辑的出库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 2\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialName).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"taskMaterials.map(item => item.materialSpec).join(' ')\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\" v-if=\"taskInfoForm.taskType\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSpec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutTotal\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutTotalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutProcessType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutHeatNo\" placeholder=\"请输入炉号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.stockOutSteelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-select v-model=\"factoryConfirmForm.stockOutAxles\" placeholder=\"请选择轴数\">\r\n                  <el-option label=\"2\" value=\"2\"></el-option>\r\n                  <el-option label=\"3\" value=\"3\"></el-option>\r\n                  <el-option label=\"4\" value=\"4\"></el-option>\r\n                  <el-option label=\"5\" value=\"5\"></el-option>\r\n                  <el-option label=\"6轴标准\" value=\"6轴标准\"></el-option>\r\n                  <el-option label=\"6轴非标准\" value=\"6轴非标准\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.stockOutRemark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitStockOutConfirm\" size=\"medium\" class=\"dispatch-btn\">确认出库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的出库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 2 && taskInfoForm.taskType !== 2 && taskInfoForm.isDirectSupply != 3\">\r\n        <div class=\"section-title\">出库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"皮重(t)\">\r\n                <el-input :value=\"taskInfoForm.tare\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"planForm.planType == 3\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物料规格\">\r\n                <el-input :value=\"materialSpecs\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.stockOutSpec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.stockOutTotals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.stockOutProcessType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.stockOutHeatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.stockOutSteelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"轴数\">\r\n                <el-input :value=\"taskInfoForm.stockOutAxles\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.stockOutRemark\" disabled></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 可编辑的入库信息表单 -->\r\n      <div class=\"section-container\" v-if=\"measureFlag == 1 && taskInfoForm.taskStatus == 7\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"factoryConfirmForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input v-model=\"factoryConfirmForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input v-model=\"factoryConfirmForm.carNum\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.secGross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input v-model=\"factoryConfirmForm.gross\" placeholder=\"\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.sourceCompany\" placeholder=\"请输入车牌号\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input v-model=\"factoryConfirmForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-select v-model=\"factoryConfirmForm.processType\" placeholder=\"请选择加工类型\" filterable\r\n                  :filter-method=\"filterProcessType\">\r\n                  <el-option v-for=\"item in filteredProcessTypeOptions\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input v-model=\"factoryConfirmForm.steelGrade\" placeholder=\"请输入钢种\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input v-model=\"factoryConfirmForm.spec1Length\" placeholder=\"请输入规格\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input v-model=\"factoryConfirmForm.total\" placeholder=\"请输入总数\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数单位\">\r\n                <el-select v-model=\"factoryConfirmForm.totalUnit\" placeholder=\"请选择总数单位\">\r\n                  <el-option label=\"件\" value=\"件\"></el-option>\r\n                  <el-option label=\"支\" value=\"支\"></el-option>\r\n                  <el-option label=\"张\" value=\"张\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input v-model=\"factoryConfirmForm.heatNo\" placeholder=\"请输入炉号/批号\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重(t)\">\r\n                <el-input v-model=\"factoryConfirmForm.deductWeight\" placeholder=\"请输入扣重\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"出库备注\">\r\n            <el-input type=\"textarea\" v-model=\"factoryConfirmForm.remark\" placeholder=\"请输入出库备注\"></el-input>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"是否直供\" v-if=\"taskInfoForm.taskType == 2\">\r\n            <el-checkbox v-model=\"factoryConfirmForm.showDropdown\" @change=\"handleShowDropdownChange\">是否直供</el-checkbox>\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"factoryConfirmForm.showDropdown\" label=\"直供申请单号\">\r\n            <el-input v-model=\"factoryConfirmForm.extraOption\" placeholder=\"请选择直供申请单号\" readonly style=\"width: 300px;\">\r\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"openOptionDialog\"></el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <div class=\"btn-wrapper\">\r\n          <el-button type=\"primary\" @click=\"submitFactoryConfirm\" size=\"medium\" class=\"dispatch-btn\">确认入库</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只读的入库信息表单 -->\r\n      <div class=\"section-container\"\r\n        v-if=\"measureFlag == 1 && taskInfoForm.taskStatus > 7 && taskInfoForm.taskType !== 1\">\r\n        <div class=\"section-title\">入库信息</div>\r\n\r\n        <el-form :model=\"taskInfoForm\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"计划号\">\r\n                <el-input :value=\"taskInfoForm.planNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"车牌号\">\r\n                <el-input :value=\"taskInfoForm.carNum\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"物资名称\">\r\n                <el-input :value=\"materialNames\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <!-- <div\r\n                v-if=\"taskInfoForm.isDirectSupply == 0 || taskInfoForm.isDirectSupply == null || taskInfoForm.isDirectSupply == ''\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <div v-if=\"taskInfoForm.isDirectSupply == 1\">\r\n                <el-form-item label=\"毛重(t)\">\r\n                  <el-input :value=\"taskInfoForm.gross\" disabled></el-input>\r\n                </el-form-item>\r\n              </div> -->\r\n\r\n              <el-form-item label=\"毛重(t)\">\r\n                <el-input :value=\"taskInfoForm.secGross\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"供货单位\">\r\n                <el-input :value=\"planForm.sourceCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"收货单位\">\r\n                <el-input :value=\"planForm.receiveCompany\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"加工类型\">\r\n                <el-input :value=\"taskInfoForm.processType\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"钢种\">\r\n                <el-input :value=\"taskInfoForm.steelGrade\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"规格\">\r\n                <el-input :value=\"taskInfoForm.spec1Length\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"总数\">\r\n                <el-input :value=\"taskInfoForm.totals\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"炉号/批号\">\r\n                <el-input :value=\"taskInfoForm.heatNo\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"扣重\">\r\n                <el-input :value=\"taskInfoForm.deductWeight\" disabled></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-form-item label=\"备注\">\r\n            <el-input type=\"textarea\" :value=\"taskInfoForm.remark\" disabled></el-input>\r\n          </el-form-item>\r\n\r\n\r\n          <el-form-item v-if=\"taskInfoForm.directSupplyTaskNo\" label=\"直供对应任务单号\">\r\n            <el-input :value=\"taskInfoForm.directSupplyTaskNo\" disabled style=\"width: 300px;\"></el-input>\r\n            <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n              @click=\"openNewTaskWindow\">前往任务单号</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">任务日志</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in taskLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <div class=\"form-footer\">\r\n        <el-button @click=\"cancel\">返 回</el-button>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 选项弹窗 -->\r\n    <el-dialog title=\"选择直供申请单号\" :visible.sync=\"optionDialogVisible\" width=\"1600px\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"demo-form-inline\">\r\n        <el-form-item label=\"计划号\">\r\n          <el-input v-model=\"searchForm.planNo\" placeholder=\"请输入计划号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"申请编号\">\r\n          <el-input v-model=\"searchForm.applyNo\" placeholder=\"请输入申请编号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货单位\">\r\n          <el-input v-model=\"searchForm.receiveCompany\" placeholder=\"请输入收货单位\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"searchOptions\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewWindow\">直供对应任务号\r\n          </el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table :data=\"optionList\" style=\"width: 100%\" @selection-change=\"handleOptionSelection\" ref=\"optionTable\">\r\n        <el-table-column type=\"selection\" width=\"55\" />\r\n        <el-table-column prop=\"planNo\" label=\"计划号\" width=\"150\" />\r\n        <el-table-column prop=\"applyNo\" label=\"申请编号\" width=\"150\" />\r\n        <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"150\" />\r\n        <el-table-column prop=\"materialSpec\" label=\"物料规格\" width=\"120\" />\r\n        <el-table-column prop=\"sourceCompany\" label=\"申请单位\" width=\"150\" />\r\n        <el-table-column prop=\"receiveCompany\" label=\"收货单位\" width=\"150\" />\r\n        <el-table-column prop=\"plannedAmount\" label=\"计划量/t\" width=\"150\" />\r\n        <el-table-column prop=\"startTime\" label=\"开始时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"endTime\" label=\"结束时间\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.create_time) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"planStatus\" label=\"状态\" width=\"150\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"optionDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmOptionSelection\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getTask, getTaskByTaskNo, getTaskmaterials, getProcessList, getDirectSupplyPlans, getDirectSupplyPlanAndTaskDetail, handleUnload, handleStockOut, isAllowDispatch, getPlanMaterials, editTaskmaterials, getTaskLogs, addLeaveLog, updateTask, addLeaveLogAndEditTaskMaterialsAndUpdateTask } from \"@/api/leave/task\";\r\nimport { detailPlan } from \"@/api/leave/plan\";\r\nimport { listXctgDriverCar, getXctgDriverCar, delXctgDriverCar, addXctgDriverCar, updateXctgDriverCar, exportXctgDriverCar } from \"@/api/truck/common/xctgDriverCar\";\r\nimport { Message } from \"element-ui\";\r\nimport QRCode from \"qrcodejs2\";\r\nimport { readGateLocationFileSilent } from \"@/utils/fileReader\";\r\n\r\n\r\nexport default {\r\n  name: \"DispatchTaskDetail\",\r\n  data() {\r\n    return {\r\n      factoryConfirmDialogVisible: false,\r\n      factoryConfirmForm: {\r\n        companyName: '',\r\n        taskNo: '',\r\n        applyNo: '',\r\n        planNo: '',\r\n        taskType: null,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: null,\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9, // 完成状态\r\n        carNum: '', // 车牌号\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        handledMaterialName: '',\r\n        sourceCompany: '',\r\n        receiveCompany: '',\r\n        showDropdown: false,\r\n        extraOption: '',\r\n        deductWeight: null, // 添加扣重字段\r\n      },\r\n      optionDialogVisible: false,\r\n      searchForm: {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      },\r\n      optionList: [],\r\n      editDoorManStatus: false,\r\n      editFactoryStatus: false,\r\n      // 司机信息\r\n      driverInfo: {\r\n        id: 1,\r\n        name: '王小明',\r\n        idCard: '110101199001010001',\r\n        phone: '13800138000',\r\n        gender: '1',\r\n        company: '北京运输有限公司',\r\n        photo: 'https://via.placeholder.com/150',\r\n        driverLicenseImgs: 'https://via.placeholder.com/300x200',\r\n        vehicleLicenseImgs: 'https://via.placeholder.com/300x200'\r\n      },\r\n\r\n      // 车辆信息\r\n      carInfo: {},\r\n\r\n      // 任务物资列表\r\n      taskMaterials: [],\r\n\r\n      // 任务日志列表\r\n      taskLogs: [],\r\n\r\n      // 申请编号\r\n      applyNo: null,\r\n\r\n      isdoorMan: false,\r\n\r\n      // 派车任务ID\r\n      dispatchId: null,\r\n\r\n      taskInfoForm: {},\r\n\r\n      measureFlag: null,\r\n\r\n      backupTaskMaterials: null,\r\n      taskNo: null,\r\n\r\n      selectedOption: null,\r\n\r\n      planForm: {},\r\n\r\n      processTypeOptions: [], // 动态加载的加工类型选项\r\n\r\n      filteredProcessTypeOptions: [], // 过滤后的加工类型选项\r\n\r\n      searchProcessTypeQuery: '',// 搜索框的值\r\n\r\n      directSupplyPlanList: [], // 直供计划列表\r\n\r\n      editingRow: null,\r\n\r\n      selectedRows: [], // 添加选中行数据数组\r\n\r\n      directSupplyParams: {}\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    displayProcessTypeOptions() {\r\n      return this.searchProcessTypeQuery ? this.filteredProcessTypeOptions : this.processTypeOptions;\r\n    },\r\n\r\n    // 是否有选中的项\r\n    hasSelectedItems() {\r\n      return this.selectedRows.length > 0;\r\n    },\r\n\r\n    // 添加计算属性\r\n    materialNames() {\r\n      return this.taskMaterials.map(item => item.materialName).join(' ');\r\n    },\r\n\r\n    materialSpecs() {\r\n      return this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n    }\r\n  },\r\n\r\n  activated() {\r\n    console.log(\"activated执行\");\r\n    this.resetTaskInfoForm();\r\n\r\n    // 获取路由参数 - 支持两种方式：query参数和路径参数\r\n    let taskNo = this.$route.params.taskNo || this.$route.query.taskNo;\r\n\r\n    if (taskNo) {\r\n      // 新的方式：通过taskNo获取所有参数\r\n      this.taskNo = taskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeDataByTaskNo();\r\n    } else {\r\n      // 兼容旧的方式：从query参数获取\r\n      const { dispatchId, applyNo, measureFlag, planType, taskNo: queryTaskNo } = this.$route.query;\r\n      this.dispatchId = dispatchId;\r\n      this.applyNo = applyNo;\r\n      this.measureFlag = measureFlag;\r\n      console.log(\"this.measureFlag\", this.measureFlag)\r\n      this.planType = planType;\r\n      this.taskNo = queryTaskNo;\r\n      console.log(\"taskNo\", this.taskNo);\r\n      this.validDoorMan();\r\n\r\n      // 使用 async/await 确保按顺序执行\r\n      this.initializeData();\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    /**\r\n     * 调用带大门位置信息的接口\r\n     * @param {Object} param 接口参数\r\n     * @returns {Promise} 接口调用结果\r\n     */\r\n    async callApiWithGateLocation(param) {\r\n      try {\r\n        // 尝试读取大门位置文件\r\n        const gateLocationData = await readGateLocationFileSilent();\r\n\r\n        // 如果成功读取到大门位置信息，添加到参数中\r\n        if (gateLocationData) {\r\n          param.gateLocationJson = JSON.stringify(gateLocationData);\r\n          console.log('成功读取大门位置文件，已添加到请求参数中');\r\n        } else {\r\n          console.warn('未能读取大门位置文件，将使用服务器默认配置');\r\n        }\r\n\r\n        // 调用接口\r\n        return await addLeaveLogAndEditTaskMaterialsAndUpdateTask(param);\r\n      } catch (error) {\r\n        console.error('调用接口失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    getDirectSupplyPlanAndTask() {\r\n\r\n\r\n      let leaveTask0 = {\r\n        taskNo: this.taskInfoForm.directSupplyTaskNo\r\n      }\r\n\r\n      getDirectSupplyPlanAndTaskDetail(leaveTask0).then(res => {\r\n        console.log(\"getDirectSupplyPlanAndTaskDetail\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyParams.dispatchId = res.rows[0].id;\r\n          this.directSupplyParams.applyNo = res.rows[0].applyNo;\r\n          this.directSupplyParams.taskNo = res.rows[0].taskNo;\r\n          this.directSupplyParams.measureFlag = res.rows[1].measureFlag;\r\n          this.directSupplyParams.planType = res.rows[1].planType;\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getDirectSupplyPlanAndTaskDetail error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      });\r\n\r\n    },\r\n\r\n    validDoorMan() {\r\n      this.$store.getters.roles.forEach(item => {\r\n        if (item == 'leave.quard') {\r\n          this.isdoorMan = true;\r\n        }\r\n      });\r\n      console.log(\"isdoorMan\", this.isdoorMan)\r\n    },\r\n    async initializeData() {\r\n      try {\r\n        // 等待所有异步操作完成\r\n        await this.getTaskInfo();\r\n        await this.getTaskmaterialList(this.taskNo);\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    async initializeDataByTaskNo() {\r\n      try {\r\n        // 通过taskNo获取任务信息\r\n        await this.getTaskInfoByTaskNo();\r\n\r\n        // 通过applyNo获取计划信息\r\n        await this.getPlanInfo(this.applyNo);\r\n\r\n        // 获取任务物资列表\r\n        await this.getTaskmaterialList(this.taskNo);\r\n\r\n        // 在所有数据加载完成后执行\r\n        this.uploadFactoryConfirmForm();\r\n\r\n        // 其他初始化操作\r\n        this.getTaskLogList(this.taskNo);\r\n        this.getProcessType();\r\n\r\n        //查询直供对应计划、任务详情\r\n        this.getDirectSupplyPlanAndTask();\r\n      } catch (error) {\r\n        console.error('Error initializing data by taskNo:', error);\r\n        this.$message.error('数据加载失败，请刷新页面重试');\r\n      }\r\n    },\r\n\r\n    uploadFactoryConfirmForm() {\r\n      // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n      this.taskMaterials.forEach(item => {\r\n        item.doormanReceiveNum = item.planNum;\r\n        console.log(\"item.planType\", this.planForm.planType);\r\n        if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n          item.doormanReceiveNumIn = item.planNum;\r\n        }\r\n      });\r\n\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      let materialSpecs = this.taskMaterials.map(item => item.materialSpec).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        driverName: this.taskInfoForm.driverName,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        materialSpecs: materialSpecs,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n    },\r\n\r\n    openNewWindow() {\r\n      const newWindowUrl = 'http://localhost/leave/leavePlanList'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    //获取可以直供的计划\r\n    async getDirectSupplyList() {\r\n      try {\r\n        let leavePlan = {\r\n          sourceCompany: this.planForm.sourceCompany,\r\n          planType: 3,\r\n        }\r\n        console.log(\"获取可以直供的计划\", leavePlan)\r\n\r\n        const res = await getDirectSupplyPlans(leavePlan);\r\n        console.log(\"getDirectSupplyPlans\", res)\r\n        if (res.code == 200) {\r\n          this.directSupplyPlanList = res.rows;\r\n          // //查询每个计划的物资\r\n          // for (const item of this.directSupplyPlanList) {\r\n          //   console.log(\"item\", item)\r\n          //   let leavePlanMaterial = {\r\n          //     applyNo: item.applyNo\r\n          //   };\r\n          //   const response = await getPlanMaterials(leavePlanMaterial);\r\n          //   if (response.code == 200) {\r\n          //     console.log(\"getPlanMaterials\", response)\r\n          //     item.materialName = response.rows[0].materialName;\r\n          //     item.materialSpec = response.rows[0].materialSpec;\r\n          //   } else {\r\n          //     this.$message.error(response.message || '获取计划物资失败');\r\n          //   }\r\n          // }\r\n        } else {\r\n          this.$message.error(res.message || '获取计划列表失败');\r\n        }\r\n      } catch (err) {\r\n        console.error('getDirectSupplyPlans error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n        throw err;\r\n      }\r\n    },\r\n    filterProcessType(query) {\r\n      this.searchProcessTypeQuery = query;\r\n\r\n      if (this.searchProcessTypeQuery) {\r\n        console.log(\"processTypeOptions\", this.processTypeOptions)\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions.filter(item =>\r\n          item.value.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredProcessTypeOptions = this.processTypeOptions;\r\n      }\r\n    },\r\n    getProcessType() {\r\n      getProcessList().then(res => {\r\n        console.log(\"getProcessList\", res)\r\n        if (res.code == 200) {\r\n          this.processTypeOptions = res.rows.map(item => ({\r\n            value: item.processname,\r\n            label: item.processname\r\n          }));\r\n          this.filteredProcessTypeOptions = this.processTypeOptions; // 初始化过滤后的选项\r\n        } else {\r\n          this.$message.error(res.message || '获取加工类型失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('getProcessList error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    async getPlanInfo(applyNo) {\r\n      try {\r\n        const response = await detailPlan(applyNo);\r\n        console.log(\"detailPlan\", response);\r\n        this.planForm = response.data;\r\n\r\n        // 从计划信息中获取planType和measureFlag\r\n        this.planType = this.planForm.planType;\r\n        this.measureFlag = this.planForm.measureFlag;\r\n        console.log(\"this.planType\", this.planType);\r\n        console.log(\"this.measureFlag\", this.measureFlag);\r\n\r\n        await this.getDirectSupplyList();\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getPlanInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    openFactoryConfirmDialog() {\r\n      let handledMaterialName = this.taskMaterials.map(item => item.materialName).join(' ');\r\n      // 初始化表单数据\r\n      this.factoryConfirmForm = {\r\n        companyName: this.taskInfoForm.companyName,\r\n        gross: this.taskInfoForm.gross,\r\n        secGross: this.taskInfoForm.secGross,\r\n        tare: this.taskInfoForm.tare,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        planNo: this.taskInfoForm.planNo,\r\n        unloadingWorkNo: '',\r\n        unloadingTime: new Date(),\r\n        spec1Length: null,\r\n        spec2Width: null,\r\n        totals: '',\r\n        total: '',\r\n        totalUnit: '',\r\n        processType: '',\r\n        heatNo: '',\r\n        steelGrade: '',\r\n        axles: '',\r\n        remark: '',\r\n        taskStatus: 9,\r\n        carNum: this.taskInfoForm.carNum, // 初始化车牌号\r\n        handledMaterialName: handledMaterialName,\r\n        sourceCompany: this.planForm.sourceCompany,\r\n        receiveCompany: this.planForm.receiveCompany,\r\n        showDropdown: false, // 是否启用额外选项\r\n        extraOption: '', // 额外选项的值\r\n        // 出库信息\r\n        stockOutSpec1Length: null,\r\n        stockOutSpec2Width: null,\r\n        stockOutTotals: '',\r\n        stockOutTotalUnit: '',\r\n        stockOutTotal: '',\r\n        stockOutProcessType: '',\r\n        stockOutHeatNo: '',\r\n        stockOutSteelGrade: '',\r\n        stockOutAxles: '',\r\n        stockOutRemark: '',\r\n        deductWeight: null, // 添加扣重字段初始化\r\n      };\r\n      this.factoryConfirmDialogVisible = true;\r\n    },\r\n    submitFactoryConfirm() {\r\n      if (this.factoryConfirmForm.showDropdown == true) {\r\n        if (this.factoryConfirmForm.extraOption == null || this.factoryConfirmForm.extraOption == '') {\r\n          this.$message.error('请选择额外选项');\r\n          return;\r\n        }\r\n      }\r\n\r\n      let submitData = {};\r\n      if (this.taskInfoForm.isDirectSupply == 3) {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 3,\r\n            planNo: this.taskInfoForm.planNo,\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      } else {\r\n        // 构建提交数据\r\n        submitData = {\r\n          leaveTask: {\r\n            id: this.dispatchId,\r\n            taskNo: this.taskNo,\r\n            applyNo: this.applyNo,\r\n            planNo: this.taskInfoForm.planNo,\r\n            //入库信息\r\n            spec1Length: this.factoryConfirmForm.spec1Length,\r\n            spec2Width: this.factoryConfirmForm.spec2Width,\r\n            totals: this.factoryConfirmForm.total + this.factoryConfirmForm.totalUnit,\r\n            processType: this.factoryConfirmForm.processType,\r\n            heatNo: this.factoryConfirmForm.heatNo,\r\n            steelGrade: this.factoryConfirmForm.steelGrade,\r\n            axles: this.factoryConfirmForm.axles,\r\n            remark: this.factoryConfirmForm.remark,\r\n            carNum: this.taskInfoForm.carNum,\r\n            driverName: this.taskInfoForm.driverName,\r\n            isDirectSupply: 0, // 默认不是直供\r\n            deductWeight: this.factoryConfirmForm.deductWeight, // 添加扣重字段\r\n            directSupplyTaskNo: this.factoryConfirmForm.extraOption,\r\n            // 出库信息\r\n            stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n            stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n            stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n            stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n            stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n            stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n            stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n            stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n            // 更改任务状态: 9\r\n            // todo 任务状态如何变化\r\n            taskStatus: 8,\r\n            taskType: this.taskInfoForm.taskType,\r\n          },\r\n          leavePlan: this.planForm,\r\n          leaveTaskMaterial: this.taskMaterials[0],\r\n        };\r\n      }\r\n\r\n\r\n\r\n      let directSupplyTask = {\r\n        //taskNo后台雪花生成\r\n        applyNo: this.factoryConfirmForm.extraOption,\r\n        taskType: 3,\r\n        taskStatus: 7,\r\n        secGross: this.taskInfoForm.secGross,\r\n        secGrossTime: this.taskInfoForm.secGrossTime,\r\n        planNo: this.taskInfoForm.planNo,\r\n        driverName: this.taskInfoForm.driverName,\r\n        sex: this.taskInfoForm.sex,\r\n        mobilePhone: this.taskInfoForm.mobilePhone,\r\n        idCardNo: this.taskInfoForm.idCardNo,\r\n        carNum: this.taskInfoForm.carNum,\r\n        vehicleEmissionStandards: this.taskInfoForm.vehicleEmissionStandards,\r\n        faceImg: this.taskInfoForm.faceImg,\r\n        drivingLicenseImg: this.taskInfoForm.drivingLicenseImg,\r\n        driverLicenseImg: this.taskInfoForm.driverLicenseImg,\r\n        companyName: this.taskInfoForm.companyName,\r\n        isDirectSupply: 3\r\n      };\r\n\r\n      let directSupplyTaskMaterialList = this.taskMaterials;\r\n\r\n      if (this.factoryConfirmForm.showDropdown == true && this.factoryConfirmForm.extraOption != null && this.factoryConfirmForm.extraOption != '') {\r\n        submitData.leaveTask.isDirectSupply = 1; // 设置为直供\r\n        submitData.directSupplyTask = directSupplyTask;\r\n        submitData.directSupplyTaskMaterialList = directSupplyTaskMaterialList;\r\n      }\r\n\r\n      handleUnload(submitData).then(res => {\r\n        console.log(\"handleUnload\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认入库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认入库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    submitStockOutConfirm() {\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有确认出库权限');\r\n        return;\r\n      }\r\n      // 构建提交数据\r\n      let submitData = {\r\n        leaveTask: {\r\n          //todo 计量系统补充信息待完善\r\n          id: this.dispatchId,\r\n          taskNo: this.taskNo,\r\n          applyNo: this.applyNo,\r\n          planNo: this.taskInfoForm.planNo,\r\n          // 出库信息\r\n          stockOutSpec1Length: this.factoryConfirmForm.stockOutSpec1Length,\r\n          stockOutSpec2Width: this.factoryConfirmForm.stockOutSpec2Width,\r\n          stockOutTotals: this.factoryConfirmForm.stockOutTotal + this.factoryConfirmForm.stockOutTotalUnit,\r\n          stockOutProcessType: this.factoryConfirmForm.stockOutProcessType,\r\n          stockOutHeatNo: this.factoryConfirmForm.stockOutHeatNo,\r\n          stockOutSteelGrade: this.factoryConfirmForm.stockOutSteelGrade,\r\n          stockOutAxles: this.factoryConfirmForm.stockOutAxles,\r\n          stockOutRemark: this.factoryConfirmForm.stockOutRemark,\r\n\r\n          // 更改任务状态: 9\r\n          taskStatus: 3,\r\n          carNum: this.taskInfoForm.carNum,\r\n        },\r\n        leavePlan: this.planForm,\r\n        leaveTaskMaterial: this.taskMaterials[0],\r\n      };\r\n\r\n      handleStockOut(submitData).then(res => {\r\n        console.log(\"handleStockOut\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('确认出库成功');\r\n          this.factoryConfirmDialogVisible = false;\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '确认出库失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDirectSupply error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n    handleFactoryConfirm() {\r\n      if (this.editFactoryStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n\r\n      //todo\r\n      //生成派车日志\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '分厂确认数量';\r\n\r\n\r\n      let factoryTaskInfo = {}\r\n      //todo 出入场\r\n      factoryTaskInfo.id = this.taskInfoForm.id\r\n      factoryTaskInfo.unloadingWorkNo = '卸货人占位符'\r\n      factoryTaskInfo.unloadingTime = new Date()\r\n      factoryTaskInfo.taskStatus = 9\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = factoryTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '分厂确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n\r\n\r\n    handleDoorManConfirm() {\r\n      if (this.editDoorManStatus) {\r\n        this.$message.warning('请先保存');\r\n        return\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n\r\n\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      // this.taskMaterials.map(item => {\r\n      //   editTaskmaterials(item);\r\n      // })\r\n      //todo\r\n      // let leaveTaskLog = {};\r\n      // leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫确认数量';\r\n      // addLeaveLog(leaveTaskLog);\r\n      // this.getTaskLogList(this.taskNo);\r\n\r\n      // let doorManTaskInfo = {}\r\n      // doorManTaskInfo.id = this.taskInfoForm.id\r\n      // if (this.taskInfoForm.taskType == 1) {\r\n      //   doorManTaskInfo.taskStatus = 9\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n      //   doorManTaskInfo.taskStatus = 5\r\n      //   doorManTaskInfo.leaveTime = new Date()\r\n      //   //离厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 7\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n      //   doorManTaskInfo.taskStatus = 6\r\n      //   doorManTaskInfo.enterTime = new Date()\r\n      //   //出厂大门\r\n      // }\r\n      // updateTask(doorManTaskInfo);\r\n      // this.$message.success('门卫确认成功');\r\n\r\n      // setTimeout(() => {\r\n      //   this.getTaskInfo();\r\n      // }, 500)\r\n\r\n    },\r\n\r\n    handleDoorManMeasureConfirm() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      if (this.taskInfoForm.taskStatus == 4) {\r\n        leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      } else {\r\n        leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n      }\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {};\r\n      param.taskMaterialList = this.taskMaterials;\r\n      param.leaveLog = leaveTaskLog;\r\n      param.leaveTask = doorManTaskInfo;\r\n      param.measureFlag = this.measureFlag;\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n      //todo\r\n\r\n    },\r\n    // 生成二维码\r\n    creatQrCode() {\r\n      if (this.taskInfoForm.qrCodeContent) {\r\n        this.$refs.qrCode.innerHTML = \"\";\r\n        var YSqrCode = new QRCode(this.$refs.qrCode, {\r\n          text: this.taskInfoForm.qrCodeContent, // 需要转换为二维码的内容\r\n          width: 120,\r\n          height: 120,\r\n          colorDark: \"#000000\",\r\n          colorLight: \"#ffffff\",\r\n          correctLevel: QRCode.CorrectLevel.H,\r\n        });\r\n      }\r\n    },\r\n    getTaskLogList(taskNo) {\r\n      let taskLog = {};\r\n      taskLog.taskNo = taskNo\r\n      getTaskLogs(taskLog).then(response => {\r\n        console.log(\"getTaskLogs\", response);\r\n        // this.taskLogs = response.rows;\r\n        let logs = response.rows || [];\r\n        // 找出包含\"任务完成\"的日志\r\n        const finishedLogs = logs.filter(log => log.info && log.info.includes('任务完成'));\r\n        const otherLogs = logs.filter(log => !(log.info && log.info.includes('任务完成')));\r\n        // 先放\"任务完成\"，再放其他\r\n        this.taskLogs = [...finishedLogs, ...otherLogs];\r\n      })\r\n\r\n    },\r\n    async getTaskmaterialList(taskNo) {\r\n      try {\r\n        console.log(\"getTaskmaterialList\");\r\n        let leaveMaterial = {};\r\n        leaveMaterial.taskNo = taskNo;\r\n        const response = await getTaskmaterials(leaveMaterial);\r\n        this.taskMaterials = response.rows;\r\n        // 赋值后，初始化每个元素的 doormanReceiveNum 和 doormanReceiveNumIn\r\n        this.taskMaterials.forEach(item => {\r\n          item.doormanReceiveNum = item.planNum;\r\n          console.log(\"item.planType\", this.planForm.planType);\r\n          if (this.planForm.planType == 2 || this.planForm.planType == 3) {\r\n            item.doormanReceiveNumIn = item.planNum;\r\n          }\r\n        });\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskmaterialList error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    editDoorManRow(row) {\r\n      row._backup = JSON.parse(JSON.stringify(row));//深拷贝\r\n      this.editingRow = row;\r\n      this.editDoorManStatus = true;\r\n      console.log(\"this.editDoorManRow\", row);\r\n    },\r\n    editFactoryRow() {\r\n      this.backupMaterials = JSON.parse(JSON.stringify(this.taskMaterials));//深拷贝\r\n      this.editFactoryStatus = true;\r\n    },\r\n    cancelDoorManEdit(row) {\r\n      //深拷贝\r\n      if (row._backup) {\r\n        // 恢复备份数据\r\n        Object.assign(row, row._backup);\r\n        delete row._backup; // 删除备份数据\r\n      };\r\n      this.editingRow = null; // 清空当前编辑行\r\n      this.editDoorManStatus = false;\r\n    },\r\n    cancelFactoryEdit() {\r\n      this.taskMaterials = JSON.parse(JSON.stringify(this.backupMaterials));//深拷贝\r\n      console.log(\"this.taskMaterials\", this.taskMaterials);\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    saveDoorManRowIn() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNumIn是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNumIn !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫入厂确认数量(${item.doormanReceiveNumIn})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫入厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id;\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n    saveDoorManRow() {\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.guard')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n\r\n      if (this.taskMaterials.length == 0) {\r\n        console.log(\"taskMaterials\", this.taskMaterials);\r\n        this.$message.warning('物资异常');\r\n        return\r\n      }\r\n\r\n      // 校验doormanReceiveNum是否等于planNum\r\n      for (const item of this.taskMaterials) {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning(`物资\"${item.materialName}\"的门卫确认数量(${item.doormanReceiveNum})与计划数量(${item.planNum})不一致，请检查`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      let leaveTaskLog = {};\r\n      leaveTaskLog.logType = 2;\r\n      leaveTaskLog.taskNo = this.taskNo;\r\n      leaveTaskLog.applyNo = this.applyNo;\r\n      leaveTaskLog.info = '门卫出厂确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ');\r\n\r\n      let doorManTaskInfo = {}\r\n      doorManTaskInfo.id = this.taskInfoForm.id\r\n      if (this.taskInfoForm.taskType == 1) {\r\n        doorManTaskInfo.taskStatus = 9\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 0) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 2 && this.measureFlag == 1) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.taskInfoForm.taskStatus == 4) {\r\n        doorManTaskInfo.taskStatus = 5\r\n        doorManTaskInfo.leaveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //离厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 0 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 7\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      } else if (this.taskInfoForm.taskType == 3 && this.measureFlag == 1 && this.taskInfoForm.taskStatus == 5) {\r\n        doorManTaskInfo.taskStatus = 6\r\n        doorManTaskInfo.enterTime = new Date().toISOString().slice(0, 19).replace('T', ' ')\r\n        //出厂大门\r\n      }\r\n\r\n      let param = {\r\n        taskMaterialList: this.taskMaterials,\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: doorManTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", param, this.taskInfoForm.taskType);\r\n\r\n\r\n      this.callApiWithGateLocation(param).then(res => {\r\n        console.log(\"addLeaveLogAndEditTaskMaterialsAndUpdateTask\", res)\r\n        if (res.code == 200) {\r\n          this.$message.success('门卫确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n        } else {\r\n          // 其他失败原因\r\n          this.$message.error(res.message || '门卫确认成功');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleDoorManConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n\r\n      this.editDoorManStatus = false;\r\n    },\r\n\r\n\r\n    saveFactoryRow() {\r\n\r\n      this.editFactoryStatus = false;\r\n    },\r\n\r\n    resetTaskInfoForm() {\r\n      this.taskInfoForm = {};\r\n    },\r\n\r\n    async getTaskInfo() {\r\n      try {\r\n        const response = await getTask(this.dispatchId);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async getTaskInfoByTaskNo() {\r\n      try {\r\n        const response = await getTaskByTaskNo(this.taskNo);\r\n        this.taskInfoForm = response.data;\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n\r\n        // 从返回的数据中获取所需的参数\r\n        this.dispatchId = this.taskInfoForm.id;\r\n        this.applyNo = this.taskInfoForm.applyNo;\r\n\r\n        if (this.taskInfoForm.licensePlateColor == 1) {\r\n          this.taskInfoForm.licensePlateColor = '蓝色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 2) {\r\n          this.taskInfoForm.licensePlateColor = '绿色'\r\n        } else if (this.taskInfoForm.licensePlateColor == 3) {\r\n          this.taskInfoForm.licensePlateColor = '黄'\r\n        } else if (this.taskInfoForm.licensePlateColor == 4) {\r\n          this.taskInfoForm.licensePlateColor = '黄绿色'\r\n        }\r\n        console.log(\"this.taskInfoForm\", this.taskInfoForm);\r\n        // 生成二维码\r\n        this.$nextTick(() => {\r\n          this.creatQrCode();\r\n        });\r\n        return response;\r\n      } catch (error) {\r\n        console.error('getTaskInfoByTaskNo error:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    //计划状态\r\n    getPlanStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期',\r\n        '待分厂审批': '待分厂审批',\r\n        '待分厂复审': '待分厂复审',\r\n        '待生产指挥中心审批': '待生产指挥中心审批',\r\n        '审批完成': '审批完成',\r\n        '已出厂': '已出厂',\r\n        '部分收货': '部分收货',\r\n        '已完成': '已完成',\r\n        '驳回': '驳回',\r\n        '废弃': '废弃',\r\n        '过期': '过期',\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n    // 获取排放标准文本\r\n    getEmissionStandardsText(standard) {\r\n      const standardMap = {\r\n        1: '国五',\r\n        2: '国六',\r\n        3: '新能源'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取排放标准标签类型\r\n    getEmissionStandardsTagType(standard) {\r\n      const typeMap = {\r\n        1: 'warning',  // 国五\r\n        2: 'success',  // 国六\r\n        3: 'primary'   // 新能源\r\n      };\r\n      return typeMap[standard] || 'info';\r\n    },\r\n\r\n    // 获取物资状态文本\r\n    getMaterialStatusText(status) {\r\n      const statusMap = {\r\n        1: '待装载',\r\n        2: '已装载',\r\n        3: '已签收',\r\n        4: '异常'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取物资状态标签类型\r\n    getMaterialStatusType(status) {\r\n      const typeMap = {\r\n        1: 'info',     // 待装载\r\n        2: 'warning',  // 已装载\r\n        3: 'success',  // 已签收\r\n        4: 'danger'    // 异常\r\n      };\r\n      return typeMap[status] || 'info';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#E6A23C', // 更新\r\n        3: '#67C23A', // 完成\r\n        4: '#F56C6C', // 异常\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.type] || '#409EFF';\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    // 获取任务详情数据\r\n    getTaskDetail(dispatchId) {\r\n      // 实际项目中这里需要调用API获取数据\r\n      // getDispatchTaskDetail(dispatchId).then(response => {\r\n      //   const { driverInfo, carInfo, taskMaterials, taskLogs } = response.data;\r\n      //   this.driverInfo = driverInfo;\r\n      //   this.carInfo = carInfo;\r\n      //   this.taskMaterials = taskMaterials;\r\n      //   this.taskLogs = taskLogs;\r\n      // });\r\n    },\r\n    handleShowDropdownChange(val) {\r\n      if (!val) {\r\n        this.factoryConfirmForm.extraOption = '';\r\n      }\r\n    },\r\n    openOptionDialog() {\r\n      this.optionDialogVisible = true;\r\n      this.loadOptions();\r\n      // 重置选中状态\r\n      this.selectedOption = null;\r\n      this.$nextTick(() => {\r\n        if (this.$refs.optionTable) {\r\n          this.$refs.optionTable.clearSelection();\r\n        }\r\n      });\r\n    },\r\n    handleOptionSelection(selection) {\r\n      // 只保留最后选中的一项\r\n      if (selection.length > 1) {\r\n        const lastSelected = selection[selection.length - 1];\r\n        this.$refs.optionTable.clearSelection();\r\n        this.$refs.optionTable.toggleRowSelection(lastSelected, true);\r\n        this.selectedOption = lastSelected;\r\n      } else {\r\n        this.selectedOption = selection[0];\r\n      }\r\n    },\r\n    confirmOptionSelection() {\r\n      if (!this.selectedOption) {\r\n        this.$message.warning('请选择一个选项');\r\n        return;\r\n      }\r\n\r\n      this.factoryConfirmForm.extraOption = this.selectedOption.applyNo;\r\n\r\n      // let dispatchInfo = {};\r\n      // dispatchInfo.carNum = this.taskInfoForm.carNum;\r\n      // dispatchInfo.isDirectSupply = 1;\r\n\r\n      // isAllowDispatch(dispatchInfo).then(response => {\r\n      //   let row = response.data;\r\n      //   if (row > 0) {\r\n      //     this.$message.error(\"当前车有正在执行的任务\")\r\n      //     return;\r\n      //   } else {\r\n      //     this.optionDialogVisible = false;\r\n      //     this.$message.success('选项已确认');\r\n      //   }\r\n      //   console.log(\"this.isAllowDispatch\", response);\r\n      // }).catch(err => {\r\n      //   console.error('dispatch error:', err);\r\n      //   this.$message.error('网络异常，稍后重试');\r\n      // });\r\n\r\n      this.optionDialogVisible = false;\r\n      this.$message.success('选项已确认');\r\n\r\n\r\n\r\n    },\r\n    loadOptions() {\r\n      // 这里应该调用API获取leave_plan表的数据\r\n      this.optionList = this.directSupplyPlanList; // 使用直供计划列表作为选项数据\\\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n      console.log(\"optionList\", this.optionList)\r\n    },\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用（出厂不返回）',\r\n        11: '通用（出厂返回）',\r\n        12: '委外加工（出厂返回）',\r\n        21: '有计划量计量（跨区调拨）',\r\n        22: '短期（跨区调拨）',\r\n        23: '钢板（圆钢）（跨区调拨）',\r\n        31: '通用（退货申请）'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n    searchOptions() {\r\n      // 取出并转小写\r\n      const searchPlanNo = (this.searchForm.planNo || '').toLowerCase();\r\n      const searchApplyNo = (this.searchForm.applyNo || '').toLowerCase();\r\n      const searchReceiveCompany = (this.searchForm.receiveCompany || '').toLowerCase();\r\n\r\n      // 过滤\r\n      this.optionList = this.directSupplyPlanList.filter(item => {\r\n        const planNo = (item.planNo || '').toString().toLowerCase();\r\n        const applyNo = (item.applyNo || '').toString().toLowerCase();\r\n        const receiveCompany = (item.receiveCompany || '').toString().toLowerCase();\r\n\r\n        // 为空不作为条件\r\n        const matchPlanNo = !searchPlanNo || planNo.includes(searchPlanNo);\r\n        const matchApplyNo = !searchApplyNo || applyNo.includes(searchApplyNo);\r\n        const matchReceiveCompany = !searchReceiveCompany || receiveCompany.includes(searchReceiveCompany);\r\n\r\n        return matchPlanNo && matchApplyNo && matchReceiveCompany;\r\n      });\r\n\r\n      // 更新状态显示\r\n      this.optionList.forEach(item => {\r\n        item.planStatus = this.getPlanStatusText(item.planStatus);\r\n      });\r\n    },\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        planNo: '',\r\n        applyNo: '',\r\n        receiveCompany: ''\r\n      };\r\n      this.loadOptions(); // 重新加载所有数据\r\n    },\r\n    getTaskTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return typeMap[type] || '未知';\r\n    },\r\n    // // 判断行是否可选\r\n    // isSelectable(row) {\r\n    //   // 当门卫确认数量不为0时，该行可选\r\n    //   return row.doormanReceiveNum > 0 && this.taskInfoForm.taskStatus !== 9;\r\n    // },\r\n\r\n    // 表格选择变化时的处理函数\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n    },\r\n\r\n    // 处理非计量分厂确认\r\n    handleNonMeasureFactoryConfirm() {\r\n      const roles = this.$store.getters.roles;\r\n      if (!roles.includes('leave.unloading')) {\r\n        this.$message.error('您没有门卫出厂确认权限');\r\n        return;\r\n      }\r\n      let isHandled = false;\r\n      this.selectedRows.forEach(item => {\r\n        if (item.doormanReceiveNum !== item.planNum) {\r\n          this.$message.warning('门卫确认数量和计划数量不一致，请检查');\r\n          isHandled = true;\r\n        }\r\n      });\r\n\r\n      if (isHandled) {\r\n        return;\r\n      }\r\n\r\n      // if (this.selectedRows.length === 0) {\r\n      //   this.$message.warning('请选择需要确认的物资');\r\n      //   return;\r\n      // }\r\n\r\n      // 生成派车日志\r\n      let leaveTaskLog = {\r\n        logType: 2,\r\n        taskNo: this.taskNo,\r\n        applyNo: this.applyNo,\r\n        info: '分厂接收确认，确认物资：' + this.taskMaterials.map(item => item.materialName).join('、 ')\r\n      };\r\n\r\n      // 构建任务信息\r\n      let factoryTaskInfo = {\r\n        id: this.taskInfoForm.id,\r\n        unloadingWorkNo: '卸货人占位符',//后端updateLeaveTask方法\r\n        unloadingTime: new Date(),\r\n        taskStatus: 9\r\n      };\r\n\r\n      this.selectedRows.forEach(item => {\r\n        // 设置非计量分厂确认数量\r\n        item.factoryReceiveNum = item.doormanReceiveNum;\r\n      });\r\n\r\n      // 构建请求参数\r\n      let param = {\r\n        taskMaterialList: this.selectedRows, // 使用选中的行数据\r\n        leaveLog: leaveTaskLog,\r\n        leaveTask: factoryTaskInfo,\r\n        measureFlag: this.measureFlag\r\n      };\r\n\r\n      // 发送请求\r\n      addLeaveLogAndEditTaskMaterialsAndUpdateTask(param).then(res => {\r\n        if (res.code == 200) {\r\n          this.$message.success('非计量分厂确认成功');\r\n          this.getTaskLogList(this.taskNo);\r\n          this.getTaskInfo();\r\n          // 清空选中状态\r\n          this.selectedRows = [];\r\n        } else {\r\n          this.$message.error(res.message || '非计量分厂确认失败');\r\n        }\r\n      }).catch(err => {\r\n        console.error('handleNonMeasureFactoryConfirm error:', err);\r\n        this.$message.error('网络异常，稍后重试');\r\n      });\r\n    },\r\n    openNewTaskWindow() {\r\n      console.log(\"openNewTaskWindow\", this.directSupplyParams);\r\n      let dispatchId = this.directSupplyParams.dispatchId;\r\n      let applyNo = BigInt(this.directSupplyParams.applyNo);\r\n      let measureFlag = this.directSupplyParams.measureFlag;\r\n      let planType = this.directSupplyParams.planType;\r\n      let taskNo = BigInt(this.directSupplyParams.taskNo);\r\n      const url = `http://localhost/leave/plan/task?dispatchId=${dispatchId}&applyNo=${applyNo}&measureFlag=${measureFlag}&planType=${planType}&taskNo=${taskNo}`;\r\n      window.open(url, '_blank');\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.btn-wrapper {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.qrcode {\r\n  margin: 0 auto;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 通行证二维码模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 司机信息模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 车辆信息模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 物资列表模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.form-footer {\r\n  margin-top: 30px;\r\n  text-align: center;\r\n}\r\n\r\n.driver-photos {\r\n  padding: 0 20px 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.photo-item {\r\n  width: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.photo-item h4 {\r\n  padding: 10px;\r\n  background: #f5f7fa;\r\n  margin: 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.photo-container {\r\n  padding: 10px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.photo-container img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  object-fit: contain;\r\n}\r\n\r\n.button-container {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 25px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n</style>\r\n"]}]}