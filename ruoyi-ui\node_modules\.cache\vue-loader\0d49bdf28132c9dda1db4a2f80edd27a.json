{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue?vue&type=template&id=c9a9bf16&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue", "mtime": 1756370285682}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDxlbC1jYXJkIGNsYXNzPSJib3gtY2FyZCI+CiAgICA8ZGl2IHNsb3Q9ImhlYWRlciIgY2xhc3M9ImNhcmQtaGVhZGVyIj4KICAgICAgPGgzPueUs+ivt+ivpuaDhTwvaDM+CiAgICA8L2Rpdj4KCiAgICA8IS0tIOWfuuacrOS/oeaBr+mDqOWIhiAtLT4KICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tY29udGFpbmVyIj4KICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi10aXRsZSI+5Z+65pys5L+h5oGvPC9kaXY+CiAgICAgIDxlbC1kZXNjcmlwdGlvbnMgOmNvbHVtbj0iMiIgYm9yZGVyPgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i55Sz6K+357yW5Y+3Ij4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tZG9jdW1lbnQiPjwvaT4g55Sz6K+357yW5Y+3PC90ZW1wbGF0ZT4KICAgICAgICAgIHt7IHBsYW5JbmZvLmFwcGx5Tm8gfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i6K6h5YiS5Y+3Ij4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tZG9jdW1lbnQiPjwvaT4g6K6h5YiS5Y+3PC90ZW1wbGF0ZT4KICAgICAgICAgIHt7IHBsYW5JbmZvLnBsYW5ObyB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLorqHliJLnirbmgIEiPgogICAgICAgICAgPHRlbXBsYXRlIHNsb3Q9ImxhYmVsIj48aSBjbGFzcz0iZWwtaWNvbi1zLWZsYWciPjwvaT4g6K6h5YiS54q25oCBPC90ZW1wbGF0ZT4KICAgICAgICAgIDxlbC10YWcgOnR5cGU9ImdldFBsYW5TdGF0dXNUeXBlKHBsYW5JbmZvLnBsYW5TdGF0dXMpIj57eyBnZXRQbGFuU3RhdHVzVGV4dChwbGFuSW5mby5wbGFuU3RhdHVzKSB9fTwvZWwtdGFnPgogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i6K6h5YiS57G75Z6LIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tcy1vcmRlciI+PC9pPiDorqHliJLnsbvlnos8L3RlbXBsYXRlPgogICAgICAgICAgPGVsLXRhZyA6dHlwZT0iZ2V0UGxhblR5cGVUYWdUeXBlKHBsYW5JbmZvLnBsYW5UeXBlKSI+e3sgZ2V0UGxhblR5cGVUZXh0KHBsYW5JbmZvLnBsYW5UeXBlKSB9fTwvZWwtdGFnPgogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLkuJrliqHnsbvlnosiPgogICAgICAgICAgPHRlbXBsYXRlIHNsb3Q9ImxhYmVsIj48aSBjbGFzcz0iZWwtaWNvbi1zLW1hbmFnZW1lbnQiPjwvaT4g5Lia5Yqh57G75Z6LPC90ZW1wbGF0ZT4KICAgICAgICAgIDxlbC10YWcgOnR5cGU9ImdldEJ1c2luZXNzQ2F0ZWdvcnlUYWdUeXBlKHBsYW5JbmZvLmJ1c2luZXNzQ2F0ZWdvcnkpIj57ewogICAgICAgICAgICBnZXRCdXNpbmVzc0NhdGVnb3J5VGV4dChwbGFuSW5mby5idXNpbmVzc0NhdGVnb3J5KSB9fTwvZWwtdGFnPgogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5piv5ZCm6K6h6YePIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tcy1vcGVyYXRpb24iPjwvaT4g5piv5ZCm6K6h6YePPC90ZW1wbGF0ZT4KICAgICAgICAgIDxlbC10YWcgOnR5cGU9InBsYW5JbmZvLm1lYXN1cmVGbGFnID09PSAxID8gJ3N1Y2Nlc3MnIDogJ2RhbmdlciciPgogICAgICAgICAgICB7eyBwbGFuSW5mby5tZWFzdXJlRmxhZyA9PT0gMSA/ICforqHph48nIDogJ+S4jeiuoemHjycgfX0KICAgICAgICAgIDwvZWwtdGFnPgogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLorqHliJLph48iIHYtaWY9InBsYW5JbmZvLnBsYW5uZWRBbW91bnQiPgogICAgICAgICAgPHRlbXBsYXRlIHNsb3Q9ImxhYmVsIj48aSBjbGFzcz0iZWwtaWNvbi1kb2N1bWVudCI+PC9pPiDorqHliJLph4/vvIjlkKjvvIk8L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8ucGxhbm5lZEFtb3VudCB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLmmK/lkKblpI3lrqEiPgogICAgICAgICAgPHRlbXBsYXRlIHNsb3Q9ImxhYmVsIj48aSBjbGFzcz0iZWwtaWNvbi1zLWNoZWNrIj48L2k+IOaYr+WQpuWkjeWuoTwvdGVtcGxhdGU+CiAgICAgICAgICA8ZWwtdGFnIDp0eXBlPSJwbGFuSW5mby5zZWNBcHByb3ZlRmxhZyA9PT0gMSA/ICd3YXJuaW5nJyA6ICdpbmZvJyI+CiAgICAgICAgICAgIHt7IHBsYW5JbmZvLnNlY0FwcHJvdmVGbGFnID09PSAxID8gJ+aYrycgOiAn5ZCmJyB9fQogICAgICAgICAgPC9lbC10YWc+CiAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KCiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLnlLPor7fljZXkvY0iIHYtaWY9InBsYW5JbmZvLnNvdXJjZUNvbXBhbnkiPgogICAgICAgICAgPHRlbXBsYXRlIHNsb3Q9ImxhYmVsIj48aSBjbGFzcz0iZWwtaWNvbi1vZmZpY2UtYnVpbGRpbmciPjwvaT4g55Sz6K+35Y2V5L2NPC90ZW1wbGF0ZT4KICAgICAgICAgIHt7IHBsYW5JbmZvLnNvdXJjZUNvbXBhbnkgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5pS26LSn5Y2V5L2NIiB2LWlmPSJwbGFuSW5mby5yZWNlaXZlQ29tcGFueSI+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0ibGFiZWwiPjxpIGNsYXNzPSJlbC1pY29uLXNjaG9vbCI+PC9pPiDmlLbotKfljZXkvY08L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8ucmVjZWl2ZUNvbXBhbnkgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgoKICAgICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9Iui/lOWbnuWNleS9jSIgdi1pZj0icGxhbkluZm8udGFyZ2V0Q29tcGFueSI+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0ibGFiZWwiPjxpIGNsYXNzPSJlbC1pY29uLXMtaG9tZSI+PC9pPiDov5Tlm57ljZXkvY08L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8udGFyZ2V0Q29tcGFueSB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLorqHliJLov5Tlm57ml7bpl7QiIHYtaWY9InBsYW5JbmZvLnBsYW5SZXR1cm5UaW1lIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tdGltZSI+PC9pPiDorqHliJLov5Tlm57ml7bpl7Q8L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8ucGxhblJldHVyblRpbWUgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgoKICAgICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IumAgOi0p+WNleS9jSIgdi1pZj0icGxhbkluZm8ucmVmdW5kRGVwYXJ0bWVudCI+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0ibGFiZWwiPjxpIGNsYXNzPSJlbC1pY29uLXMtc2hvcCI+PC9pPiDpgIDotKfljZXkvY08L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8ucmVmdW5kRGVwYXJ0bWVudCB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5byA5aeL5pe26Ze0IiB2LWlmPSJwbGFuSW5mby5zdGFydFRpbWUiPgogICAgICAgICAgPHRlbXBsYXRlIHNsb3Q9ImxhYmVsIj48aSBjbGFzcz0iZWwtaWNvbi1kYXRlIj48L2k+IOW8gOWni+aXtumXtDwvdGVtcGxhdGU+CiAgICAgICAgICB7eyBwbGFuSW5mby5zdGFydFRpbWUgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i57uT5p2f5pe26Ze0IiB2LWlmPSJwbGFuSW5mby5lbmRUaW1lIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tZGF0ZSI+PC9pPiDnu5PmnZ/ml7bpl7Q8L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8uZW5kVGltZSB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLmnInmlYjmnJ8iIHYtaWY9IiFwbGFuSW5mby5lbmRUaW1lIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tZGF0ZSI+PC9pPiDmnInmlYjmnJ88L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8uZXhwaXJlVGltZSB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i55uR6KOF5Lq6IiB2LWlmPSJwbGFuSW5mby5tb25pdG9yIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tdXNlciI+PC9pPiDnm5Hoo4Xkuro8L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8ubW9uaXRvciB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLnianotYTkuJPnrqHlkZgiIHYtaWY9InBsYW5JbmZvLnNwZWNpYWxNYW5hZ2VyIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tcy1jdXN0b20iPjwvaT4g54mp6LWE5LiT566h5ZGYPC90ZW1wbGF0ZT4KICAgICAgICAgIHt7IHBsYW5JbmZvLnNwZWNpYWxNYW5hZ2VyIH19CiAgICAgICAgPC9lbC1kZXNjcmlwdGlvbnMtaXRlbT4KCiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLnianotYTnsbvlnosiIHYtaWY9InBsYW5JbmZvLml0ZW1UeXBlIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tZ29vZHMiPjwvaT4g54mp6LWE57G75Z6LPC90ZW1wbGF0ZT4KICAgICAgICAgIDxlbC10YWcgOnR5cGU9ImdldE1hdGVyaWFsVHlwZVRhZ1R5cGUocGxhbkluZm8uaXRlbVR5cGUpIj4KICAgICAgICAgICAge3sgZ2V0TWF0ZXJpYWxUeXBlVGV4dChwbGFuSW5mby5pdGVtVHlwZSkgfX0KICAgICAgICAgIDwvZWwtdGFnPgogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLlh7rljoLljp/lm6AiIHYtaWY9InBsYW5JbmZvLnJlYXNvbiI+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0ibGFiZWwiPjxpIGNsYXNzPSJlbC1pY29uLWluZm8iPjwvaT4g5Ye65Y6C5Y6f5ZugPC90ZW1wbGF0ZT4KICAgICAgICAgIHt7IHBsYW5JbmZvLnJlYXNvbiB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i5ZCI5ZCM5Y+3IiB2LWlmPSJwbGFuSW5mby5jb250cmFjdE5vIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90PSJsYWJlbCI+PGkgY2xhc3M9ImVsLWljb24tdGlja2V0cyI+PC9pPiDlkIjlkIzlj7c8L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8uY29udHJhY3RObyB9fQogICAgICAgIDwvZWwtZGVzY3JpcHRpb25zLWl0ZW0+CiAgICAgICAgPGVsLWRlc2NyaXB0aW9ucy1pdGVtIGxhYmVsPSLnlLPor7fml7bpl7QiIHYtaWY9InBsYW5JbmZvLmFwcGx5VGltZSI+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0ibGFiZWwiPjxpIGNsYXNzPSJlbC1pY29uLXRpbWVyIj48L2k+IOeUs+ivt+aXtumXtDwvdGVtcGxhdGU+CiAgICAgICAgICB7eyBwbGFuSW5mby5hcHBseVRpbWUgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgoKICAgICAgICA8ZWwtZGVzY3JpcHRpb25zLWl0ZW0gbGFiZWw9IueUs+ivt+S6uiIgdi1pZj0icGxhbkluZm8uYXBwbHlVc2VyTmFtZSI+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0ibGFiZWwiPjxpIGNsYXNzPSJlbC1pY29uLXVzZXItc29saWQiPjwvaT4g55Sz6K+35Lq6PC90ZW1wbGF0ZT4KICAgICAgICAgIHt7IHBsYW5JbmZvLmFwcGx5VXNlck5hbWUgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICAgIDxlbC1kZXNjcmlwdGlvbnMtaXRlbSBsYWJlbD0i6K6h5YiS6YePIiB2LWlmPSJwbGFuSW5mby5wbGFubmVkX2Ftb3VudCI+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdD0ibGFiZWwiPjxpIGNsYXNzPSJlbC1pY29uLXVzZXItc29saWQiPjwvaT4g6K6h5YiS6YePKOWQqCk8L3RlbXBsYXRlPgogICAgICAgICAge3sgcGxhbkluZm8ucGxhbm5lZF9hbW91bnQgfX0KICAgICAgICA8L2VsLWRlc2NyaXB0aW9ucy1pdGVtPgogICAgICA8L2VsLWRlc2NyaXB0aW9ucz4KICAgIDwvZGl2PgoKICAgIDwhLS0g5paw5aKe5Zu+54mH5YiX6KGo6YOo5YiGIC0tPgogICAgPGRpdiBjbGFzcz0ic2VjdGlvbi1jb250YWluZXIiIHYtaWY9ImltYWdlTGlzdC5sZW5ndGggPiAwIj4KICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi10aXRsZSI+CiAgICAgICAgPHNwYW4+PGkgY2xhc3M9ImVsLWljb24tcGljdHVyZS1vdXRsaW5lIj48L2k+IOeUs+ivt+WbvueJhzwvc3Bhbj4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9ImltYWdlLWNvbnRhaW5lciI+CiAgICAgICAgPHZpZXdlciA6aW1hZ2VzPSJpbWFnZUxpc3QiPgogICAgICAgICAgPGRpdiBjbGFzcz0iaW1hZ2UtbGlzdCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImltYWdlLWl0ZW0iIHYtZm9yPSIoaW1hZ2UsIGluZGV4KSBpbiBpbWFnZUxpc3QiIDprZXk9IidpbWctJyArIGluZGV4Ij4KICAgICAgICAgICAgICA8aW1nIDpzcmM9ImltYWdlLnVybCIgOmFsdD0iaW1hZ2UubmFtZSI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW1hZ2UtbmFtZSI+e3sgaW1hZ2UubmFtZSB9fTwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvdmlld2VyPgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgoKICAgIDwhLS0g5paw5aKe5paH5Lu25YiX6KGo6YOo5YiGIC0tPgogICAgPGRpdiBjbGFzcz0ic2VjdGlvbi1jb250YWluZXIiIHYtaWY9ImZpbGVMaXN0Lmxlbmd0aCA+IDAiPgogICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLXRpdGxlIj4KICAgICAgICA8c3Bhbj48aSBjbGFzcz0iZWwtaWNvbi1kb2N1bWVudCI+PC9pPiDnlLPor7fpmYTku7Y8L3NwYW4+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJmaWxlLWNvbnRhaW5lciI+CiAgICAgICAgPGRpdiBjbGFzcz0iZmlsZS1saXN0Ij4KICAgICAgICAgIDxkaXYgY2xhc3M9ImZpbGUtaXRlbSIgdi1mb3I9IihmaWxlLCBpbmRleCkgaW4gZmlsZUxpc3QiIDprZXk9IidmaWxlLScgKyBpbmRleCIKICAgICAgICAgICAgQGNsaWNrPSJkb3dubG9hZEZpbGUoZmlsZS51cmwsIGZpbGUubmFtZSkiPgogICAgICAgICAgICA8aSBjbGFzcz0iZWwtaWNvbi1kb2N1bWVudCBmaWxlLWljb24iPjwvaT4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iZmlsZS1uYW1lIj57eyBmaWxlLm5hbWUgfX08L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgIDwvZGl2PgoKICAgIDwhLS0g54mp6LWE5YiX6KGo6YOo5YiGIC0tPgogICAgPGRpdiBjbGFzcz0ic2VjdGlvbi1jb250YWluZXIiPgogICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLXRpdGxlIj7nianotYTliJfooag8L2Rpdj4KICAgICAgPGVsLXRhYmxlIDpkYXRhPSJwbGFuSW5mby5tYXRlcmlhbHMiIHN0eWxlPSJ3aWR0aDogMTAwJSIgYm9yZGVyPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gdHlwZT0iaW5kZXgiIHdpZHRoPSI1MCIgbGFiZWw9IuW6j+WPtyI+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJtYXRlcmlhbE5hbWUiIGxhYmVsPSLnianotYTlkI3np7AiIHdpZHRoPSIxODAiPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0ibWF0ZXJpYWxTcGVjIiBsYWJlbD0i54mp6LWE5Z6L5Y+36KeE5qC8IiB3aWR0aD0iMTgwIj4KICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9InBsYW5OdW0iIGxhYmVsPSLorqHliJLmlbDph48iIHdpZHRoPSIxMjAiPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0ibWVhc3VyZVVuaXQiIGxhYmVsPSLljZXkvY0iIHdpZHRoPSIxMjAiPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0icmVtYXJrIiBsYWJlbD0i5aSH5rOoIiBtaW4td2lkdGg9IjE1MCI+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgIDwvZWwtdGFibGU+CiAgICA8L2Rpdj4KCiAgICA8IS0tIOa0vui9puS/oeaBr+mDqOWIhiAtLT4KICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tY29udGFpbmVyIj4KICAgICAgPGRpdiBjbGFzcz0ic2VjdGlvbi10aXRsZSI+CiAgICAgICAgPHNwYW4+5rS+6L2m5L+h5oGvPC9zcGFuPgogICAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgc2l6ZT0ic21hbGwiIGljb249ImVsLWljb24tdHJ1Y2siIEBjbGljaz0ib3BlbkRpc3BhdGNoRGlhbG9nIgogICAgICAgICAgOmRpc2FibGVkPSIhY2FuRGlzcGF0Y2hDYXIiIGNsYXNzPSJkaXNwYXRjaC1idG4iPgogICAgICAgICAg5rS+6L2mCiAgICAgICAgPC9lbC1idXR0b24+CiAgICAgIDwvZGl2PgoKICAgICAgPGVsLXRhYmxlIHYtaWY9InRhc2tMaXN0SW5mby5sZW5ndGggPiAwIiA6ZGF0YT0idGFza0xpc3RJbmZvIiBzdHlsZT0id2lkdGg6IDEwMCUiIGJvcmRlcj4KICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHR5cGU9ImluZGV4IiB3aWR0aD0iNTAiIGxhYmVsPSLluo/lj7ciPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0iY2FyTnVtIiBsYWJlbD0i6L2m54mM5Y+3IiB3aWR0aD0iMTIwIj4KICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9ImRyaXZlck5hbWUiIGxhYmVsPSLlj7jmnLrlp5PlkI0iIHdpZHRoPSIxMDAiPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0ibW9iaWxlUGhvbmUiIGxhYmVsPSLlj7jmnLrmiYvmnLrlj7ciIHdpZHRoPSIxMjAiPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0idGFza1R5cGUiIGxhYmVsPSLku7vliqHnsbvlnosiIHdpZHRoPSIxMjAiIDpmb3JtYXR0ZXI9InRhc2tUeXBlRm9ybWF0Ij4KICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICA8IS0tIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0icGxhbk51bSIgbGFiZWw9IuiuoeWIkuaVsOmHjyIgd2lkdGg9IjE4MCIgdi1pZj0icGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMCI+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJkb29ybWFuUmVjZWl2ZU51bSIgbGFiZWw9IumXqOWNq+ehruiupOaVsOmHjyIgd2lkdGg9IjE4MCIgdi1pZj0icGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMCAiPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0iZmFjdG9yeVJlY2VpdmVOdW0iIGxhYmVsPSLliIbljoLnoa7orqTmlbDph48iIHdpZHRoPSIxODAiIHYtaWY9InBsYW5JbmZvLm1lYXN1cmVGbGFnID09IDAiPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPiAtLT4KICAgICAgICA8IS0tIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0icGxhbk51bSIgbGFiZWw9IuiuoeWIkuaVsOmHjyIgd2lkdGg9IjEyMCIgdi1pZj0icGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMSI+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJtZWFzdXJlVW5pdCIgbGFiZWw9IuiuoemHj+WNleS9jSIgd2lkdGg9IjEyMCIgdi1pZj0icGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMSI+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+IC0tPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0idGFyZVdlaWdodCIgbGFiZWw9IuearumHjSIgd2lkdGg9IjEwMCIgdi1pZj0icGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMSI+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgICB7eyBzY29wZS5yb3cudGFyZSB8fCAnLScgfX0ge3sgc2NvcGUucm93LnRhcmUgPyAndCcgOiAnJyB9fQogICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9Imdyb3NzV2VpZ2h0IiBsYWJlbD0i5q+b6YeNIiB3aWR0aD0iMTAwIiB2LWlmPSJwbGFuSW5mby5tZWFzdXJlRmxhZyA9PSAxIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgIHt7IHNjb3BlLnJvdy5ncm9zcyB8fCAnLScgfX0ge3sgc2NvcGUucm93Lmdyb3NzID8gJ3QnIDogJycgfX0KICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJ0YXJlV2VpZ2h0IiBsYWJlbD0i55qu6YeNKOWkjeejhSkiIHdpZHRoPSIxMDAiCiAgICAgICAgICB2LWlmPSJwbGFuSW5mby5tZWFzdXJlRmxhZyA9PSAxICYmIHBsYW5JbmZvLnBsYW5UeXBlICE9PSAxIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgIHt7IHNjb3BlLnJvdy5zZWNUYXJlIHx8ICctJyB9fSB7eyBzY29wZS5yb3cuc2VjVGFyZSA/ICd0JyA6ICcnIH19CiAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0iZ3Jvc3NXZWlnaHQiIGxhYmVsPSLmr5vph40o5aSN56OFKSIgd2lkdGg9IjEwMCIKICAgICAgICAgIHYtaWY9InBsYW5JbmZvLm1lYXN1cmVGbGFnID09IDEgJiYgcGxhbkluZm8ucGxhblR5cGUgIT09IDEiPgogICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAge3sgc2NvcGUucm93LnNlY0dyb3NzIHx8ICctJyB9fSB7eyBzY29wZS5yb3cuc2VjR3Jvc3MgPyAndCcgOiAnJyB9fQogICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KCgogICAgICAgIDxlbC10YWJsZS1jb2x1bW4gcHJvcD0iY3JlYXRlVGltZSIgbGFiZWw9Iua0vui9puaXtumXtCIgd2lkdGg9IjE2MCI+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJ0YXNrU3RhdHVzIiBsYWJlbD0i5Lu75Yqh54q25oCBIiB3aWR0aD0iMTIwIiA6Zm9ybWF0dGVyPSJ0YXNrU3RhdHVzRm9ybWF0Ij4KICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmk43kvZwiIG1pbi13aWR0aD0iMTIwIj4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ibWluaSIgdHlwZT0idGV4dCIgaWNvbj0iZWwtaWNvbi12aWV3IiBAY2xpY2s9ImdvVG9UYXNrRGV0YWlsKHNjb3BlLnJvdykiPgogICAgICAgICAgICAgIOS7u+WKoeivpuaDhQogICAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgIDwvZWwtdGFibGU+CgogICAgICA8ZGl2IHYtZWxzZSBjbGFzcz0iZW1wdHktZGF0YSI+CiAgICAgICAgPGVsLWVtcHR5IGRlc2NyaXB0aW9uPSLmmoLml6DmtL7ovaborrDlvZUiPjwvZWwtZW1wdHk+CiAgICAgIDwvZGl2PgoKICAgICAgPCEtLSDnianotYTnoa7orqTmjInpkq4gLS0+CiAgICAgIDxkaXYgc3R5bGU9InRleHQtYWxpZ246IHJpZ2h0OyBtYXJnaW4tdG9wOiAxNXB4OyI+CiAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBpY29uPSJlbC1pY29uLWZpbmlzaGVkIiBAY2xpY2s9ImhhbmRsZU1hdGVyaWFsQ29uZmlybSI+CiAgICAgICAgICDnianotYTnoa7orqQKICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICAgIDwhLS0gdi1pZj0iY2FuU2hvd01hdGVyaWFsQ29uZmlybSIgLS0+CgogICAgPCEtLSDlrqHmoLjlhoXlrrnpg6jliIYgLS0+CiAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLWNvbnRhaW5lciIgdi1pZj0icGxhbkluZm8uYXBwcm92ZUJ1dHRvblNob3ciPgogICAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLXRpdGxlIj7lrqHmoLjlhoXlrrk8L2Rpdj4KICAgICAgPGVsLWZvcm0gbGFiZWwtd2lkdGg9IjgwcHgiIDptb2RlbD0iYXBwcm92ZUZvcm0iIHJlZj0iYXBwcm92ZUZvcm0iPgogICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWuoeaguOW7uuiuriI+CiAgICAgICAgICA8ZWwtaW5wdXQgdHlwZT0idGV4dGFyZWEiIHYtbW9kZWw9ImFwcHJvdmVGb3JtLmFwcHJvdmVDb250ZW50IiA6cm93cz0iNCIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWuoeaguOW7uuiuriIKICAgICAgICAgICAgbWF4bGVuZ3RoPSIyMDAiIHNob3ctd29yZC1saW1pdD48L2VsLWlucHV0PgogICAgICAgICAgPGRpdiBzdHlsZT0iY29sb3I6ICM5MDkzOTk7IGZvbnQtc2l6ZTogMTJweDsgbWFyZ2luLXRvcDogNXB4OyBtYXJnaW4tYm90dG9tOiAxMHB4OyI+5a6h5qC45bu66K6u5Y+v5LiN5aGr77yM6buY6K6k6YCa6L+H5Li65ZCM5oSP77yM6amz5Zue5Li65ouS57udCiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPC9lbC1mb3JtPgogICAgPC9kaXY+CgogICAgPCEtLSDml6Xlv5fliJfooajpg6jliIYgLS0+CiAgICA8ZGl2IGNsYXNzPSJzZWN0aW9uLWNvbnRhaW5lciI+CiAgICAgIDxkaXYgY2xhc3M9InNlY3Rpb24tdGl0bGUiPuaXpeW/l+WIl+ihqDwvZGl2PgogICAgICA8ZWwtdGltZWxpbmU+CiAgICAgICAgPGVsLXRpbWVsaW5lLWl0ZW0gdi1mb3I9Iihsb2csIGluZGV4KSBpbiBwbGFuSW5mby5sZWF2ZUxvZ3MiIDprZXk9ImluZGV4IiA6dGltZXN0YW1wPSJsb2cuY3JlYXRlVGltZSIKICAgICAgICAgIDpjb2xvcj0iZ2V0TG9nQ29sb3IobG9nKSI+CiAgICAgICAgICB7eyBsb2cuaW5mbyB9fQogICAgICAgIDwvZWwtdGltZWxpbmUtaXRlbT4KICAgICAgPC9lbC10aW1lbGluZT4KICAgIDwvZGl2PgoKICAgIDwhLS0g5Zu65a6a5bqV6YOo5pON5L2c5qCPIC0tPgogICAgPGRpdiBjbGFzcz0iZml4ZWQtYm90dG9tLWFjdGlvbiI+CiAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTAiIHR5cGU9ImZsZXgiIGp1c3RpZnk9ImNlbnRlciIgYWxpZ249Im1pZGRsZSI+CiAgICAgICAgPCEtLSDov5Tlm57mjInpkq4gLS0+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMiIgOnhzPSI2Ij4KICAgICAgICAgIDxlbC1idXR0b24gc2l6ZT0ibWVkaXVtIiBAY2xpY2s9ImNhbmNlbCI+6L+U5ZuePC9lbC1idXR0b24+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDwhLS0g6YCa6L+H5oyJ6ZKuIC0tPgogICAgICAgIDxlbC1jb2wgOnNwYW49IjIiIDp4cz0iNiIgdi1pZj0icGxhbkluZm8uYXBwcm92ZUJ1dHRvblNob3ciPgogICAgICAgICAgPGVsLWJ1dHRvbiBzaXplPSJtZWRpdW0iIHR5cGU9InByaW1hcnkiIGljb249ImVsLWljb24tY2hlY2siIEBjbGljaz0iaGFuZGxlQXBwcm92ZSI+6YCa6L+HPC9lbC1idXR0b24+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDwhLS0g6amz5Zue5oyJ6ZKuIC0tPgogICAgICAgIDxlbC1jb2wgOnNwYW49IjIiIDp4cz0iNiIgdi1pZj0icGxhbkluZm8ucmVqZWN0QnV0dG9uU2hvdyI+CiAgICAgICAgICA8ZWwtYnV0dG9uIHNpemU9Im1lZGl1bSIgdHlwZT0iZGFuZ2VyIiBpY29uPSJlbC1pY29uLWNsb3NlIiBAY2xpY2s9ImhhbmRsZVJlamVjdCI+6amz5ZuePC9lbC1idXR0b24+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDwhLS0g5bqf5byD5oyJ6ZKuIC0tPgogICAgICAgIDxlbC1jb2wgOnNwYW49IjIiIDp4cz0iNiIgdi1pZj0icGxhbkluZm8uZGlzY2FyZEJ1dHRvblNob3ciPgogICAgICAgICAgPGVsLWJ1dHRvbiBzaXplPSJtZWRpdW0iIHR5cGU9InN1Y2Nlc3MiIGljb249ImVsLWljb24tZGVsZXRlIiBAY2xpY2s9ImhhbmRsZURpc2NhcmQiPuW6n+W8gzwvZWwtYnV0dG9uPgogICAgICAgIDwvZWwtY29sPgogICAgICA8L2VsLXJvdz4KICAgIDwvZGl2PgoKICA8L2VsLWNhcmQ+CgogIDwhLS0g5rS+6L2m5by55qGGIC0tPgogIDxlbC1kaWFsb2cgdGl0bGU9Iua0vui9piIgOnZpc2libGUuc3luYz0iZGlzcGF0Y2hEaWFsb2dWaXNpYmxlIiB3aWR0aD0iMTIwMHB4IiBhcHBlbmQtdG8tYm9keSBkZXN0cm95LW9uLWNsb3NlCiAgICBAY2xvc2VkPSJyZXNldERpc3BhdGNoRm9ybSI+CiAgICA8ZWwtZm9ybSByZWY9ImRpc3BhdGNoRm9ybSIgOm1vZGVsPSJkaXNwYXRjaEZvcm0iIDpydWxlcz0iZGlzcGF0Y2hSdWxlcyIgbGFiZWwtd2lkdGg9IjEwMHB4IgogICAgICBjbGFzcz0iZGlzcGF0Y2gtZm9ybSI+CgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLotKfovablj7jmnLoiIHByb3A9ImRyaXZlcklkIiA6cnVsZXM9Ilt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5Y+45py65L+h5oGv5LiN6IO95Li656m6JyB9XSI+CiAgICAgICAgPGVsLXNlbGVjdCBzdHlsZT0id2lkdGg6MzAwcHgiIHYtbW9kZWw9ImRpc3BhdGNoRm9ybS5kcml2ZXJJZCIgZmlsdGVyYWJsZSA6ZmlsdGVyLW1ldGhvZD0iZmlsdGVyRHJpdmVyRGF0YSIKICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nvvIjlpoLmnpzmmL7npLrkuI3lh7ror7flnKjovpPlhaXmoYbmkJzntKLvvIkiIEBjaGFuZ2U9ImhhbmRsZURyaXZlckNoYW5nZSI+CiAgICAgICAgICA8ZWwtb3B0aW9uIHYtZm9yPSJpdGVtIGluIGZpbHRlcmVkRHJpdmVyT3B0aW9ucy5zbGljZSgwLCA1MCkiIDprZXk9Iml0ZW0uaWQiIDpsYWJlbD0iaXRlbS5kcml2ZXJJbmZvIgogICAgICAgICAgICA6dmFsdWU9Iml0ZW0uaWQiPgogICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgPGVsLWJ1dHRvbiBzdHlsZT0ibWFyZ2luLWxlZnQ6IDEwcHg7IGZvbnQtc2l6ZTogMTRweDsgcGFkZGluZzogNXB4IDEwcHg7IiB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgQGNsaWNrPSJvcGVuTmV3RHJpdmVyV2luZG93Ij7liY3lvoDmlrDlop7miJbkv67mlLnlj7jmnLrkv6Hmga8KICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlj7jmnLrlkI3np7AiIHByb3A9Im5hbWUiIHYtaWY9ImRpc3BhdGNoRm9ybS5uYW1lICE9IG51bGwiPgogICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJkaXNwYXRjaEZvcm0ubmFtZSIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWPuOacuuWQjeensCIgZGlzYWJsZWQgc3R5bGU9IndpZHRoOjMwMHB4IiAvPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5omL5py65Y+3IiBwcm9wPSJwaG9uZSIgdi1pZj0iZGlzcGF0Y2hGb3JtLnBob25lICE9IG51bGwiPgogICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJkaXNwYXRjaEZvcm0ucGhvbmUiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmiYvmnLrlj7ciIGRpc2FibGVkIHN0eWxlPSJ3aWR0aDozMDBweCIgLz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iui6q+S7veivgeWPtyIgcHJvcD0iaWRDYXJkIiB2LWlmPSJkaXNwYXRjaEZvcm0uaWRDYXJkICE9IG51bGwiPgogICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJkaXNwYXRjaEZvcm0uaWRDYXJkIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl6Lqr5Lu96K+B5Y+3IiBkaXNhYmxlZCBzdHlsZT0id2lkdGg6MzAwcHgiIC8+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Lq66IS454Wn54mHIiBwcm9wPSJmYWNlSW1nIiB2LWlmPSJkaXNwYXRjaEZvcm0ucGhvdG8gIT0gbnVsbCAmJiBkaXNwYXRjaEZvcm0ucGhvdG8gIT0gJyciPgogICAgICAgIDxkaXYgY2xhc3M9ImltYWdlLWdyaWQiPgogICAgICAgICAgPCEtLSA8ZWwtaW1hZ2Ugdi1mb3I9IihpbWFnZSwgaW5kZXgpIGluIGZvcm0uZmFjZUltZ0xpc3QiIDprZXk9ImluZGV4IiA6c3JjPSJpbWFnZSIgZml0PSJjb3ZlciIKICAgICAgICAgICAgICAgICAgICBsYXp5PjwvZWwtaW1hZ2U+IC0tPgogICAgICAgICAgPGVsLWltYWdlIHN0eWxlPSJ3aWR0aDogMjAwcHg7IGhlaWdodDogMjAwcHgiIDpzcmM9ImRpc3BhdGNoRm9ybS5waG90byIgZml0PSJjb3ZlciI+PC9lbC1pbWFnZT4KICAgICAgICA8L2Rpdj4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpqb7pqbbor4HnhafniYciIHByb3A9ImRyaXZlckxpY2Vuc2VJbWdzIgogICAgICAgIHYtaWY9ImRpc3BhdGNoRm9ybS5kcml2ZXJMaWNlbnNlSW1ncyAhPSBudWxsICYmIGRpc3BhdGNoRm9ybS5kcml2ZXJMaWNlbnNlSW1ncyAhPSAnJyI+CiAgICAgICAgPGRpdiBjbGFzcz0iaW1hZ2UtZ3JpZCI+CiAgICAgICAgICA8IS0tIDxlbC1pbWFnZSB2LWZvcj0iKGltYWdlLCBpbmRleCkgaW4gZm9ybS5kcml2aW5nTGljZW5zZUltZ0xpc3QiIDprZXk9ImluZGV4IiA6c3JjPSJpbWFnZSIgZml0PSJjb3ZlciIKICAgICAgICAgICAgbGF6eT48L2VsLWltYWdlPiAtLT4KICAgICAgICAgIDxlbC1pbWFnZSBzdHlsZT0id2lkdGg6IDIwMHB4OyBoZWlnaHQ6IDIwMHB4IiA6c3JjPSJkaXNwYXRjaEZvcm0uZHJpdmVyTGljZW5zZUltZ3MiIGZpdD0iY292ZXIiPjwvZWwtaW1hZ2U+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6KGM6am26K+B54Wn54mHIiBwcm9wPSJ2ZWhpY2xlTGljZW5zZUltZ3MiCiAgICAgICAgdi1pZj0iZGlzcGF0Y2hGb3JtLnZlaGljbGVMaWNlbnNlSW1ncyAhPSBudWxsICYmIGRpc3BhdGNoRm9ybS52ZWhpY2xlTGljZW5zZUltZ3MgIT0gJyciPgogICAgICAgIDxkaXYgY2xhc3M9ImltYWdlLWdyaWQiPgogICAgICAgICAgPCEtLSA8ZWwtaW1hZ2Ugdi1mb3I9IihpbWFnZSwgaW5kZXgpIGluIGZvcm0uZHJpdmVyTGljZW5zZUltZ0xpc3QiIDprZXk9ImluZGV4IiA6c3JjPSJpbWFnZSIgZml0PSJjb3ZlciIKICAgICAgICAgICAgbGF6eT48L2VsLWltYWdlPiAtLT4KICAgICAgICAgIDxlbC1pbWFnZSBzdHlsZT0id2lkdGg6IDIwMHB4OyBoZWlnaHQ6IDIwMHB4IiA6c3JjPSJkaXNwYXRjaEZvcm0udmVoaWNsZUxpY2Vuc2VJbWdzIiBmaXQ9ImNvdmVyIj48L2VsLWltYWdlPgogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9Iui0p+i9piIgcHJvcD0iY2FyVVVJZCIgOnJ1bGVzPSJbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+i0p+i9puS/oeaBr+S4jeiDveS4uuepuicgfV0iPgogICAgICAgIDxlbC1zZWxlY3Qgc3R5bGU9IndpZHRoOjMwMHB4IiB2LW1vZGVsPSJkaXNwYXRjaEZvcm0uY2FyVVVJZCIgZmlsdGVyYWJsZSA6ZmlsdGVyLW1ldGhvZD0iZmlsdGVyQ2FyRGF0YSIKICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nvvIjlpoLmnpzmmL7npLrkuI3lh7ror7flnKjovpPlhaXmoYbmkJzntKLvvIkiIEBjaGFuZ2U9ImhhbmRsZUNhckNoYW5nZSI+CiAgICAgICAgICA8ZWwtb3B0aW9uIHYtZm9yPSJpdGVtIGluIGZpbHRlcmVkQ2FyT3B0aW9ucy5zbGljZSgwLCA1MCkiIDprZXk9Iml0ZW0uaWQiIDpsYWJlbD0iaXRlbS5jYXJOdW1iZXIiCiAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS5pZCI+CiAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICA8L2VsLXNlbGVjdD4KCiAgICAgICAgPGVsLWJ1dHRvbiBzdHlsZT0ibWFyZ2luLWxlZnQ6IDEwcHg7IGZvbnQtc2l6ZTogMTRweDsgcGFkZGluZzogNXB4IDEwcHg7IiB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgQGNsaWNrPSJvcGVuTmV3Q2FyV2luZG93Ij7liY3lvoDmlrDlop7miJbkv67mlLnotKfovabkv6Hmga8KICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLovabniYzlj7ciIHByb3A9ImNhck51bWJlciIgdi1pZj0iZGlzcGF0Y2hGb3JtLmNhck51bWJlciAhPSBudWxsIj4KICAgICAgICA8ZWwtaW5wdXQgdi1tb2RlbD0iZGlzcGF0Y2hGb3JtLmNhck51bWJlciIgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpei9pueJjOWPtyIgZGlzYWJsZWQgc3R5bGU9IndpZHRoOjMwMHB4IiAvPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6L2m6L6G5o6S5pS+5qCH5YeGIiBwcm9wPSJ2ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMiCiAgICAgICAgdi1pZj0iZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyAhPSBudWxsIj4KICAgICAgICA8ZWwtc2VsZWN0IHYtbW9kZWw9ImRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMiIHBsYWNlaG9sZGVyPSLor7fpgInmi6novabovobmjpLmlL7moIflh4YiIGRpc2FibGVkCiAgICAgICAgICBzdHlsZT0id2lkdGg6MzAwcHgiPgogICAgICAgICAgPGVsLW9wdGlvbiB2LWZvcj0iZGljdCBpbiB2ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHNPcHRpb25zIiA6a2V5PSJkaWN0LmRpY3RWYWx1ZSIgOmxhYmVsPSJkaWN0LmRpY3RMYWJlbCIKICAgICAgICAgICAgOnZhbHVlPSJkaWN0LmRpY3RWYWx1ZSI+PC9lbC1vcHRpb24+CiAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Lu75Yqh57G75Z6LIiBwcm9wPSJ0YXNrVHlwZSIgOnJ1bGVzPSJbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+S7u+WKoeexu+Wei+S4jeiDveS4uuepuicgfV0iCiAgICAgICAgdi1pZj0iaXNUYXNrVHlwZUVkaXQgPT0gdHJ1ZSI+CiAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJkaXNwYXRjaEZvcm0udGFza1R5cGUiIHBsYWNlaG9sZGVyPSLor7fpgInmi6novabku7vliqHnsbvlnosiIHN0eWxlPSJ3aWR0aDozMDBweCI+CiAgICAgICAgICA8ZWwtb3B0aW9uIHYtZm9yPSJkaWN0IGluIHRhc2tUeXBlT3B0aW9ucyIgOmtleT0iZGljdC5kaWN0VmFsdWUiIDpsYWJlbD0iZGljdC5kaWN0TGFiZWwiCiAgICAgICAgICAgIDp2YWx1ZT0iZGljdC5kaWN0VmFsdWUiPjwvZWwtb3B0aW9uPgogICAgICAgIDwvZWwtc2VsZWN0PgogICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICAgIDwhLS0g5paw5aKe54mp6LWE6YCJ5oup6KGo5qC8IC0tPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLnianotYTpgInmi6kiIHByb3A9InNlbGVjdGVkTWF0ZXJpYWxzIgogICAgICAgIHYtaWY9InBsYW5JbmZvLm1lYXN1cmVGbGFnID09IDAgJiYgZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID09IDIiPgogICAgICAgIDxlbC10YWJsZSA6ZGF0YT0ibWF0ZXJpYWxTZWxlY3Rpb25MaXN0IiBib3JkZXIgc3R5bGU9IndpZHRoOiAxMDAlIj4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gdHlwZT0iaW5kZXgiIHdpZHRoPSI1MCIgbGFiZWw9IuW6j+WPtyI+PC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9Im1hdGVyaWFsTmFtZSIgbGFiZWw9IueJqei1hOWQjeensCIgd2lkdGg9IjE4MCI+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJzY29wZS5yb3cubWF0ZXJpYWxJZCIgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeeJqei1hCIKICAgICAgICAgICAgICAgIEBjaGFuZ2U9ImhhbmRsZU1hdGVyaWFsQ2hhbmdlKHNjb3BlLnJvdywgc2NvcGUuJGluZGV4KSI+CiAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uIHYtZm9yPSJpdGVtIGluIGF2YWlsYWJsZU1hdGVyaWFscyIgOmtleT0iaXRlbS5tYXRlcmlhbElkIiA6bGFiZWw9Iml0ZW0ubWF0ZXJpYWxOYW1lIgogICAgICAgICAgICAgICAgICA6dmFsdWU9Iml0ZW0ubWF0ZXJpYWxJZCIgOmRpc2FibGVkPSJpc01hdGVyaWFsQXZhaWxhYmxlKGl0ZW0pIj4KCiAgICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJtYXRlcmlhbFNwZWMiIGxhYmVsPSLnianotYTop4TmoLwiIHdpZHRoPSIxODAiPgogICAgICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgICAgIHt7IHNjb3BlLnJvdy5tYXRlcmlhbFNwZWMgfX0KICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbiBwcm9wPSJwbGFuTnVtIiBsYWJlbD0i6K6h5YiS5pWw6YePIiB3aWR0aD0iMTIwIj4KICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICB7eyBzY29wZS5yb3cucGxhbk51bSB9fQogICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9InJlbWFpbmluZ051bSIgbGFiZWw9IuWJqeS9meaVsOmHjyIgd2lkdGg9IjEyMCI+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAge3sgc2NvcGUucm93LnJlbWFpbmluZ051bSB9fQogICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uIHByb3A9ImN1cnJlbnROdW0iIGxhYmVsPSLmnKzmrKHmlbDph48iIHdpZHRoPSIxNTAiPgogICAgICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgICAgIDxlbC1pbnB1dC1udW1iZXIgdi1tb2RlbD0ic2NvcGUucm93LmN1cnJlbnROdW0iIDptaW49IjAiCiAgICAgICAgICAgICAgICBAY2hhbmdlPSJoYW5kbGVOdW1DaGFuZ2UoJGV2ZW50LCBzY29wZS4kaW5kZXgpIiA6ZGlzYWJsZWQ9IiFzY29wZS5yb3cubWF0ZXJpYWxJZCI+CiAgICAgICAgICAgICAgPC9lbC1pbnB1dC1udW1iZXI+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuaTjeS9nCIgbWluLXdpZHRoPSI4MCI+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJ0ZXh0IiBpY29uPSJlbC1pY29uLWRlbGV0ZSIgQGNsaWNrPSJyZW1vdmVNYXRlcmlhbChzY29wZS4kaW5kZXgpIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJtYXRlcmlhbFNlbGVjdGlvbkxpc3QubGVuZ3RoID09PSAxIj4KICAgICAgICAgICAgICAgIOWIoOmZpAogICAgICAgICAgICAgIDwvZWwtYnV0dG9uPgogICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgPC9lbC10YWJsZT4KICAgICAgICA8ZGl2IHN0eWxlPSJtYXJnaW4tdG9wOiAxMHB4OyI+CiAgICAgICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIHNpemU9InNtYWxsIiBpY29uPSJlbC1pY29uLXBsdXMiIEBjbGljaz0iYWRkTWF0ZXJpYWxSb3ciPua3u+WKoOeJqei1hDwvZWwtYnV0dG9uPgogICAgICAgIDwvZGl2PgogICAgICA8L2VsLWZvcm0taXRlbT4KCiAgICA8L2VsLWZvcm0+CiAgICA8ZGl2IHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iZGlzcGF0Y2hEaWFsb2dWaXNpYmxlID0gZmFsc2UiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9InN1Ym1pdERpc3BhdGNoRm9ybSI+56GuIOiupDwvZWwtYnV0dG9uPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CjwvZGl2Pgo="}, null]}