package com.ruoyi.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 大门位置工具类
 */
public class GateLocationUtils {
    private static final Logger log = LoggerFactory.getLogger(GateLocationUtils.class);
    private static GateLocation GATE_LOCATION = null;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String CONFIG_FILE_PATH = "C:\\Users\\<USER>\\GateLocation\\gate-locations.json";

    static {
        try {
            loadGateLocation();
        } catch (Exception e) {
            log.error("加载大门位置配置失败", e);
        }
    }

    /**
     * 大门位置信息类
     */
    public static class GateLocation {
        private String code;
        private String name;
        private int postag;
        private BigDecimal latitude;
        private BigDecimal longitude;
        private String description;

        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public int getPostag() { return postag; }
        public void setPostag(int postag) { this.postag = postag; }
        public BigDecimal getLatitude() { return latitude; }
        public void setLatitude(BigDecimal latitude) { this.latitude = latitude; }
        public BigDecimal getLongitude() { return longitude; }
        public void setLongitude(BigDecimal longitude) { this.longitude = longitude; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        @Override
        public String toString() {
            return String.format("大门[编号=%s, 名称=%s, postag=%d, 描述=%s]", 
                code, name, postag, description);
        }
    }

    /**
     * 获取当前大门信息
     * @return 当前大门的详细信息
     */
    public static String getCurrentGateInfo() {
        if (GATE_LOCATION == null) {
            return "未加载大门信息";
        }

        StringBuilder info = new StringBuilder();
        info.append("当前大门信息：\n");
        info.append("----------------------------------------\n");
        info.append(String.format("编号: %s\n", GATE_LOCATION.getCode()));
        info.append(String.format("名称: %s\n", GATE_LOCATION.getName()));
        info.append(String.format("postag: %d\n", GATE_LOCATION.getPostag()));
        info.append(String.format("描述: %s\n", GATE_LOCATION.getDescription()));
        info.append("----------------------------------------\n");

        return info.toString();
    }

    /**
     * 获取当前大门对象
     * @return 当前大门对象
     */
    public static Optional<GateLocation> getCurrentGate() {
        return Optional.ofNullable(GATE_LOCATION);
    }

    /**
     * 从JSON字符串加载大门位置配置（前端传来的数据）
     * @param gateLocationJson 大门位置JSON字符串
     * @return 解析后的大门信息
     */
    public static Optional<GateLocation> loadGateLocationFromJson(String gateLocationJson) {
        if (gateLocationJson == null || gateLocationJson.trim().isEmpty()) {
            log.warn("大门位置JSON字符串为空");
            return Optional.empty();
        }

        try {
            GateLocationConfig config = objectMapper.readValue(gateLocationJson, GateLocationConfig.class);
            if (config.getGates() != null && !config.getGates().isEmpty()) {
                GateLocation gateLocation = config.getGates().get(0); // 只取第一个大门信息
                log.info("成功从JSON解析大门位置配置: {}", gateLocation.getName());
                return Optional.of(gateLocation);
            } else {
                log.error("JSON中没有大门信息");
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("解析大门位置JSON失败", e);
            return Optional.empty();
        }
    }

    /**
     * 加载大门位置配置
     */
    private static void loadGateLocation() throws IOException {
        File configFile = new File(CONFIG_FILE_PATH);
        if (!configFile.exists()) {
            log.error("配置文件不存在: {}", CONFIG_FILE_PATH);
            return;
        }

        GateLocationConfig config = objectMapper.readValue(configFile, GateLocationConfig.class);
        if (config.getGates() != null && !config.getGates().isEmpty()) {
            GATE_LOCATION = config.getGates().get(0); // 只取第一个大门信息
            log.info("成功加载大门位置配置: {}", GATE_LOCATION.getName());
        } else {
            log.error("配置文件中没有大门信息");
        }
    }

    /**
     * 配置类
     */
    private static class GateLocationConfig {
        private List<GateLocation> gates;

        public List<GateLocation> getGates() {
            return gates != null ? gates : Collections.emptyList();
        }

        public void setGates(List<GateLocation> gates) {
            this.gates = gates;
        }
    }
} 