{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\utils\\fileReader.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\utils\\fileReader.js", "mtime": 1756371346144}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["readLocalJsonFile", "filePath", "Promise", "resolve", "reject", "window", "File", "FileReader", "Error", "input", "document", "createElement", "type", "accept", "style", "display", "addEventListener", "event", "file", "target", "files", "name", "toLowerCase", "endsWith", "reader", "onload", "e", "json<PERSON><PERSON><PERSON>", "JSON", "parse", "result", "error", "message", "onerror", "readAsText", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "readGateLocationFile", "expectedPath", "arguments", "length", "undefined", "concat", "confirm", "then", "gates", "Array", "isArray", "firstGate", "code", "postag", "catch", "readGateLocationFileSilent", "content", "console", "warn"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/utils/fileReader.js"], "sourcesContent": ["/**\n * 客户端本地文件读取工具\n */\n\n/**\n * 读取客户端本地JSON文件\n * @param {string} filePath 文件路径\n * @returns {Promise<Object>} 解析后的JSON对象\n */\nexport function readLocalJsonFile(filePath) {\n  return new Promise((resolve, reject) => {\n    // 检查是否支持File API\n    if (!window.File || !window.FileReader) {\n      reject(new Error('浏览器不支持File API'));\n      return;\n    }\n\n    // 创建一个隐藏的文件输入元素\n    const input = document.createElement('input');\n    input.type = 'file';\n    input.accept = '.json';\n    input.style.display = 'none';\n    \n    // 监听文件选择\n    input.addEventListener('change', function(event) {\n      const file = event.target.files[0];\n      if (!file) {\n        reject(new Error('未选择文件'));\n        return;\n      }\n\n      // 检查文件类型\n      if (!file.name.toLowerCase().endsWith('.json')) {\n        reject(new Error('请选择JSON文件'));\n        return;\n      }\n\n      // 读取文件内容\n      const reader = new FileReader();\n      reader.onload = function(e) {\n        try {\n          const jsonContent = JSON.parse(e.target.result);\n          resolve(jsonContent);\n        } catch (error) {\n          reject(new Error('JSON文件格式错误: ' + error.message));\n        }\n      };\n      \n      reader.onerror = function() {\n        reject(new Error('文件读取失败'));\n      };\n      \n      reader.readAsText(file);\n    });\n\n    // 触发文件选择对话框\n    document.body.appendChild(input);\n    input.click();\n    document.body.removeChild(input);\n  });\n}\n\n/**\n * 尝试读取指定路径的大门位置文件\n * 由于浏览器安全限制，无法直接读取指定路径的文件\n * 这个函数会提示用户手动选择文件\n * @param {string} expectedPath 期望的文件路径（仅用于提示）\n * @returns {Promise<Object>} 大门位置配置对象\n */\nexport function readGateLocationFile(expectedPath = 'C:\\\\Users\\\\<USER>\\\\GateLocation\\\\gate-locations.json') {\n  return new Promise((resolve, reject) => {\n    // 显示提示信息\n    const message = `请选择大门位置配置文件：\\n期望路径：${expectedPath}\\n\\n由于浏览器安全限制，需要您手动选择该文件。`;\n    \n    if (!confirm(message)) {\n      reject(new Error('用户取消选择文件'));\n      return;\n    }\n\n    // 调用文件读取函数\n    readLocalJsonFile(expectedPath)\n      .then(jsonContent => {\n        // 验证文件格式\n        if (!jsonContent.gates || !Array.isArray(jsonContent.gates)) {\n          reject(new Error('大门位置文件格式错误：缺少gates数组'));\n          return;\n        }\n\n        if (jsonContent.gates.length === 0) {\n          reject(new Error('大门位置文件中没有大门信息'));\n          return;\n        }\n\n        // 验证第一个大门信息的必要字段\n        const firstGate = jsonContent.gates[0];\n        if (!firstGate.code || !firstGate.name || firstGate.postag === undefined) {\n          reject(new Error('大门位置信息不完整：缺少必要字段'));\n          return;\n        }\n\n        resolve(jsonContent);\n      })\n      .catch(error => {\n        reject(error);\n      });\n  });\n}\n\n/**\n * 自动读取大门位置文件（静默模式）\n * 如果读取失败，返回null而不是抛出错误\n * @returns {Promise<Object|null>} 大门位置配置对象或null\n */\nexport function readGateLocationFileSilent() {\n  return readGateLocationFile()\n    .then(content => content)\n    .catch(error => {\n      console.warn('读取大门位置文件失败:', error.message);\n      return null;\n    });\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASA,iBAAiBA,CAACC,QAAQ,EAAE;EAC1C,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC;IACA,IAAI,CAACC,MAAM,CAACC,IAAI,IAAI,CAACD,MAAM,CAACE,UAAU,EAAE;MACtCH,MAAM,CAAC,IAAII,KAAK,CAAC,gBAAgB,CAAC,CAAC;MACnC;IACF;;IAEA;IACA,IAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAACG,IAAI,GAAG,MAAM;IACnBH,KAAK,CAACI,MAAM,GAAG,OAAO;IACtBJ,KAAK,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;;IAE5B;IACAN,KAAK,CAACO,gBAAgB,CAAC,QAAQ,EAAE,UAASC,KAAK,EAAE;MAC/C,IAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACF,IAAI,EAAE;QACTd,MAAM,CAAC,IAAII,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B;MACF;;MAEA;MACA,IAAI,CAACU,IAAI,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC9CnB,MAAM,CAAC,IAAII,KAAK,CAAC,WAAW,CAAC,CAAC;QAC9B;MACF;;MAEA;MACA,IAAMgB,MAAM,GAAG,IAAIjB,UAAU,CAAC,CAAC;MAC/BiB,MAAM,CAACC,MAAM,GAAG,UAASC,CAAC,EAAE;QAC1B,IAAI;UACF,IAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAACP,MAAM,CAACW,MAAM,CAAC;UAC/C3B,OAAO,CAACwB,WAAW,CAAC;QACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd3B,MAAM,CAAC,IAAII,KAAK,CAAC,cAAc,GAAGuB,KAAK,CAACC,OAAO,CAAC,CAAC;QACnD;MACF,CAAC;MAEDR,MAAM,CAACS,OAAO,GAAG,YAAW;QAC1B7B,MAAM,CAAC,IAAII,KAAK,CAAC,QAAQ,CAAC,CAAC;MAC7B,CAAC;MAEDgB,MAAM,CAACU,UAAU,CAAChB,IAAI,CAAC;IACzB,CAAC,CAAC;;IAEF;IACAR,QAAQ,CAACyB,IAAI,CAACC,WAAW,CAAC3B,KAAK,CAAC;IAChCA,KAAK,CAAC4B,KAAK,CAAC,CAAC;IACb3B,QAAQ,CAACyB,IAAI,CAACG,WAAW,CAAC7B,KAAK,CAAC;EAClC,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS8B,oBAAoBA,CAAA,EAAsE;EAAA,IAArEC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,oDAAoD;EACtG,OAAO,IAAIvC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC;IACA,IAAM4B,OAAO,8GAAAY,MAAA,CAAyBJ,YAAY,uIAA2B;IAE7E,IAAI,CAACK,OAAO,CAACb,OAAO,CAAC,EAAE;MACrB5B,MAAM,CAAC,IAAII,KAAK,CAAC,UAAU,CAAC,CAAC;MAC7B;IACF;;IAEA;IACAR,iBAAiB,CAACwC,YAAY,CAAC,CAC5BM,IAAI,CAAC,UAAAnB,WAAW,EAAI;MACnB;MACA,IAAI,CAACA,WAAW,CAACoB,KAAK,IAAI,CAACC,KAAK,CAACC,OAAO,CAACtB,WAAW,CAACoB,KAAK,CAAC,EAAE;QAC3D3C,MAAM,CAAC,IAAII,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACzC;MACF;MAEA,IAAImB,WAAW,CAACoB,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;QAClCtC,MAAM,CAAC,IAAII,KAAK,CAAC,eAAe,CAAC,CAAC;QAClC;MACF;;MAEA;MACA,IAAM0C,SAAS,GAAGvB,WAAW,CAACoB,KAAK,CAAC,CAAC,CAAC;MACtC,IAAI,CAACG,SAAS,CAACC,IAAI,IAAI,CAACD,SAAS,CAAC7B,IAAI,IAAI6B,SAAS,CAACE,MAAM,KAAKT,SAAS,EAAE;QACxEvC,MAAM,CAAC,IAAII,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACrC;MACF;MAEAL,OAAO,CAACwB,WAAW,CAAC;IACtB,CAAC,CAAC,CACD0B,KAAK,CAAC,UAAAtB,KAAK,EAAI;MACd3B,MAAM,CAAC2B,KAAK,CAAC;IACf,CAAC,CAAC;EACN,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASuB,0BAA0BA,CAAA,EAAG;EAC3C,OAAOf,oBAAoB,CAAC,CAAC,CAC1BO,IAAI,CAAC,UAAAS,OAAO;IAAA,OAAIA,OAAO;EAAA,EAAC,CACxBF,KAAK,CAAC,UAAAtB,KAAK,EAAI;IACdyB,OAAO,CAACC,IAAI,CAAC,aAAa,EAAE1B,KAAK,CAACC,OAAO,CAAC;IAC1C,OAAO,IAAI;EACb,CAAC,CAAC;AACN", "ignoreList": []}]}