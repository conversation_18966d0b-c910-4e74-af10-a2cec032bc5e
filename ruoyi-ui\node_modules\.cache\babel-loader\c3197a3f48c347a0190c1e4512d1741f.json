{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\utils\\fileReader.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\utils\\fileReader.js", "mtime": 1756372537206}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["getClientIp", "Promise", "resolve", "reject", "rtc", "RTCPeerConnection", "iceServers", "urls", "createDataChannel", "createOffer", "then", "offer", "setLocalDescription", "onicecandidate", "event", "candidate", "ipMatch", "match", "close", "setTimeout", "getIpFromService", "ip", "catch", "err", "console", "warn", "error", "services", "attempts", "tryService", "index", "length", "Error", "fetch", "timeout", "response", "json", "data", "origin", "query", "concat", "getClientIpSilent", "message", "readLocalJsonFile", "filePath", "window", "File", "FileReader", "input", "document", "createElement", "type", "accept", "style", "display", "addEventListener", "file", "target", "files", "name", "toLowerCase", "endsWith", "reader", "onload", "e", "json<PERSON><PERSON><PERSON>", "JSON", "parse", "result", "onerror", "readAsText", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "readGateLocationFile", "expectedPath", "arguments", "undefined", "confirm", "gates", "Array", "isArray", "firstGate", "code", "postag", "readGateLocationFileSilent", "content"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/utils/fileReader.js"], "sourcesContent": ["/**\n * 客户端工具类\n */\n\n/**\n * 获取客户端IP地址\n * @returns {Promise<string>} 客户端IP地址\n */\nexport function getClientIp() {\n  return new Promise((resolve, reject) => {\n    // 方法1：通过WebRTC获取本地IP（可能被浏览器限制）\n    try {\n      const rtc = new RTCPeerConnection({\n        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]\n      });\n\n      rtc.createDataChannel('');\n      rtc.createOffer().then(offer => rtc.setLocalDescription(offer));\n\n      rtc.onicecandidate = function(event) {\n        if (event.candidate) {\n          const candidate = event.candidate.candidate;\n          const ipMatch = candidate.match(/(\\d+\\.\\d+\\.\\d+\\.\\d+)/);\n          if (ipMatch && ipMatch[1] !== '127.0.0.1') {\n            rtc.close();\n            resolve(ipMatch[1]);\n            return;\n          }\n        }\n      };\n\n      // 超时处理\n      setTimeout(() => {\n        rtc.close();\n        // 方法2：通过第三方服务获取公网IP（备用方案）\n        getIpFromService()\n          .then(ip => resolve(ip))\n          .catch(err => {\n            console.warn('无法获取客户端IP地址:', err);\n            resolve('127.0.0.1'); // 返回默认IP\n          });\n      }, 3000);\n\n    } catch (error) {\n      // 方法2：通过第三方服务获取IP\n      getIpFromService()\n        .then(ip => resolve(ip))\n        .catch(err => {\n          console.warn('无法获取客户端IP地址:', err);\n          resolve('127.0.0.1'); // 返回默认IP\n        });\n    }\n  });\n}\n\n/**\n * 通过第三方服务获取IP地址（备用方案）\n * @returns {Promise<string>} IP地址\n */\nfunction getIpFromService() {\n  return new Promise((resolve, reject) => {\n    // 可以使用多个服务作为备选\n    const services = [\n      'https://api.ipify.org?format=json',\n      'https://ipapi.co/json/',\n      'https://httpbin.org/ip'\n    ];\n\n    let attempts = 0;\n\n    function tryService(index) {\n      if (index >= services.length) {\n        reject(new Error('所有IP服务都无法访问'));\n        return;\n      }\n\n      fetch(services[index], { timeout: 5000 })\n        .then(response => response.json())\n        .then(data => {\n          let ip = data.ip || data.origin || data.query;\n          if (ip) {\n            resolve(ip);\n          } else {\n            tryService(index + 1);\n          }\n        })\n        .catch(error => {\n          console.warn(`IP服务 ${services[index]} 访问失败:`, error);\n          tryService(index + 1);\n        });\n    }\n\n    tryService(0);\n  });\n}\n\n/**\n * 获取客户端IP地址（静默模式）\n * 如果获取失败，返回默认IP而不抛出错误\n * @returns {Promise<string>} IP地址\n */\nexport function getClientIpSilent() {\n  return getClientIp()\n    .then(ip => ip)\n    .catch(error => {\n      console.warn('获取客户端IP失败，使用默认IP:', error.message);\n      return '127.0.0.1';\n    });\n}\n\n/**\n * 读取客户端本地JSON文件\n * @param {string} filePath 文件路径\n * @returns {Promise<Object>} 解析后的JSON对象\n */\nexport function readLocalJsonFile(filePath) {\n  return new Promise((resolve, reject) => {\n    // 检查是否支持File API\n    if (!window.File || !window.FileReader) {\n      reject(new Error('浏览器不支持File API'));\n      return;\n    }\n\n    // 创建一个隐藏的文件输入元素\n    const input = document.createElement('input');\n    input.type = 'file';\n    input.accept = '.json';\n    input.style.display = 'none';\n    \n    // 监听文件选择\n    input.addEventListener('change', function(event) {\n      const file = event.target.files[0];\n      if (!file) {\n        reject(new Error('未选择文件'));\n        return;\n      }\n\n      // 检查文件类型\n      if (!file.name.toLowerCase().endsWith('.json')) {\n        reject(new Error('请选择JSON文件'));\n        return;\n      }\n\n      // 读取文件内容\n      const reader = new FileReader();\n      reader.onload = function(e) {\n        try {\n          const jsonContent = JSON.parse(e.target.result);\n          resolve(jsonContent);\n        } catch (error) {\n          reject(new Error('JSON文件格式错误: ' + error.message));\n        }\n      };\n      \n      reader.onerror = function() {\n        reject(new Error('文件读取失败'));\n      };\n      \n      reader.readAsText(file);\n    });\n\n    // 触发文件选择对话框\n    document.body.appendChild(input);\n    input.click();\n    document.body.removeChild(input);\n  });\n}\n\n/**\n * 尝试读取指定路径的大门位置文件\n * 由于浏览器安全限制，无法直接读取指定路径的文件\n * 这个函数会提示用户手动选择文件\n * @param {string} expectedPath 期望的文件路径（仅用于提示）\n * @returns {Promise<Object>} 大门位置配置对象\n */\nexport function readGateLocationFile(expectedPath = 'C:\\\\Users\\\\<USER>\\\\GateLocation\\\\gate-locations.json') {\n  return new Promise((resolve, reject) => {\n    // 显示提示信息\n    const message = `请选择大门位置配置文件：\\n期望路径：${expectedPath}\\n\\n由于浏览器安全限制，需要您手动选择该文件。`;\n    \n    if (!confirm(message)) {\n      reject(new Error('用户取消选择文件'));\n      return;\n    }\n\n    // 调用文件读取函数\n    readLocalJsonFile(expectedPath)\n      .then(jsonContent => {\n        // 验证文件格式\n        if (!jsonContent.gates || !Array.isArray(jsonContent.gates)) {\n          reject(new Error('大门位置文件格式错误：缺少gates数组'));\n          return;\n        }\n\n        if (jsonContent.gates.length === 0) {\n          reject(new Error('大门位置文件中没有大门信息'));\n          return;\n        }\n\n        // 验证第一个大门信息的必要字段\n        const firstGate = jsonContent.gates[0];\n        if (!firstGate.code || !firstGate.name || firstGate.postag === undefined) {\n          reject(new Error('大门位置信息不完整：缺少必要字段'));\n          return;\n        }\n\n        resolve(jsonContent);\n      })\n      .catch(error => {\n        reject(error);\n      });\n  });\n}\n\n/**\n * 自动读取大门位置文件（静默模式）\n * 如果读取失败，返回null而不是抛出错误\n * @returns {Promise<Object|null>} 大门位置配置对象或null\n */\nexport function readGateLocationFileSilent() {\n  return readGateLocationFile()\n    .then(content => content)\n    .catch(error => {\n      console.warn('读取大门位置文件失败:', error.message);\n      return null;\n    });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACO,SAASA,WAAWA,CAAA,EAAG;EAC5B,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC;IACA,IAAI;MACF,IAAMC,GAAG,GAAG,IAAIC,iBAAiB,CAAC;QAChCC,UAAU,EAAE,CAAC;UAAEC,IAAI,EAAE;QAA+B,CAAC;MACvD,CAAC,CAAC;MAEFH,GAAG,CAACI,iBAAiB,CAAC,EAAE,CAAC;MACzBJ,GAAG,CAACK,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,UAAAC,KAAK;QAAA,OAAIP,GAAG,CAACQ,mBAAmB,CAACD,KAAK,CAAC;MAAA,EAAC;MAE/DP,GAAG,CAACS,cAAc,GAAG,UAASC,KAAK,EAAE;QACnC,IAAIA,KAAK,CAACC,SAAS,EAAE;UACnB,IAAMA,SAAS,GAAGD,KAAK,CAACC,SAAS,CAACA,SAAS;UAC3C,IAAMC,OAAO,GAAGD,SAAS,CAACE,KAAK,CAAC,sBAAsB,CAAC;UACvD,IAAID,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;YACzCZ,GAAG,CAACc,KAAK,CAAC,CAAC;YACXhB,OAAO,CAACc,OAAO,CAAC,CAAC,CAAC,CAAC;YACnB;UACF;QACF;MACF,CAAC;;MAED;MACAG,UAAU,CAAC,YAAM;QACff,GAAG,CAACc,KAAK,CAAC,CAAC;QACX;QACAE,gBAAgB,CAAC,CAAC,CACfV,IAAI,CAAC,UAAAW,EAAE;UAAA,OAAInB,OAAO,CAACmB,EAAE,CAAC;QAAA,EAAC,CACvBC,KAAK,CAAC,UAAAC,GAAG,EAAI;UACZC,OAAO,CAACC,IAAI,CAAC,cAAc,EAAEF,GAAG,CAAC;UACjCrB,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC;MACN,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACd;MACAN,gBAAgB,CAAC,CAAC,CACfV,IAAI,CAAC,UAAAW,EAAE;QAAA,OAAInB,OAAO,CAACmB,EAAE,CAAC;MAAA,EAAC,CACvBC,KAAK,CAAC,UAAAC,GAAG,EAAI;QACZC,OAAO,CAACC,IAAI,CAAC,cAAc,EAAEF,GAAG,CAAC;QACjCrB,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC;IACN;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASkB,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,IAAInB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC;IACA,IAAMwB,QAAQ,GAAG,CACf,mCAAmC,EACnC,wBAAwB,EACxB,wBAAwB,CACzB;IAED,IAAIC,QAAQ,GAAG,CAAC;IAEhB,SAASC,UAAUA,CAACC,KAAK,EAAE;MACzB,IAAIA,KAAK,IAAIH,QAAQ,CAACI,MAAM,EAAE;QAC5B5B,MAAM,CAAC,IAAI6B,KAAK,CAAC,aAAa,CAAC,CAAC;QAChC;MACF;MAEAC,KAAK,CAACN,QAAQ,CAACG,KAAK,CAAC,EAAE;QAAEI,OAAO,EAAE;MAAK,CAAC,CAAC,CACtCxB,IAAI,CAAC,UAAAyB,QAAQ;QAAA,OAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC;MAAA,EAAC,CACjC1B,IAAI,CAAC,UAAA2B,IAAI,EAAI;QACZ,IAAIhB,EAAE,GAAGgB,IAAI,CAAChB,EAAE,IAAIgB,IAAI,CAACC,MAAM,IAAID,IAAI,CAACE,KAAK;QAC7C,IAAIlB,EAAE,EAAE;UACNnB,OAAO,CAACmB,EAAE,CAAC;QACb,CAAC,MAAM;UACLQ,UAAU,CAACC,KAAK,GAAG,CAAC,CAAC;QACvB;MACF,CAAC,CAAC,CACDR,KAAK,CAAC,UAAAI,KAAK,EAAI;QACdF,OAAO,CAACC,IAAI,mBAAAe,MAAA,CAASb,QAAQ,CAACG,KAAK,CAAC,iCAAUJ,KAAK,CAAC;QACpDG,UAAU,CAACC,KAAK,GAAG,CAAC,CAAC;MACvB,CAAC,CAAC;IACN;IAEAD,UAAU,CAAC,CAAC,CAAC;EACf,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASY,iBAAiBA,CAAA,EAAG;EAClC,OAAOzC,WAAW,CAAC,CAAC,CACjBU,IAAI,CAAC,UAAAW,EAAE;IAAA,OAAIA,EAAE;EAAA,EAAC,CACdC,KAAK,CAAC,UAAAI,KAAK,EAAI;IACdF,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAEC,KAAK,CAACgB,OAAO,CAAC;IAChD,OAAO,WAAW;EACpB,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EAC1C,OAAO,IAAI3C,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC;IACA,IAAI,CAAC0C,MAAM,CAACC,IAAI,IAAI,CAACD,MAAM,CAACE,UAAU,EAAE;MACtC5C,MAAM,CAAC,IAAI6B,KAAK,CAAC,gBAAgB,CAAC,CAAC;MACnC;IACF;;IAEA;IACA,IAAMgB,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAACG,IAAI,GAAG,MAAM;IACnBH,KAAK,CAACI,MAAM,GAAG,OAAO;IACtBJ,KAAK,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;;IAE5B;IACAN,KAAK,CAACO,gBAAgB,CAAC,QAAQ,EAAE,UAASzC,KAAK,EAAE;MAC/C,IAAM0C,IAAI,GAAG1C,KAAK,CAAC2C,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACF,IAAI,EAAE;QACTrD,MAAM,CAAC,IAAI6B,KAAK,CAAC,OAAO,CAAC,CAAC;QAC1B;MACF;;MAEA;MACA,IAAI,CAACwB,IAAI,CAACG,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC9C1D,MAAM,CAAC,IAAI6B,KAAK,CAAC,WAAW,CAAC,CAAC;QAC9B;MACF;;MAEA;MACA,IAAM8B,MAAM,GAAG,IAAIf,UAAU,CAAC,CAAC;MAC/Be,MAAM,CAACC,MAAM,GAAG,UAASC,CAAC,EAAE;QAC1B,IAAI;UACF,IAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAACP,MAAM,CAACW,MAAM,CAAC;UAC/ClE,OAAO,CAAC+D,WAAW,CAAC;QACtB,CAAC,CAAC,OAAOvC,KAAK,EAAE;UACdvB,MAAM,CAAC,IAAI6B,KAAK,CAAC,cAAc,GAAGN,KAAK,CAACgB,OAAO,CAAC,CAAC;QACnD;MACF,CAAC;MAEDoB,MAAM,CAACO,OAAO,GAAG,YAAW;QAC1BlE,MAAM,CAAC,IAAI6B,KAAK,CAAC,QAAQ,CAAC,CAAC;MAC7B,CAAC;MAED8B,MAAM,CAACQ,UAAU,CAACd,IAAI,CAAC;IACzB,CAAC,CAAC;;IAEF;IACAP,QAAQ,CAACsB,IAAI,CAACC,WAAW,CAACxB,KAAK,CAAC;IAChCA,KAAK,CAACyB,KAAK,CAAC,CAAC;IACbxB,QAAQ,CAACsB,IAAI,CAACG,WAAW,CAAC1B,KAAK,CAAC;EAClC,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS2B,oBAAoBA,CAAA,EAAsE;EAAA,IAArEC,YAAY,GAAAC,SAAA,CAAA9C,MAAA,QAAA8C,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,oDAAoD;EACtG,OAAO,IAAI5E,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC;IACA,IAAMuC,OAAO,8GAAAF,MAAA,CAAyBoC,YAAY,uIAA2B;IAE7E,IAAI,CAACG,OAAO,CAACrC,OAAO,CAAC,EAAE;MACrBvC,MAAM,CAAC,IAAI6B,KAAK,CAAC,UAAU,CAAC,CAAC;MAC7B;IACF;;IAEA;IACAW,iBAAiB,CAACiC,YAAY,CAAC,CAC5BlE,IAAI,CAAC,UAAAuD,WAAW,EAAI;MACnB;MACA,IAAI,CAACA,WAAW,CAACe,KAAK,IAAI,CAACC,KAAK,CAACC,OAAO,CAACjB,WAAW,CAACe,KAAK,CAAC,EAAE;QAC3D7E,MAAM,CAAC,IAAI6B,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACzC;MACF;MAEA,IAAIiC,WAAW,CAACe,KAAK,CAACjD,MAAM,KAAK,CAAC,EAAE;QAClC5B,MAAM,CAAC,IAAI6B,KAAK,CAAC,eAAe,CAAC,CAAC;QAClC;MACF;;MAEA;MACA,IAAMmD,SAAS,GAAGlB,WAAW,CAACe,KAAK,CAAC,CAAC,CAAC;MACtC,IAAI,CAACG,SAAS,CAACC,IAAI,IAAI,CAACD,SAAS,CAACxB,IAAI,IAAIwB,SAAS,CAACE,MAAM,KAAKP,SAAS,EAAE;QACxE3E,MAAM,CAAC,IAAI6B,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACrC;MACF;MAEA9B,OAAO,CAAC+D,WAAW,CAAC;IACtB,CAAC,CAAC,CACD3C,KAAK,CAAC,UAAAI,KAAK,EAAI;MACdvB,MAAM,CAACuB,KAAK,CAAC;IACf,CAAC,CAAC;EACN,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS4D,0BAA0BA,CAAA,EAAG;EAC3C,OAAOX,oBAAoB,CAAC,CAAC,CAC1BjE,IAAI,CAAC,UAAA6E,OAAO;IAAA,OAAIA,OAAO;EAAA,EAAC,CACxBjE,KAAK,CAAC,UAAAI,KAAK,EAAI;IACdF,OAAO,CAACC,IAAI,CAAC,aAAa,EAAEC,KAAK,CAACgB,OAAO,CAAC;IAC1C,OAAO,IAAI;EACb,CAAC,CAAC;AACN", "ignoreList": []}]}