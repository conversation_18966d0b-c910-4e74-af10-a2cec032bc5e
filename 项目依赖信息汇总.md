# 项目依赖信息汇总

## 项目概述

该项目是一个基于Spring Boot的多模块Maven项目，名为"兴澄管理系统"，包含16个子模块，涵盖了系统管理、业务处理、设备管理等多个功能领域。

- **项目名称**: 兴澄管理系统
- **项目版本**: 3.3.0
- **Spring Boot版本**: 2.1.18.RELEASE
- **Java版本**: 1.8
- **构建工具**: Maven

## 模块结构

```
ruoyi (根项目)
├── ruoyi-admin (Web服务入口)
├── ruoyi-framework (框架核心)
├── ruoyi-system (系统模块)
├── ruoyi-common (通用工具)
├── ruoyi-quartz (定时任务)
├── ruoyi-generator (代码生成)
├── ruoyi-app (APP模块)
├── ruoyi-questionnaire (问卷模块)
├── ruoyi-lading (运单模块)
├── ruoyi-socket (电文模块)
├── ruoyi-msghandle (电文处理模块)
├── ruoyi-xcerp (产销系统)
├── ruoyi-xctgDevice (点检模块)
├── ruoyi-pda (PDA手持机系统)
└── ruoyi-measure (计量接口系统)
```

## 1. 根项目 (pom.xml)

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi |
| Version | 3.3.0 |
| Packaging | pom |
| Description | 兴澄管理系统 |

### 版本属性配置
| 属性名 | 版本号 | 说明 |
|--------|--------|------|
| ruoyi.version | 3.3.0 | 项目版本 |
| java.version | 1.8 | Java版本 |
| druid.version | 1.2.2 | Druid连接池版本 |
| bitwalker.version | 1.21 | UserAgent解析版本 |
| swagger.version | 2.9.2 | Swagger版本 |
| kaptcha.version | 2.3.2 | 验证码版本 |
| pagehelper.boot.version | 1.3.0 | 分页插件版本 |
| fastjson.version | 1.2.74 | FastJSON版本 |
| oshi.version | 5.3.6 | 系统信息获取版本 |
| jna.version | 5.6.0 | JNA版本 |
| commons.io.version | 2.11.0 | Commons IO版本 |
| commons.fileupload.version | 1.3.3 | 文件上传版本 |
| poi.version | 5.2.2 | POI版本 |
| velocity.version | 1.7 | Velocity版本 |
| jwt.version | 0.9.1 | JWT版本 |

### 依赖管理 (dependencyManagement)
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.springframework.boot | spring-boot-dependencies | 2.1.18.RELEASE | SpringBoot依赖配置 |
| com.alibaba | druid-spring-boot-starter | ${druid.version} | 阿里数据库连接池 |
| eu.bitwalker | UserAgentUtils | ${bitwalker.version} | 解析客户端操作系统、浏览器等 |
| com.github.pagehelper | pagehelper-spring-boot-starter | ${pagehelper.boot.version} | 分页插件 |
| com.github.oshi | oshi-core | ${oshi.version} | 获取系统信息 |
| net.java.dev.jna | jna | ${jna.version} | JNA库 |
| net.java.dev.jna | jna-platform | ${jna.version} | JNA平台库 |
| io.springfox | springfox-swagger2 | ${swagger.version} | Swagger2 |
| io.springfox | springfox-swagger-ui | ${swagger.version} | Swagger2-UI |
| commons-io | commons-io | ${commons.io.version} | IO常用工具类 |
| commons-fileupload | commons-fileupload | ${commons.fileupload.version} | 文件上传工具类 |
| org.apache.poi | poi-ooxml | ${poi.version} | Excel工具 |
| org.apache.logging.log4j | log4j-api | 2.17.1 | 日志API |
| org.apache.velocity | velocity | ${velocity.version} | 代码生成模板 |
| com.alibaba | fastjson | ${fastjson.version} | 阿里JSON解析器 |
| io.jsonwebtoken | jjwt | ${jwt.version} | Token生成与解析 |
| com.github.penggle | kaptcha | ${kaptcha.version} | 验证码 |
| org.apache.axis | axis | 1.4 | Axis |
| javax.xml | jaxrpc-api | 1.1 | JAXRPC API |
| commons-logging | commons-logging | 1.1.1 | Commons Logging |
| commons-discovery | commons-discovery | 0.2 | Commons Discovery |
| com.squareup.okhttp3 | okhttp | 3.14.8 | OkHttp |
| com.sun.mail | javax.mail | 1.6.2 | 邮件 |

## 2. ruoyi-admin 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-admin |
| Version | 3.3.0 |
| Packaging | jar |
| Description | web服务入口 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.springframework.boot | spring-boot-devtools | - | 开发工具 |
| io.springfox | springfox-swagger2 | - | Swagger2 |
| io.swagger | swagger-annotations | 1.5.21 | Swagger注解 |
| io.swagger | swagger-models | 1.5.21 | Swagger模型 |
| io.springfox | springfox-swagger-ui | - | Swagger UI |
| mysql | mysql-connector-java | - | MySQL驱动 |
| com.oracle | ojdbc6 | ******** | Oracle驱动 |
| com.microsoft.sqlserver | sqljdbc4 | 4.0 | SQL Server驱动 |
| org.springframework.boot | spring-boot-starter-mail | - | 邮件 |
| com.jcraft | jsch | 0.1.55 | SSH客户端 |
| fr.opensagres.xdocreport | fr.opensagres.poi.xwpf.converter.pdf | 2.0.4 | Word转PDF |
| fr.opensagres.xdocreport | fr.opensagres.xdocreport.converter | 2.0.4 | PDF转换核心 |

### 构建插件
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.springframework.boot | spring-boot-maven-plugin | 2.1.1.RELEASE | Spring Boot Maven插件 |
| org.apache.maven.plugins | maven-war-plugin | 3.1.0 | War打包插件 |

## 3. ruoyi-common 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-common |
| Version | 3.3.0 |
| Description | common通用工具 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.springframework | spring-context-support | - | Spring框架核心工具 |
| org.springframework | spring-web | - | SpringWeb模块 |
| org.springframework.boot | spring-boot-starter-test | - | 测试 |
| org.springframework.boot | spring-boot-starter-security | - | 安全认证 |
| org.springframework.boot | spring-boot-starter-data-redis | - | Redis缓存 |
| org.springframework.boot | spring-boot-starter-mail | - | 邮件 |
| org.springframework.boot | spring-boot-starter-websocket | - | WebSocket |
| org.springframework.boot | spring-boot-starter-web | - | Web |
| org.springframework.boot | spring-boot-starter-amqp | - | RabbitMQ |
| org.springframework.boot | spring-boot-starter-freemarker | - | FreeMarker |
| org.springframework.boot | spring-boot-configuration-processor | - | 配置注解支持 |
| javax.validation | validation-api | - | 验证注解 |
| org.apache.commons | commons-lang3 | - | 常用工具类 |
| org.apache.commons | commons-pool2 | - | 对象池 |
| com.fasterxml.jackson.core | jackson-databind | - | JSON工具 |
| javax.servlet | javax.servlet-api | - | Servlet包 |
| commons-net | commons-net | 3.6 | 网络工具 |
| org.bouncycastle | bcprov-jdk15on | 1.65 | 加密库 |
| com.belerweb | pinyin4j | 2.5.0 | 拼音转换 |

### PDF处理相关依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.itextpdf | itextpdf | 5.5.11 | PDF处理 |
| com.itextpdf.tool | xmlworker | 5.5.11 | PDF XML工具 |
| com.itextpdf | itext-asian | 5.2.0 | PDF中文支持 |
| org.xhtmlrenderer | flying-saucer-pdf-itext5 | 9.4.1 | CSS样式渲染 |
| net.sf.jtidy | jtidy | r938 | HTML转XHTML |

### 工具库依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| cn.hutool | hutool-core | 5.7.22 | Hutool核心 |
| cn.hutool | hutool-http | 5.7.10 | Hutool HTTP |
| cn.hutool | hutool-all | 5.7.22 | Hutool全部 |
| com.googlecode.aviator | aviator | 5.1.4 | 计算引擎 |
| io.minio | minio | 8.2.1 | MinIO |
| com.alibaba | easyexcel | 3.3.2 | EasyExcel |
| commons-beanutils | commons-beanutils | 1.9.4 | Bean工具 |

### Web服务相关依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.apache.axis | axis-jaxrpc | 1.4 | Axis JAXRPC |
| org.apache.axis | axis-saaj | 1.4 | Axis SAAJ |
| wsdl4j | wsdl4j | 1.4 | WSDL4J |
| com.google.zxing | core | 3.0.0 | 二维码核心 |
| com.google.zxing | javase | 3.0.0 | 二维码JavaSE |
| org.apache.httpcomponents | httpclient | 4.5.13 | HTTP客户端 |
| org.apache.httpcomponents | fluent-hc | 4.5.13 | HTTP流式客户端 |

### POI相关依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.apache.poi | poi | 5.2.2 | POI |
| org.apache.poi | poi-scratchpad | 3.17 | POI Scratchpad |
| com.deepoove | poi-tl | 1.12.1 | POI模板 |
| net.coobird | thumbnailator | 0.4.8 | 图片处理 |

### 其他依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.apache.cxf | cxf-core | 3.4.5 | CXF核心 |
| org.projectlombok | lombok | 1.18.16 | Lombok |
| com.alibaba.fastjson2 | fastjson2 | 2.0.26 | FastJSON2 |
| com.thoughtworks.xstream | xstream | 1.4.11.1 | XStream |
| org.glassfish | jakarta.json | 2.0.1 | Jakarta JSON |
| org.springframework | spring-test | 5.1.6.RELEASE | Spring测试 |
| icu.xuyijie | SM4Utils | 1.4.8 | SM4加密工具 |
| commons-codec | commons-codec | 1.16.0 | 编码工具 |
| com.jcraft | jsch | 0.1.55 | SFTP支持 |
| com.zj.pws | pws | 1.0 | PWS SDK (本地jar) |

## 4. ruoyi-framework 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-framework |
| Version | 3.3.0 |
| Description | framework框架核心 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.springframework.boot | spring-boot-starter-web | - | Web容器 |
| org.springframework.boot | spring-boot-starter-aop | - | 拦截器 |
| com.alibaba | druid-spring-boot-starter | - | 数据库连接池 |
| com.github.penggle | kaptcha | - | 验证码 |
| com.github.oshi | oshi-core | - | 系统信息 |
| com.ruoyi | ruoyi-system | - | 系统模块 |
| com.ruoyi | ruoyi-common | - | 通用工具 |
| org.apache.shardingsphere | sharding-jdbc-core | 4.1.1 | 分库分表 |

## 5. ruoyi-system 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-system |
| Version | 3.3.0 |
| Description | system系统模块 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | - | 通用工具 |

## 6. ruoyi-quartz 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-quartz |
| Version | 3.3.0 |
| Description | quartz定时任务 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.quartz-scheduler | quartz | - | 定时任务 |
| com.ruoyi | ruoyi-common | - | 通用工具 |
| com.ruoyi | ruoyi-xctgDevice | 3.3.0 | 设备模块 |
| com.ruoyi | ruoyi-msghandle | 3.3.0 | 电文处理模块 |
| com.ruoyi | ruoyi-measure | 3.3.0 | 计量模块 |
| com.ruoyi | ruoyi-app | 3.3.0 | APP模块 |

## 7. ruoyi-generator 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-generator |
| Version | 3.3.0 |
| Description | generator代码生成 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| org.apache.velocity | velocity | - | 代码生成模板 |
| com.ruoyi | ruoyi-common | - | 通用工具 |

## 8. ruoyi-app 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-app |
| Version | 3.3.0 |
| Description | APP模块 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| io.swagger | swagger-annotations | 1.5.21 | Swagger注解 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |
| org.springframework.boot | spring-boot-starter-mail | - | 邮件 |
| com.ruoyi | ruoyi-questionnaire | 3.3.0 | 问卷模块 |
| com.ruoyi | ruoyi-xcerp | 3.3.0 | 产销模块 |
| com.ruoyi | ruoyi-socket | 3.3.0 | 电文模块 |
| com.ruoyi | ruoyi-xctgDevice | 3.3.0 | 设备模块 |
| com.ruoyi | ruoyi-system | - | 系统模块 |
| org.apache.axis | axis | - | Axis |
| javax.xml | jaxrpc-api | - | JAXRPC API |
| commons-logging | commons-logging | - | Commons Logging |
| commons-discovery | commons-discovery | - | Commons Discovery |
| com.fasterxml.jackson.core | jackson-databind | 2.9.5 | JSON数据绑定 |
| org.apache.velocity | velocity | - | 模板引擎 |
| org.projectlombok | lombok | 1.18.16 | Lombok |
| org.xhtmlrenderer | flying-saucer-core | 9.4.1 | HTML渲染 |
| org.jsoup | jsoup | 1.17.2 | HTML解析 |
| org.apache.commons | commons-text | 1.9 | 文本工具 |
| com.google.guava | guava | 31.1-jre | Google工具库 |
| org.apache.httpcomponents | httpclient | 4.5.13 | HTTP客户端 |

## 9. ruoyi-questionnaire 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-questionnaire |
| Version | 3.3.0 |
| Description | 问卷类服务入口 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |

## 10. ruoyi-lading 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-lading |
| Version | 3.3.0 |
| Description | 运单模块 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| io.swagger | swagger-annotations | 1.5.21 | Swagger注解 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |

## 11. ruoyi-socket 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-socket |
| Version | 3.3.0 |
| Description | 电文模块 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| io.swagger | swagger-annotations | 1.5.21 | Swagger注解 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |
| org.springframework.boot | spring-boot-starter-web | - | Web |
| org.java-websocket | Java-WebSocket | 1.3.8 | WebSocket核心 |
| com.ruoyi | ruoyi-msghandle | 3.3.0 | 电文处理模块 |
| com.ruoyi | ruoyi-xcerp | 3.3.0 | 产销模块 |

## 12. ruoyi-msghandle 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-msghandle |
| Version | 3.3.0 |
| Description | 电文处理模块 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| io.swagger | swagger-annotations | 1.5.21 | Swagger注解 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |
| org.springframework.boot | spring-boot-starter-web | - | Web |
| com.ruoyi | ruoyi-xcerp | 3.3.0 | 产销模块 |

## 13. ruoyi-xcerp 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-xcerp |
| Version | 3.3.0 |
| Description | 产销系统入口 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |

## 14. ruoyi-xctgDevice 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-xctgDevice |
| Version | 3.3.0 |
| Description | 点检模块 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |
| org.quartz-scheduler | quartz | - | 定时任务 |

## 15. ruoyi-pda 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-pda |
| Version | 3.3.0 |
| Description | pda手持机系统入口 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |

## 16. ruoyi-measure 模块

### 基本信息
| 属性 | 值 |
|------|-----|
| GroupId | com.ruoyi |
| ArtifactId | ruoyi-measure |
| Version | 3.3.0 |
| Description | 计量接口系统 |

### 主要依赖
| GroupId | ArtifactId | Version | 说明 |
|---------|------------|---------|------|
| com.ruoyi | ruoyi-common | 3.3.0 | 通用工具 |
| com.ruoyi | ruoyi-framework | - | 框架核心 |
| com.ruoyi | ruoyi-app | - | APP模块 |

## 技术栈总结

### 核心框架
- **Spring Boot**: 2.1.18.RELEASE
- **Spring Framework**: 由Spring Boot管理版本
- **Spring Security**: 安全认证框架
- **MyBatis**: 数据持久层框架（通过PageHelper集成）

### 数据库相关
- **数据库驱动**:
  - MySQL Connector Java
  - Oracle OJDBC6 (********)
  - SQL Server SQLJDBC4 (4.0)
- **连接池**: Alibaba Druid (1.2.2)
- **分库分表**: Apache ShardingSphere (4.1.1)
- **缓存**: Spring Boot Redis Starter

### Web相关
- **Web框架**: Spring Boot Web Starter
- **WebSocket**: Spring Boot WebSocket Starter + Java-WebSocket (1.3.8)
- **API文档**: Swagger2 (2.9.2)
- **模板引擎**: FreeMarker

### 工具库
- **JSON处理**:
  - FastJSON (1.2.74)
  - FastJSON2 (2.0.26)
  - Jackson Databind
- **Excel处理**:
  - Apache POI (5.2.2)
  - EasyExcel (3.3.2)
  - POI-TL (1.12.1)
- **PDF处理**:
  - iText (5.5.11)
  - Flying Saucer (9.4.1)
- **HTTP客户端**:
  - OkHttp (3.14.8)
  - Apache HttpClient (4.5.13)
- **工具类**:
  - Hutool (5.7.22)
  - Apache Commons Lang3
  - Google Guava (31.1-jre)

### 任务调度
- **定时任务**: Quartz Scheduler

### 消息队列
- **RabbitMQ**: Spring Boot AMQP Starter

### 代码生成
- **模板引擎**: Apache Velocity (1.7)

### 安全相关
- **JWT**: JJWT (0.9.1)
- **验证码**: Kaptcha (2.3.2)
- **加密**: BouncyCastle (1.65)
- **SM4加密**: SM4Utils (1.4.8)

### 系统监控
- **系统信息**: OSHI (5.3.6)
- **用户代理解析**: UserAgentUtils (1.21)

### 文件存储
- **对象存储**: MinIO (8.2.1)
- **文件上传**: Commons FileUpload (1.3.3)

### 其他工具
- **二维码**: ZXing (3.0.0)
- **图片处理**: Thumbnailator (0.4.8)
- **拼音转换**: Pinyin4J (2.5.0)
- **计算引擎**: Aviator (5.1.4)
- **XML处理**: XStream (1.4.11.1)
- **网络工具**: Commons Net (3.9.0)
- **SSH/SFTP**: JSch (0.1.55)

## 模块依赖关系

```
ruoyi-admin (主启动模块)
├── ruoyi-framework (框架核心)
│   ├── ruoyi-system (系统模块)
│   │   └── ruoyi-common (通用工具)
│   └── ruoyi-common
├── ruoyi-quartz (定时任务)
│   └── ruoyi-common
├── ruoyi-generator (代码生成)
│   └── ruoyi-common
├── ruoyi-app (APP模块)
│   ├── ruoyi-framework
│   ├── ruoyi-questionnaire
│   ├── ruoyi-xcerp
│   ├── ruoyi-socket
│   ├── ruoyi-xctgDevice
│   └── ruoyi-system
├── ruoyi-questionnaire (问卷模块)
│   ├── ruoyi-framework
│   └── ruoyi-common
├── ruoyi-lading (运单模块)
│   ├── ruoyi-framework
│   └── ruoyi-common
├── ruoyi-socket (电文模块)
│   ├── ruoyi-framework
│   ├── ruoyi-common
│   ├── ruoyi-msghandle
│   └── ruoyi-xcerp
├── ruoyi-msghandle (电文处理)
│   ├── ruoyi-framework
│   ├── ruoyi-common
│   └── ruoyi-xcerp
├── ruoyi-xcerp (产销系统)
│   ├── ruoyi-framework
│   └── ruoyi-common
├── ruoyi-xctgDevice (点检模块)
│   ├── ruoyi-framework
│   └── ruoyi-common
├── ruoyi-pda (PDA手持机)
│   ├── ruoyi-framework
│   └── ruoyi-common
└── ruoyi-measure (计量接口)
    ├── ruoyi-framework
    ├── ruoyi-common
    └── ruoyi-app
```

## 版本兼容性说明

- **Java版本**: 1.8
- **Maven版本**: 建议3.6+
- **Spring Boot版本**: 2.1.18.RELEASE
- **数据库支持**: MySQL、Oracle、SQL Server

## 构建说明

项目使用Maven进行构建管理，主要构建插件：
- **maven-compiler-plugin**: 3.1
- **spring-boot-maven-plugin**: 2.1.1.RELEASE
- **maven-war-plugin**: 3.1.0

## 仓库配置

项目配置了阿里云Maven仓库作为依赖下载源：
- **仓库地址**: http://maven.aliyun.com/nexus/content/groups/public/

---

*文档生成时间: 2025-08-28*
*项目版本: 3.3.0*
