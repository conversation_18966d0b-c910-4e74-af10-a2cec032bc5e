{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\plan\\detail.vue", "mtime": 1756369610407}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBkZXRhaWxQbGFuLCBhcHByb3ZlLCBkaXNjYXJkLCBsaXN0VGFza01hdGVyaWFsLCBjb25maXJtTWF0ZXJpYWwgfSBmcm9tICJAL2FwaS9sZWF2ZS9wbGFuIjsNCmltcG9ydCB7IGxpc3RBbGxUYXNrLCBhZGRUYXNrLCBhZGRUYXNrTWF0ZXJpYWwsIGFkZFRhc2tBbmRNYXRlcmlhbCwgYWRkTGVhdmVMb2csIGlzQWxsb3dEaXNwYXRjaCwgYWRkVGFza0FuZE1hdGVyaWFsQW5kQWRkTGVhdmVMb2cgfSBmcm9tICJAL2FwaS9sZWF2ZS90YXNrIjsNCmltcG9ydCB7IGxpc3RBbGxEcml2ZXIsICBnZXRYY3RnRHJpdmVyVXNlckxpc3RCeVBhZ2UsIGdldFhjdGdEcml2ZXJDYXJMaXN0QnlQYWdlIH0gZnJvbSAiQC9hcGkvZGdjYi9kcml2ZXIvZHJpdmVyIjsNCmltcG9ydCB7IG1vdW50IH0gZnJvbSAic29ydGFibGVqcyI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJEZXRhaWxMZWF2ZVBsYW4iLA0KICBkYXRhKCkgew0KICAgIC8vIOmqjOivgei9pueJjOWPtw0KICAgIGNvbnN0IHZhbGlkYXRlQ2FyTnVtYmVyID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgY29uc3QgcGF0dGVybiA9IC9eW+S6rOa0peayqua4neWGgOixq+S6kei+vem7kea5mOealumygeaWsOiLj+a1mei1o+mEguahgueUmOaZi+iSmemZleWQiemXvei0teeypOmdkuiXj+W3neWugeeQvOS9v+mihkEtWl17MX1bQS1aXXsxfVtBLVowLTldezR9W0EtWjAtOeaMguWtpuitpua4r+a+s117MX0kLzsNCiAgICAgIGlmICghcGF0dGVybi50ZXN0KHZhbHVlKSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpeato+ehrueahOi9pueJjOWPtycpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICB9DQogICAgfTsNCg0KICAgIC8vIOmqjOivgeaJi+acuuWPtw0KICAgIGNvbnN0IHZhbGlkYXRlUGhvbmUgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBjb25zdCBwYXR0ZXJuID0gL14xWzMtOV1cZHs5fSQvOw0KICAgICAgaWYgKCFwYXR0ZXJuLnRlc3QodmFsdWUpKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+3JykpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgIH0NCiAgICB9Ow0KDQogICAgLy8g6aqM6K+B6Lqr5Lu96K+B5Y+3DQogICAgY29uc3QgdmFsaWRhdGVJZENhcmQgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBjb25zdCBwYXR0ZXJuID0gLyheXGR7MTV9JCl8KF5cZHsxOH0kKXwoXlxkezE3fShcZHxYfHgpJCkvOw0KICAgICAgaWYgKCFwYXR0ZXJuLnRlc3QodmFsdWUpKSB7DQogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcign6K+36L6T5YWl5q2j56Gu55qE6Lqr5Lu96K+B5Y+3JykpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgIH0NCiAgICB9Ow0KDQogICAgcmV0dXJuIHsNCiAgICAgIGlzVGFza1R5cGVFZGl0OiB0cnVlLA0KICAgICAgdmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzT3B0aW9uczogW10sDQogICAgICB0YXNrVHlwZU9wdGlvbnM6IFtdLA0KICAgICAgY2FyTGlzdDogW10sDQogICAgICBzZWFyY2hDYXJRdWVyeTogJycsDQogICAgICBmaWx0ZXJlZENhck9wdGlvbnM6IFtdLA0KICAgICAgZHJpdmVyTGlzdDogW10sDQogICAgICBzZWFyY2hEcml2ZXJRdWVyeTogJycsDQogICAgICBmaWx0ZXJlZERyaXZlck9wdGlvbnM6IFtdLA0KICAgICAgLy/lrqHmoLjooajljZUNCiAgICAgIGFwcHJvdmVGb3JtOiB7DQogICAgICAgIGFwcGx5Tm86IG51bGwsDQogICAgICAgIGFwcHJvdmVDb250ZW50OiAnJywvL+WuoeaguOaEj+ingQ0KICAgICAgICBhcHByb3ZlRmxhZzogdHJ1ZSwvL+WuoeaguOeKtuaAgQ0KICAgICAgfSwNCg0KICAgICAgLy8g5Zu+54mH5YiX6KGoDQogICAgICBpbWFnZUxpc3Q6IFtdLA0KDQogICAgICAvLyDmlofku7bliJfooagNCiAgICAgIGZpbGVMaXN0OiBbXSwNCg0KICAgICAgLy8g5rS+6L2m5by55qGG5Y+v6KeB5oCnDQogICAgICBkaXNwYXRjaERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KDQogICAgICB0YXNrTGlzdEluZm86IFtdLA0KDQogICAgICAvLyDmtL7ovabooajljZXmlbDmja4NCiAgICAgIGRpc3BhdGNoRm9ybTogew0KICAgICAgICAvLyBjYXJOdW1iZXI6ICcnLA0KICAgICAgICAvLyBkcml2ZXJOYW1lOiAnJywNCiAgICAgICAgLy8gZHJpdmVyUGhvbmU6ICcnLA0KICAgICAgICAvLyBkcml2ZXJJZENhcmQ6ICcnDQogICAgICB9LA0KDQogICAgICAvLyDmtL7ovabooajljZXpqozor4Hop4TliJkNCiAgICAgIGRpc3BhdGNoUnVsZXM6IHsNCiAgICAgICAgY2FyTnVtYmVyOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpei9pueJjOWPtycsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB2YWxpZGF0ZUNhck51bWJlciwgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgZHJpdmVyTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlj7jmnLrlp5PlkI0nLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAyMCwgbWVzc2FnZTogJ+mVv+W6puWcqCAyIOWIsCAyMCDkuKrlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBkcml2ZXJQaG9uZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlj7jmnLrmiYvmnLrlj7cnLCB0cmlnZ2VyOiAnYmx1cicgfSwNCiAgICAgICAgICB7IHZhbGlkYXRvcjogdmFsaWRhdGVQaG9uZSwgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgZHJpdmVySWRDYXJkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWPuOacuui6q+S7veivgeWPtycsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB2YWxpZGF0ZUlkQ2FyZCwgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCg0KICAgICAgLy8g5rS+6L2m5YiX6KGo5pWw5o2uDQogICAgICBkaXNwYXRjaExpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAxLA0KICAgICAgICAgIGNhck51bWJlcjogJ+S6rEExMjM0NScsDQogICAgICAgICAgZHJpdmVyTmFtZTogJ+eOi+Wwj+aYjicsDQogICAgICAgICAgZHJpdmVyUGhvbmU6ICcxMzgwMDEzODAwMCcsDQogICAgICAgICAgZHJpdmVySWRDYXJkOiAnMTEwMTAxMTk5MDAxMDEwMDAxJywNCiAgICAgICAgICBkaXNwYXRjaFRpbWU6ICcyMDI1LTAzLTE4IDA5OjMwOjAwJywNCiAgICAgICAgICBzdGF0dXM6IDIsDQogICAgICAgICAgdGFyZVdlaWdodDogODUwMCwNCiAgICAgICAgICBncm9zc1dlaWdodDogMTU4MDAsDQogICAgICAgICAgcmVjaGVja2VkR3Jvc3NXZWlnaHQ6IDE1NzUwLA0KICAgICAgICAgIHJlY2hlY2tlZFRhcmVXZWlnaHQ6IDg0ODANCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGlkOiAyLA0KICAgICAgICAgIGNhck51bWJlcjogJ+S6rEI5ODc2NScsDQogICAgICAgICAgZHJpdmVyTmFtZTogJ+adjuWkp+WjricsDQogICAgICAgICAgZHJpdmVyUGhvbmU6ICcxMzkwMDEzOTAwMCcsDQogICAgICAgICAgZHJpdmVySWRDYXJkOiAnMTEwMTAxMTk5MTAyMDIwMDAyJywNCiAgICAgICAgICBkaXNwYXRjaFRpbWU6ICcyMDI1LTAzLTE5IDE0OjE1OjAwJywNCiAgICAgICAgICBzdGF0dXM6IDEsDQogICAgICAgICAgdGFyZVdlaWdodDogNzgwMCwNCiAgICAgICAgICBncm9zc1dlaWdodDogMTI2MDAsDQogICAgICAgICAgcmVjaGVja2VkR3Jvc3NXZWlnaHQ6IG51bGwsDQogICAgICAgICAgcmVjaGVja2VkVGFyZVdlaWdodDogbnVsbA0KICAgICAgICB9DQogICAgICBdLA0KDQogICAgICAvLyDorqHliJLor6bmg4Xkv6Hmga8NCiAgICAgIHBsYW5JbmZvOiB7fSwNCiAgICAgIGFwcGx5Tm86IG51bGwsDQogICAgICB0YXNrUXVlcnlQYXJhbXM6IHsNCiAgICAgICAgYXBwbHlObzogbnVsbCwNCiAgICAgIH0sDQoNCiAgICAgIHRhc2tNYXRlcmlhbExpc3Q6IG51bGwsDQogICAgICAvLyDnianotYTpgInmi6nnm7jlhbPmlbDmja4NCiAgICAgIG1hdGVyaWFsU2VsZWN0aW9uTGlzdDogW3sNCiAgICAgICAgbWF0ZXJpYWxJZDogbnVsbCwNCiAgICAgICAgbWF0ZXJpYWxOYW1lOiAnJywNCiAgICAgICAgbWF0ZXJpYWxTcGVjOiAnJywNCiAgICAgICAgcGxhbk51bTogMCwNCiAgICAgICAgdXNlZE51bTogMCwNCiAgICAgICAgcmVtYWluaW5nTnVtOiAwLA0KICAgICAgICBjdXJyZW50TnVtOiAwDQogICAgICB9XSwNCiAgICAgIGF2YWlsYWJsZU1hdGVyaWFsczogW10sIC8vIOWPr+mAieeahOeJqei1hOWIl+ihqA0KICAgICAgdGFza01hdGVyaWFsTGlzdE1hcDogbmV3IE1hcCgpLCAvLyDlt7LmtL7ovabnmoTnianotYTliJfooagNCiAgICAgIHRhc2tNYXRlcmlhbE1hcDogbmV3IE1hcCgpLCAvLyDlrZjlgqjmiYDmnInku7vliqHnianotYTnmoTmmKDlsIQNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOWIpOaWreaYr+WQpuWPr+S7pea0vui9pg0KICAgIGNhbkRpc3BhdGNoQ2FyKCkgew0KICAgICAgLy8g5Yik5pat55Sz6K+35Y2V5piv5ZCm5bey6YCa6L+HDQogICAgICAvLyBjb25zdCBpc1BsYW5BcHByb3ZlZCA9IHRoaXMucGxhbkluZm8ucGxhblN0YXR1cyA9PT0gMjsNCg0KICAgICAgLy8gLy8g5aaC5p6c5piv6Z2e6K6h6YeP57G75Z6L77yM5LiU5bey57uP5rS+6L+H6L2m77yM5YiZ5LiN6IO95YaN5rS+6L2mDQogICAgICAvLyBpZiAodGhpcy5wbGFuSW5mby5tZWFzdXJlRmxhZyAhPT0gMSAmJiB0aGlzLmRpc3BhdGNoTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAvLyAgIHJldHVybiBmYWxzZTsNCiAgICAgIC8vIH0NCg0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCiAgICAvLyDpu5jorqTmmL7npLrliY01MOadoe+8jOiLpeacieaQnOe0ou+8jOWImeaYvuekuuaQnOe0ouWQjueahOaVsOaNrg0KICAgIGRpc3BsYXlEcml2ZXJMaXN0T3B0aW9ucygpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlYXJjaERyaXZlclF1ZXJ5ID8gdGhpcy5maWx0ZXJlZERyaXZlck9wdGlvbnMgOiB0aGlzLmRyaXZlckxpc3Quc2xpY2UoMCwgNTApOw0KICAgIH0sDQogICAgZGlzcGxheUNhckxpc3RPcHRpb25zKCkgew0KICAgICAgcmV0dXJuIHRoaXMuc2VhcmNoQ2FyUXVlcnkgPyB0aGlzLmZpbHRlcmVkQ2FyT3B0aW9ucyA6IHRoaXMuY2FyTGlzdC5zbGljZSgwLCA1MCk7DQogICAgfSwNCiAgICBjYW5TaG93TWF0ZXJpYWxDb25maXJtKCkgew0KICAgICAgLy8g5Y+q5pyJcGxhblN0YXR1c+S4ujXmiJY25pe25pi+56S677yI5bey5Ye65Y6CL+mDqOWIhuaUtui0p++8ie+8jOS4lOS4jeaYr+W3suWujOaIkC/lup/lvIMv6amz5ZueL+i/h+acnw0KICAgICAgcmV0dXJuIFs1LCA2XS5pbmNsdWRlcyh0aGlzLnBsYW5JbmZvLnBsYW5TdGF0dXMpOw0KICAgIH0NCiAgfSwNCiAgYWN0aXZhdGVkKCkgew0KICAgIHRoaXMuZ2V0RGljdHMoInhjdGdfZHJpdmVyX2Nhcl9lbWlzc2lvbl9zdGFuZGFyZHMiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMudmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgfSk7DQogICAgLy8g5Yid5aeL5YyW5Lu75Yqh57G75Z6L6YCJ6aG577yI5Zyo6I635Y+W6K6h5YiS5L+h5oGv5ZCO5Lya6YeN5paw5pu05paw77yJDQogICAgdGhpcy5nZXREaWN0cygibGVhdmVfdGFza190eXBlIikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLnRhc2tUeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgfSk7DQogICAgLy8g6I635Y+W6Lev55Sx5Y+C5pWw5Lit55qESUQNCiAgICBjb25zdCBhcHBseU5vID0gdGhpcy4kcm91dGUucGFyYW1zLmFwcGx5Tm87DQogICAgdGhpcy5hcHBseU5vID0gYXBwbHlObw0KICAgIHRoaXMudGFza1F1ZXJ5UGFyYW1zLmFwcGx5Tm8gPSBhcHBseU5vOw0KICAgIHRoaXMuYXBwcm92ZUZvcm0uYXBwbHlObyA9IGFwcGx5Tm87DQogICAgaWYgKGFwcGx5Tm8pIHsNCiAgICAgIGRldGFpbFBsYW4oYXBwbHlObykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMucGxhbkluZm8gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnRhc2tUeXBlRWRpdFVwZGF0ZSgpOw0KICAgICAgICBjb25zb2xlLmxvZygidGhpcy5wbGFuSW5mbyIsIHRoaXMucGxhbkluZm8pOw0KICAgICAgICAvLyDop6PmnpDlm77niYflkozmlofku7bmlbDmja4NCiAgICAgICAgdGhpcy5wYXJzZUltYWdlQW5kRmlsZURhdGEoKTsNCiAgICAgICAgLy8g6I635Y+W5Lu75Yqh5L+h5oGv5ZCO5pu05paw5Lu75Yqh57G75Z6L6YCJ6aG5DQogICAgICAgIHRoaXMuZ2V0TGlzdFRhc2tJbmZvKCk7DQogICAgICB9KTsNCiAgICB9Ow0KICAgIHRoaXMuZ2V0RHJpdmVyTGlzdCgpOw0KICAgIHRoaXMuZ2V0Q2FyTGlzdCgpOw0KDQoNCg0KICB9LA0KDQoNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUFwcHJvdmUoKSB7DQogICAgICB0aGlzLmFwcHJvdmVGb3JtLmFwcHJvdmVGbGFnID0gdHJ1ZTsNCiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLmFwcHJvdmVGb3JtIiwgdGhpcy5hcHByb3ZlRm9ybSk7DQogICAgICBhcHByb3ZlKHRoaXMuYXBwcm92ZUZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WuoeaguOmAmui/hycpOw0KICAgICAgICAvLyDot7PovazliLDliJfooajpobXpnaLlubbliLfmlrANCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICIvbGVhdmUvbGVhdmVQbGFuTGlzdCIsDQogICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgIHQ6IERhdGUubm93KCksDQogICAgICAgICAgICByZWZyZXNoOiB0cnVlIC8vIOa3u+WKoOWIt+aWsOagh+iusA0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WuoeaguOWksei0pScpOw0KICAgICAgICBjb25zb2xlLmVycm9yKCdBcHByb3ZhbCBlcnJvcjonLCBlcnJvcik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVJlamVjdCgpIHsNCiAgICAgIHRoaXMuYXBwcm92ZUZvcm0uYXBwcm92ZUZsYWcgPSBmYWxzZTsNCiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLmFwcHJvdmVGb3JtIiwgdGhpcy5hcHByb3ZlRm9ybSk7DQogICAgICBhcHByb3ZlKHRoaXMuYXBwcm92ZUZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+mps+WbnuaIkOWKnycpOw0KICAgICAgICAvLyDot7PovazliLDliJfooajpobXpnaLlubbliLfmlrANCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICIvbGVhdmUvbGVhdmVQbGFuTGlzdCIsDQogICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgIHQ6IERhdGUubm93KCksDQogICAgICAgICAgICByZWZyZXNoOiB0cnVlIC8vIOa3u+WKoOWIt+aWsOagh+iusA0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WuoeaguOWksei0pScpOw0KICAgICAgICBjb25zb2xlLmVycm9yKCdBcHByb3ZhbCBlcnJvcjonLCBlcnJvcik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZURpc2NhcmQoKSB7DQogICAgICBkaXNjYXJkKHRoaXMucGxhbkluZm8pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W6n+W8g+aIkOWKnycpOw0KDQogICAgICAgIGlmICh3aW5kb3cuaGlzdG9yeS5sZW5ndGggPiAxKSB7DQogICAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICIvbGVhdmUvbGVhdmVQbGFuTGlzdCIsIHF1ZXJ5OiB7IHQ6IERhdGUubm93KCkgfSB9KTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflup/lvIPlpLHotKUnKTsNCiAgICAgICAgY29uc29sZS5lcnJvcignQXBwcm92YWwgZXJyb3I6JywgZXJyb3IpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICB0YXNrVHlwZUVkaXRVcGRhdGUoKSB7DQogICAgICBpZiAodGhpcy5wbGFuSW5mby5wbGFuVHlwZSAhPT0gMikgew0KICAgICAgICB0aGlzLmlzVGFza1R5cGVFZGl0ID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDmm7TmlrDku7vliqHnsbvlnovpgInpobkNCiAgICB1cGRhdGVUYXNrVHlwZU9wdGlvbnMoKSB7DQogICAgICAvLyDojrflj5bljp/lp4vnmoTku7vliqHnsbvlnovpgInpobkNCiAgICAgIHRoaXMuZ2V0RGljdHMoImxlYXZlX3Rhc2tfdHlwZSIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBsZXQgb3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIGNvbnNvbGUubG9nKCLljp/lp4vku7vliqHnsbvlnovpgInpobk6Iiwgb3B0aW9ucyk7DQogICAgICAgIGNvbnNvbGUubG9nKCLorqHliJLnsbvlnos6IiwgdGhpcy5wbGFuSW5mby5wbGFuVHlwZSk7DQogICAgICAgIGNvbnNvbGUubG9nKCLku7vliqHmlbDph486IiwgdGhpcy50YXNrTGlzdEluZm8ubGVuZ3RoKTsNCg0KICAgICAgICAvLyDlr7nkuo7lh7rljoLov5Tlm57ku7vliqHvvIhwbGFuVHlwZT0y77yJDQogICAgICAgIGlmICh0aGlzLnBsYW5JbmZvLnBsYW5UeXBlID09PSAyKSB7DQogICAgICAgICAgLy8g5aaC5p6c5b2T5YmN5Lu75Yqh5pWw5Li6MO+8jOWPquaYvuekuuWHuuWOgumAiemhuQ0KICAgICAgICAgIGlmICh0aGlzLnRhc2tMaXN0SW5mby5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgIG9wdGlvbnMgPSBvcHRpb25zLmZpbHRlcihvcHRpb24gPT4NCiAgICAgICAgICAgICAgb3B0aW9uLmRpY3RWYWx1ZSA9PT0gJzEnIHx8IG9wdGlvbi5kaWN0VmFsdWUgPT09IDENCiAgICAgICAgICAgICk7IC8vIOWPquS/neeVmeWHuuWOgumAiemhue+8jOWFvOWuueWtl+espuS4suWSjOaVsOWtl+exu+Weiw0KICAgICAgICAgICAgY29uc29sZS5sb2coIui/h+a7pOWQjuWPquS/neeVmeWHuuWOgumAiemhuToiLCBvcHRpb25zKTsNCiAgICAgICAgICB9DQogICAgICAgICAgLy8g5aaC5p6c5bey5pyJ5Lu75Yqh77yM5pi+56S65omA5pyJ6YCJ6aG577yI5Ye65Y6C5ZKM6L+U5Y6C77yJDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5a+55LqO5YW25LuW6K6h5YiS57G75Z6L77yM5L+d5oyB5Y6f5pyJ6YC76L6RDQogICAgICAgICAgaWYgKHRoaXMucGxhbkluZm8ucGxhblR5cGUgIT09IDMpIHsNCiAgICAgICAgICAgIG9wdGlvbnMgPSBvcHRpb25zLmZpbHRlcihvcHRpb24gPT4NCiAgICAgICAgICAgICAgb3B0aW9uLmRpY3RWYWx1ZSAhPT0gJzMnICYmIG9wdGlvbi5kaWN0VmFsdWUgIT09IDMNCiAgICAgICAgICAgICk7IC8vIOenu+mZpOi3qOWMuuiwg+aLqOmAiemhue+8jOWFvOWuueWtl+espuS4suWSjOaVsOWtl+exu+Weiw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCLmnIDnu4jku7vliqHnsbvlnovpgInpobk6Iiwgb3B0aW9ucyk7DQogICAgICAgIHRoaXMudGFza1R5cGVPcHRpb25zID0gb3B0aW9uczsNCiAgICAgICAgLy8g5by65Yi25Yi35paw5LiL5ouJ5qGGDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0TGlzdFRhc2tJbmZvKCkgew0KICAgICAgbGlzdEFsbFRhc2sodGhpcy50YXNrUXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygicmVzcG9uc2UuZGF0YSIsIHJlc3BvbnNlLnJvd3MpOw0KICAgICAgICB0aGlzLnRhc2tMaXN0SW5mbyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnRhc2tMaXN0SW5mbyIsIHRoaXMudGFza0xpc3RJbmZvKTsNCiAgICAgICAgLy8g6I635Y+W5omA5pyJ5Lu75Yqh54mp6LWEDQogICAgICAgIHRoaXMuZ2V0QWxsVGFza01hdGVyaWFscygpOw0KICAgICAgICAvLyDmm7TmlrDku7vliqHnsbvlnovpgInpobnvvIjln7rkuo7lvZPliY3ku7vliqHmlbDvvIkNCiAgICAgICAgdGhpcy51cGRhdGVUYXNrVHlwZU9wdGlvbnMoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgb3Blbk5ld0RyaXZlcldpbmRvdygpIHsNCiAgICAgIGNvbnN0IG5ld1dpbmRvd1VybCA9ICdodHRwczovL3lkeHQuY2l0aWNzdGVlbC5jb206ODA5OS90cnVja01hbmFnZS94Y3RnRHJpdmVyVXNlcic7IC8vIOabv+aNouS4uuWunumZheimgei3s+i9rOeahOmhtemdoiBVUkwNCiAgICAgIHdpbmRvdy5vcGVuKG5ld1dpbmRvd1VybCwgJ19ibGFuaycpOyAvLyDmiZPlvIDmlrDnqpflj6Plubbot7Povazoh7PmjIflrpogVVJMDQogICAgfSwNCiAgICBvcGVuTmV3Q2FyV2luZG93KCkgew0KICAgICAgY29uc3QgbmV3V2luZG93VXJsID0gJ2h0dHBzOi8veWR4dC5jaXRpY3N0ZWVsLmNvbTo4MDk5L3RydWNrTWFuYWdlL3hjdGdEcml2ZXJDYXInOyAvLyDmm7/mjaLkuLrlrp7pmYXopoHot7PovaznmoTpobXpnaIgVVJMDQogICAgICB3aW5kb3cub3BlbihuZXdXaW5kb3dVcmwsICdfYmxhbmsnKTsgLy8g5omT5byA5paw56qX5Y+j5bm26Lez6L2s6Iez5oyH5a6aIFVSTA0KICAgIH0sDQogICAgLy8gMeWbveS6lO+8jDLlm73lha3vvIwz5paw6IO95rqQ5a2X5YW457+76K+RDQogICAgdmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzRm9ybWF0KHJvdywgY29sdW1uKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHNPcHRpb25zLCByb3cudmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzKTsNCiAgICB9LA0KDQogICAgdGFza1R5cGVGb3JtYXQocm93LCBjb2x1bW4pIHsNCiAgICAgIHJldHVybiB0aGlzLmdldFRhc2tUeXBlVGV4dChyb3cudGFza1R5cGUpOw0KICAgIH0sDQogICAgdGFza1N0YXR1c0Zvcm1hdChyb3csIGNvbHVtbikgew0KICAgICAgcmV0dXJuIHRoaXMuZ2V0U3RhdHVzVGV4dChyb3cudGFza1N0YXR1cyk7DQogICAgfSwNCg0KICAgIC8qKiDmn6Xor6Llj7jmnLrkv6Hmga/liJfooaggKi8NCiAgICBnZXRDYXJMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIC8vIGxpc3RBbGxEcml2ZXIoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgdGhpcy5kcml2ZXJMaXN0ID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIC8vICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAvLyB9KTsNCiAgICAgIGdldFhjdGdEcml2ZXJDYXJMaXN0QnlQYWdlKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2FyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMuZmlsdGVyZWRDYXJPcHRpb25zID0gdGhpcy5jYXJMaXN0Ow0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5pCc57Si6L+H5ruk6YC76L6RDQogICAgZmlsdGVyQ2FyRGF0YShxdWVyeSkgew0KICAgICAgdGhpcy5zZWFyY2hDYXJRdWVyeSA9IHF1ZXJ5Ow0KDQogICAgICBpZiAodGhpcy5zZWFyY2hDYXJRdWVyeSkgew0KICAgICAgICAvLyDosIPnlKjlkI7nq6/mjqXlj6Pov5vooYzmkJzntKINCiAgICAgICAgY29uc3Qgc2VhcmNoUGFyYW1zID0gew0KICAgICAgICAgIGNhck51bWJlcjogcXVlcnkNCiAgICAgICAgfTsNCiAgICAgICAgZ2V0WGN0Z0RyaXZlckNhckxpc3RCeVBhZ2Uoc2VhcmNoUGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmZpbHRlcmVkQ2FyT3B0aW9ucyA9IHJlc3BvbnNlLnJvd3MgfHwgW107DQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfmkJzntKLotKfovablpLHotKU6JywgZXJyb3IpOw0KICAgICAgICAgIHRoaXMuZmlsdGVyZWRDYXJPcHRpb25zID0gW107DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5pCc57Si5p2h5Lu277yM5pi+56S65YmNNTDmnaHmlbDmja4NCiAgICAgICAgdGhpcy5maWx0ZXJlZENhck9wdGlvbnMgPSB0aGlzLmNhckxpc3Quc2xpY2UoMCwgNTApOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy/pgJrov4dkcml2ZXJJZOiOt+WPluWPuOacuuS/oeaBrw0KICAgIGhhbmRsZURyaXZlckNoYW5nZSgpIHsNCiAgICAgIGlmICh0aGlzLmRpc3BhdGNoRm9ybS5kcml2ZXJJZCAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuZHJpdmVyTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLmlkID09IHRoaXMuZGlzcGF0Y2hGb3JtLmRyaXZlcklkKSB7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5uYW1lID0gaXRlbS5uYW1lOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uaWRDYXJkID0gaXRlbS5pZENhcmQ7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5jb21wYW55ID0gaXRlbS5jb21wYW55Ow0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0ucGhvbmUgPSBpdGVtLnBob25lOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0ucGhvdG8gPSBpdGVtLnBob3RvOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uZmFjZUltZ0xpc3QgPSBpdGVtLmZhY2VJbWdMaXN0Ow0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uZHJpdmVyTGljZW5zZUltZ3MgPSBpdGVtLmRyaXZlckxpY2Vuc2VJbWdzOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUxpY2Vuc2VJbWdzID0gaXRlbS52ZWhpY2xlTGljZW5zZUltZ3M7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5zZXggPSBpdGVtLmdlbmRlcjsNCg0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICAvL+mAmui/h2RyaXZlcklk6I635Y+W5Y+45py65L+h5oGvDQogICAgaGFuZGxlQ2FyQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coImhhbmRsZUNhckNoYW5nZSIpDQogICAgICBpZiAodGhpcy5kaXNwYXRjaEZvcm0uY2FyVVVJZCAhPSBudWxsKSB7DQogICAgICAgIHRoaXMuY2FyTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLmlkID09IHRoaXMuZGlzcGF0Y2hGb3JtLmNhclVVSWQpIHsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmNhck51bWJlciA9IGl0ZW0uY2FyTnVtYmVyOw0KDQogICAgICAgICAgICBpZiAoaXRlbS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPT0gMSkgew0KICAgICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPSAi5Zu95LqUIjsNCiAgICAgICAgICAgIH0gZWxzZSBpZiAoaXRlbS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPT0gMikgew0KICAgICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPSAi5Zu95YWtIjsNCiAgICAgICAgICAgIH0gZWxzZSBpZiAoaXRlbS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPT0gMykgew0KICAgICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPSAi5paw6IO95rqQIjsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnZlaGljbGVFbWlzc2lvblN0YW5kYXJkcyA9ICIiOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0ubGljZW5zZVBsYXRlQ29sb3IgPSBpdGVtLmxpY2Vuc2VQbGF0ZUNvbG9yOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uY2FySWQgPSBpdGVtLmNhcklkOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udHJhaWxlck51bWJlciA9IGl0ZW0udHJhaWxlck51bWJlcjsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnRyYWlsZXJJZCA9IGl0ZW0udHJhaWxlcklkOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uYXhpc1R5cGUgPSBpdGVtLmF4aXNUeXBlOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uZHJpdmVyV2VpZ2h0ID0gaXRlbS5kcml2ZXJXZWlnaHQ7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5tYXhXZWlnaHQgPSBpdGVtLm1heFdlaWdodDsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmVuZ2luZU51bWJlciA9IGl0ZW0uZW5naW5lTnVtYmVyOw0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udmluTnVtYmVyID0gaXRlbS52aW5OdW1iZXI7DQogICAgICAgICAgfQ0KDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOafpeivouWPuOacuuS/oeaBr+WIl+ihqCAqLw0KICAgIGdldERyaXZlckxpc3QoKSB7DQogICAgICAvLyBsaXN0QWxsRHJpdmVyKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAvLyAgIHRoaXMuZHJpdmVyTGlzdCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAvLyAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgLy8gfSk7DQogICAgICBnZXRYY3RnRHJpdmVyVXNlckxpc3RCeVBhZ2UoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kcml2ZXJMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgY29uc29sZS5sb2coInRoaXMuZHJpdmVyTGlzdCIsIHRoaXMuZHJpdmVyTGlzdCk7DQogICAgICAgIHRoaXMuZmlsdGVyZWREcml2ZXJPcHRpb25zID0gdGhpcy5kcml2ZXJMaXN0Ow0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDmkJzntKLov4fmu6TpgLvovpENCiAgICBmaWx0ZXJEcml2ZXJEYXRhKHF1ZXJ5KSB7DQogICAgICB0aGlzLnNlYXJjaERyaXZlclF1ZXJ5ID0gcXVlcnk7DQoNCiAgICAgIGlmICh0aGlzLnNlYXJjaERyaXZlclF1ZXJ5KSB7DQogICAgICAgIC8vIOiwg+eUqOWQjuerr+aOpeWPo+i/m+ihjOaQnOe0og0KICAgICAgICBjb25zdCBzZWFyY2hQYXJhbXMgPSB7DQogICAgICAgICAgc2VhcmNoVmFsdWU6IHF1ZXJ5DQogICAgICAgIH07DQogICAgICAgIGdldFhjdGdEcml2ZXJVc2VyTGlzdEJ5UGFnZShzZWFyY2hQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuZmlsdGVyZWREcml2ZXJPcHRpb25zID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aQnOe0oui0p+i9puWPuOacuuWksei0pTonLCBlcnJvcik7DQogICAgICAgICAgdGhpcy5maWx0ZXJlZERyaXZlck9wdGlvbnMgPSBbXTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzmsqHmnInmkJzntKLmnaHku7bvvIzmmL7npLrliY01MOadoeaVsOaNrg0KICAgICAgICB0aGlzLmZpbHRlcmVkRHJpdmVyT3B0aW9ucyA9IHRoaXMuZHJpdmVyTGlzdC5zbGljZSgwLCA1MCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDop6PmnpDlm77niYflkozmlofku7bmlbDmja4NCiAgICBwYXJzZUltYWdlQW5kRmlsZURhdGEoKSB7DQogICAgICAvLyDop6PmnpDlm77niYfmlbDmja4NCiAgICAgIGlmICh0aGlzLnBsYW5JbmZvLmFwcGx5SW1nVXJsKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5pbWFnZUxpc3QgPSBKU09OLnBhcnNlKHRoaXMucGxhbkluZm8uYXBwbHlJbWdVcmwpOw0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q5Zu+54mH5pWw5o2u5aSx6LSlOicsIGUpOw0KICAgICAgICAgIHRoaXMuaW1hZ2VMaXN0ID0gW107DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g6Kej5p6Q5paH5Lu25pWw5o2uDQogICAgICBpZiAodGhpcy5wbGFuSW5mby5hcHBseUZpbGVVcmwpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmZpbGVMaXN0ID0gSlNPTi5wYXJzZSh0aGlzLnBsYW5JbmZvLmFwcGx5RmlsZVVybCk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDmlofku7bmlbDmja7lpLHotKU6JywgZSk7DQogICAgICAgICAgdGhpcy5maWxlTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOS4i+i9veaWh+S7tg0KICAgIGRvd25sb2FkRmlsZSh1cmwsIGZpbGVOYW1lKSB7DQogICAgICBpZiAoIXVybCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7bpk77mjqXml6DmlYgnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDliJvlu7rkuIDkuKph5YWD57Sg55So5LqO5LiL6L29DQogICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOw0KICAgICAgbGluay5ocmVmID0gdXJsOw0KICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lIHx8ICfkuIvovb3mlofku7YnOw0KICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTsNCiAgICAgIGxpbmsuY2xpY2soKTsNCiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluiuoeWIkuexu+Wei+aWh+acrA0KICAgIGdldFBsYW5UeXBlVGV4dCh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAxOiAn5Ye65Y6C5LiN6L+U5ZueJywNCiAgICAgICAgMjogJ+WHuuWOgui/lOWbnicsDQogICAgICAgIDM6ICfot6jljLrosIPmi6gnLA0KICAgICAgICA0OiAn6YCA6LSn55Sz6K+3Jw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfmnKrnn6XnsbvlnosnOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bkuJrliqHnsbvlnovmlofmnKwNCiAgICBnZXRCdXNpbmVzc0NhdGVnb3J5VGV4dChjYXRlZ29yeSkgew0KICAgICAgY29uc3QgY2F0ZWdvcnlNYXAgPSB7DQogICAgICAgIDE6ICfpgJrnlKgnLA0KICAgICAgICAxMTogJ+mAmueUqCcsDQogICAgICAgIDEyOiAn5aeU5aSW5Yqg5belJywNCiAgICAgICAgMjE6ICfmnInorqHliJLph4/orqHph48nLA0KICAgICAgICAyMjogJ+efreacnycsDQogICAgICAgIDIzOiAn6ZKi5p2/77yI5ZyG6ZKi77yJJywNCiAgICAgICAgMzE6ICfpgJrnlKgnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGNhdGVnb3J5TWFwW2NhdGVnb3J5XSB8fCAn5pyq55+l57G75Z6LJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W54mp6LWE57G75Z6L5paH5pysDQogICAgZ2V0TWF0ZXJpYWxUeXBlVGV4dCh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAxOiAn6ZKi5p2QJywNCiAgICAgICAgMjogJ+mSouadvycsDQogICAgICAgIDM6ICflhbbku5YnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgJ+acquefpeexu+Weiyc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluiuoeWIkueKtuaAgeaWh+acrA0KICAgIGdldFBsYW5TdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAxOiAn5b6F5YiG5Y6C5a6h5om5JywNCiAgICAgICAgMjogJ+W+heWIhuWOguWkjeWuoScsDQogICAgICAgIDM6ICflvoXnlJ/kuqfmjIfmjKXkuK3lv4PlrqHmibknLA0KICAgICAgICA0OiAn5a6h5om55a6M5oiQJywNCiAgICAgICAgNTogJ+W3suWHuuWOgicsDQogICAgICAgIDY6ICfpg6jliIbmlLbotKcnLA0KICAgICAgICA3OiAn5bey5a6M5oiQJywNCiAgICAgICAgMTE6ICfpqbPlm54nLA0KICAgICAgICAxMjogJ+W6n+W8gycsDQogICAgICAgIDEzOiAn6L+H5pyfJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAn5pyq55+l54q25oCBJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5pel5b+X6aKc6ImyDQogICAgZ2V0TG9nQ29sb3IobG9nKSB7DQogICAgICBjb25zdCBsb2dUeXBlQ29sb3JNYXAgPSB7DQogICAgICAgIDE6ICcjNDA5RUZGJywgLy8g5Yib5bu6DQogICAgICAgIDI6ICcjNjdDMjNBJywgLy8g5a6h5om5DQogICAgICAgIDM6ICcjRTZBMjNDJywgLy8g5rWB6L2sDQogICAgICAgIDQ6ICcjRjU2QzZDJywgLy8g6amz5ZueDQogICAgICAgIDU6ICcjOTA5Mzk5JyAgLy8g5YW25LuWDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGxvZ1R5cGVDb2xvck1hcFtsb2cubG9nVHlwZV0gfHwgJyM0MDlFRkYnOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bmtL7ovabnirbmgIHmlofmnKwNCiAgICBnZXREaXNwYXRjaFN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgIDA6ICflvoXlh7rlj5EnLA0KICAgICAgICAxOiAn5bey5Ye65Y+RJywNCiAgICAgICAgMjogJ+W3suWIsOi+vicsDQogICAgICAgIDM6ICflt7LlrozmiJAnLA0KICAgICAgICA0OiAn5bey5Y+W5raIJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAn5pyq55+l54q25oCBJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5rS+6L2m54q25oCB57G75Z6L77yI55So5LqO5qCH562+6aKc6Imy77yJDQogICAgZ2V0RGlzcGF0Y2hTdGF0dXNUeXBlKHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAwOiAnaW5mbycsDQogICAgICAgIDE6ICdwcmltYXJ5JywNCiAgICAgICAgMjogJ3N1Y2Nlc3MnLA0KICAgICAgICAzOiAnc3VjY2VzcycsDQogICAgICAgIDQ6ICdkYW5nZXInDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICdpbmZvJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W6K6h5YiS57G75Z6L5qCH562+5qC35byPDQogICAgZ2V0UGxhblR5cGVUYWdUeXBlKHR5cGUpIHsNCiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7DQogICAgICAgIDE6ICdzdWNjZXNzJywgIC8vIOWHuuWOguS4jei/lOWbng0KICAgICAgICAyOiAnd2FybmluZycsICAvLyDlh7rljoLov5Tlm54NCiAgICAgICAgMzogJ2luZm8nLCAgICAgLy8g6Leo5Yy66LCD5ouoDQogICAgICAgIDQ6ICdkYW5nZXInICAgIC8vIOmAgOi0p+eUs+ivtw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICdpbmZvJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W54mp6LWE57G75Z6L5qCH562+5qC35byPDQogICAgZ2V0TWF0ZXJpYWxUeXBlVGFnVHlwZSh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAxOiAncHJpbWFyeScsICAvLyDpkqLmnZANCiAgICAgICAgMjogJ3N1Y2Nlc3MnLCAgLy8g6ZKi5p2/DQogICAgICAgIDM6ICdpbmZvJyAgICAgIC8vIOWFtuS7lg0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICdpbmZvJzsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5Lia5Yqh57G75Z6L5qCH562+5qC35byPDQogICAgZ2V0QnVzaW5lc3NDYXRlZ29yeVRhZ1R5cGUoY2F0ZWdvcnkpIHsNCiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7DQogICAgICAgICcxJzogJ3ByaW1hcnknLCAgIC8vIOmAmueUqA0KICAgICAgICAnMTEnOiAncHJpbWFyeScsICAvLyDpgJrnlKgNCiAgICAgICAgJzEyJzogJ3dhcm5pbmcnLCAgLy8g5aeU5aSW5Yqg5belDQogICAgICAgICcyMSc6ICdzdWNjZXNzJywgIC8vIOacieiuoeWIkumHj+iuoemHjw0KICAgICAgICAnMjInOiAnaW5mbycsICAgICAvLyDnn63mnJ8NCiAgICAgICAgJzIzJzogJ2RhbmdlcicsICAgLy8g6ZKi5p2/77yI5ZyG6ZKi77yJDQogICAgICAgICczMSc6ICdwcmltYXJ5JyAgIC8vIOmAmueUqA0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTWFwW2NhdGVnb3J5XSB8fCAnaW5mbyc7DQogICAgfSwNCg0KICAgIC8vIOaJk+W8gOa0vui9puW8ueahhg0KICAgIG9wZW5EaXNwYXRjaERpYWxvZygpIHsNCiAgICAgIC8vIOWIneWni+WMlueJqei1hOaVsOaNrg0KICAgICAgdGhpcy5hdmFpbGFibGVNYXRlcmlhbHMgPSB0aGlzLnBsYW5JbmZvLm1hdGVyaWFscyB8fCBbXTsNCiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLmF2YWlsYWJsZU1hdGVyaWFscyIsIHRoaXMuYXZhaWxhYmxlTWF0ZXJpYWxzKTsNCg0KICAgICAgLy8g6I635Y+W5bey5rS+6L2m55qE54mp6LWE5YiX6KGo77yM5bm25Zyo5Zue6LCD5Lit5Yid5aeL5YyWIG1hdGVyaWFsU2VsZWN0aW9uTGlzdA0KICAgICAgdGhpcy5nZXRUYXNrTWF0ZXJpYWxMaXN0QW5kSW5pdFNlbGVjdGlvbigpOw0KICAgICAgLy8g5Yik5pat6Z2e6K6h6YeP5LiUdGFza1R5cGXkuLox55qE5oOF5Ya1DQogICAgICBpZiAodGhpcy5wbGFuSW5mby5tZWFzdXJlRmxhZyA9PSAwKSB7DQogICAgICAgIGlmICh0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSA9PSAxKSB7DQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey57uP5pyJdGFza1R5cGXkuLox55qE5Lu75YqhDQogICAgICAgICAgY29uc3QgaGFzVHlwZTFUYXNrID0gdGhpcy50YXNrTGlzdEluZm8uc29tZSh0YXNrID0+IHRhc2sudGFza1R5cGUgPT09IDEpOw0KICAgICAgICAgIGlmIChoYXNUeXBlMVRhc2spIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6Z2e6K6h6YeP5Y+q6IO95rS+6L2m5Ye65Y6C5LiA5qyhJyk7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCJoYXNUeXBlMVRhc2siLCBoYXNUeXBlMVRhc2spDQogICAgICAgIH0NCiAgICAgIH0NCg0KDQogICAgICAvLyDliKTmlq3nlKjmiLfop5LoibLmnYPpmZANCiAgICAgIGNvbnN0IHJvbGVzID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5yb2xlczsNCiAgICAgIGNvbnNvbGUubG9nKCJyb2xlcyIsIHJvbGVzKTsNCiAgICAgIGlmICghcm9sZXMuaW5jbHVkZXMoJ2xlYXZlLnN1cHBsaWVyJykgJiYgIXJvbGVzLmluY2x1ZGVzKCdsZWF2ZS5hcHBsaWNhbnQnKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmgqjmsqHmnInmtL7ovabmnYPpmZAnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zb2xlLmxvZygidGhpcy5wbGFuSW5mby5wbGFuU3RhdHVzIiwgdGhpcy5wbGFuSW5mby5wbGFuU3RhdHVzKTsNCiAgICAgIGlmICghWzQsIDUsIDZdLmluY2x1ZGVzKHRoaXMucGxhbkluZm8ucGxhblN0YXR1cykpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCflvZPliY3nirbmgIHml6Dms5XmtL7ovaYnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQoNCg0KDQogICAgICBjb25zb2xlLmxvZygib3BlbkRpc3BhdGNoRGlhbG9nIiwgdGhpcy50YXNrTGlzdEluZm8ubGVuZ3RoKTsNCiAgICAgIGlmICh0aGlzLnBsYW5JbmZvLmJ1c2luZXNzQ2F0ZWdvcnkgPT0gMjIgJiYgdGhpcy50YXNrTGlzdEluZm8ubGVuZ3RoID49IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfnn63mnJ/orqHliJLlj6rlhYHorrjmtL7kuIDmrKHovaYnKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5wbGFuSW5mby5idXNpbmVzc0NhdGVnb3J5ID09IDIzICYmIHRoaXMudGFza0xpc3RJbmZvLmxlbmd0aCA+PSAxKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6ZKi5p2/77yI5ZyG6ZKi77yJ6K6h5YiS5Y+q5YWB6K645rS+5LiA5qyh6L2mJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5kaXNwYXRjaEZvcm0gPSB7fTsNCg0KICAgICAgLy8g5pu05paw5Lu75Yqh57G75Z6L6YCJ6aG5DQogICAgICB0aGlzLnVwZGF0ZVRhc2tUeXBlT3B0aW9ucygpOw0KDQogICAgICBpZiAodGhpcy5wbGFuSW5mby5wbGFuVHlwZSA9PSAxKSB7DQogICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID0gIjEiDQogICAgICB9IGVsc2UgaWYgKHRoaXMucGxhbkluZm8ucGxhblR5cGUgPT0gMykgew0KICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSA9ICIzIg0KICAgICAgfSBlbHNlIGlmICh0aGlzLnBsYW5JbmZvLnBsYW5UeXBlID09IDQpIHsNCiAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPSAiMSINCiAgICAgIH0gZWxzZSBpZiAodGhpcy5wbGFuSW5mby5wbGFuVHlwZSA9PSAyKSB7DQogICAgICAgIC8vIOWvueS6juWHuuWOgui/lOWbnuS7u+WKoe+8jOagueaNruW9k+WJjeS7u+WKoeaVsOWGs+Wumum7mOiupOS7u+WKoeexu+Weiw0KICAgICAgICBpZiAodGhpcy50YXNrTGlzdEluZm8ubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPSAiMSI7IC8vIOm7mOiupOmAieaLqeWHuuWOgg0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlID0gIjIiOyAvLyDpu5jorqTpgInmi6nov5TljoINCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2codGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUpLA0KICAgICAgICB0aGlzLmRpc3BhdGNoRGlhbG9nVmlzaWJsZSA9IHRydWU7DQoNCg0KICAgIH0sDQoNCiAgICAvLyDmlrDlop7mlrnms5UNCiAgICBnZXRUYXNrTWF0ZXJpYWxMaXN0QW5kSW5pdFNlbGVjdGlvbigpIHsNCiAgICAgIC8vIOa4heepuuW3sueUqOaVsOmHj+aYoOWwhA0KICAgICAgdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmNsZWFyKCk7DQogICAgICAvLyDnu5/orqHmiYDmnInlt7LmtL7ovabnianotYQNCiAgICAgIGNvbnN0IHR5cGUyTGlzdCA9IHRoaXMudGFza0xpc3RJbmZvLmZpbHRlcihpdGVtID0+IGl0ZW0udGFza1R5cGUgPT09IDIpOw0KICAgICAgY29uc29sZS5sb2coInR5cGUyTGlzdCIsIHR5cGUyTGlzdCk7DQogICAgICBpZiAoIXR5cGUyTGlzdCB8fCB0eXBlMkxpc3QubGVuZ3RoID09PSAwKSB7DQogICAgICAgIC8vIOWIneWni+WMliBtYXRlcmlhbFNlbGVjdGlvbkxpc3TvvJrlhajpg6jpgInkuIrkuJTmlbDph4/kuLrliankvZnmlbDph48NCiAgICAgICAgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QgPSAodGhpcy5wbGFuSW5mby5tYXRlcmlhbHMgfHwgW10pLm1hcChtYXQgPT4gew0KICAgICAgICAgIC8vIGNvbnN0IHVzZWROdW0gPSAodGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXQubWF0ZXJpYWxJZCk/LnVzZWROdW0pIHx8IDA7DQogICAgICAgICAgLy8gY29uc3QgcmVtYWluaW5nTnVtID0gTWF0aC5tYXgoKG1hdC5wbGFuTnVtIHx8IDApIC0gdXNlZE51bSwgMCk7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIG1hdGVyaWFsSWQ6IG1hdC5tYXRlcmlhbElkLA0KICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXQubWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgbWF0ZXJpYWxTcGVjOiBtYXQubWF0ZXJpYWxTcGVjLA0KICAgICAgICAgICAgcGxhbk51bTogbWF0LnBsYW5OdW0sDQogICAgICAgICAgICB1c2VkTnVtOiAwLA0KICAgICAgICAgICAgcmVtYWluaW5nTnVtOiBtYXQucGxhbk51bSwNCiAgICAgICAgICAgIGN1cnJlbnROdW06IG1hdC5wbGFuTnVtDQogICAgICAgICAgfTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmxvZygidGhpcy50YXNrTGlzdEluZm8iLCB0aGlzLnRhc2tMaXN0SW5mbyk7DQogICAgICAgIHR5cGUyTGlzdC5mb3JFYWNoKHRhc2sgPT4gew0KICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHsgdGFza05vOiB0YXNrLnRhc2tObyB9Ow0KICAgICAgICAgIGxpc3RUYXNrTWF0ZXJpYWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIGxldCB0YXNrTWF0ZXJpYWxzID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgICAgIHRhc2tNYXRlcmlhbHMuZm9yRWFjaChtYXRlcmlhbCA9PiB7DQogICAgICAgICAgICAgIGlmICghdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmhhcyhtYXRlcmlhbC5tYXRlcmlhbElkKSkgew0KICAgICAgICAgICAgICAgIHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5zZXQobWF0ZXJpYWwubWF0ZXJpYWxJZCwgew0KICAgICAgICAgICAgICAgICAgdGFza01hdGVyaWFsSW5mbzogbWF0ZXJpYWwsDQogICAgICAgICAgICAgICAgICB1c2VkTnVtOiBtYXRlcmlhbC5wbGFuTnVtDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNYXRlcmlhbCA9IHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5nZXQobWF0ZXJpYWwubWF0ZXJpYWxJZCk7DQogICAgICAgICAgICAgICAgZXhpc3RpbmdNYXRlcmlhbC51c2VkTnVtICs9IG1hdGVyaWFsLnBsYW5OdW07DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICAvLyDlsIZ0YXNrTWF0ZXJpYWxMaXN0TWFw6L2s5o2i5Li65pWw57uE6ZuG5ZCIDQogICAgICAgICAgICB0aGlzLnRhc2tNYXRlcmlhbExpc3QgPSBBcnJheS5mcm9tKHRoaXMudGFza01hdGVyaWFsTGlzdE1hcCwgKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgICAgbWF0ZXJpYWxJZDoga2V5LA0KICAgICAgICAgICAgICAuLi52YWx1ZQ0KICAgICAgICAgICAgfSkpOw0KDQogICAgICAgICAgICAvLyDliJ3lp4vljJYgbWF0ZXJpYWxTZWxlY3Rpb25MaXN077ya5YWo6YOo6YCJ5LiK5LiU5pWw6YeP5Li65Ymp5L2Z5pWw6YePDQogICAgICAgICAgICB0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdCA9ICh0aGlzLnBsYW5JbmZvLm1hdGVyaWFscyB8fCBbXSkubWFwKG1hdCA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IHVzZWROdW0gPSAodGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXQubWF0ZXJpYWxJZCk/LnVzZWROdW0pIHx8IDA7DQogICAgICAgICAgICAgIGNvbnN0IHJlbWFpbmluZ051bSA9IE1hdGgubWF4KChtYXQucGxhbk51bSB8fCAwKSAtIHVzZWROdW0sIDApOw0KDQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgbWF0ZXJpYWxJZDogbWF0Lm1hdGVyaWFsSWQsDQogICAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXQubWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgICAgIG1hdGVyaWFsU3BlYzogbWF0Lm1hdGVyaWFsU3BlYywNCiAgICAgICAgICAgICAgICBwbGFuTnVtOiBtYXQucGxhbk51bSwNCiAgICAgICAgICAgICAgICB1c2VkTnVtOiB1c2VkTnVtLA0KICAgICAgICAgICAgICAgIHJlbWFpbmluZ051bTogcmVtYWluaW5nTnVtLA0KICAgICAgICAgICAgICAgIGN1cnJlbnROdW06IHJlbWFpbmluZ051bQ0KICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgfSk7DQoNCiAgICAgICAgICAgIHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0ID0gdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5yZW1haW5pbmdOdW0gPiAwKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgICAgIC8vIOWIpOaWremdnuiuoemHj+S4lHRhc2tUeXBl5Li6MeeahOaDheWGtQ0KICAgICAgaWYgKHRoaXMucGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMCkgew0KICAgICAgICBpZiAodGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPT0gMSkgew0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3sue7j+aciXRhc2tUeXBl5Li6MeeahOS7u+WKoQ0KICAgICAgICAgIGNvbnN0IGhhc1R5cGUxVGFzayA9IHRoaXMudGFza0xpc3RJbmZvLnNvbWUodGFzayA9PiB0YXNrLnRhc2tUeXBlID09PSAxKTsNCiAgICAgICAgICBpZiAoaGFzVHlwZTFUYXNrKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+mdnuiuoemHj+WPquiDvea0vui9puWHuuWOguS4gOasoScpOw0KICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgIH0NCiAgICAgICAgICBjb25zb2xlLmxvZygiaGFzVHlwZTFUYXNrIiwgaGFzVHlwZTFUYXNrKQ0KICAgICAgICB9DQogICAgICB9DQoNCg0KICAgIH0sDQoNCiAgICAvLyDph43nva7mtL7ovabooajljZUNCiAgICByZXNldERpc3BhdGNoRm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuZGlzcGF0Y2hGb3JtICYmIHRoaXMuJHJlZnMuZGlzcGF0Y2hGb3JtLnJlc2V0RmllbGRzKCk7DQogICAgICB0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdCA9IFt7DQogICAgICAgIG1hdGVyaWFsSWQ6IG51bGwsDQogICAgICAgIG1hdGVyaWFsTmFtZTogJycsDQogICAgICAgIG1hdGVyaWFsU3BlYzogJycsDQogICAgICAgIHBsYW5OdW06IDAsDQogICAgICAgIHJlbWFpbmluZ051bTogMCwNCiAgICAgICAgdXNlZE51bTogMCwNCiAgICAgICAgY3VycmVudE51bTogMA0KICAgICAgfV07DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluW3sua0vui9pueahOeJqei1hOWIl+ihqA0KICAgIGdldFRhc2tNYXRlcmlhbExpc3QoKSB7DQogICAgICAvLyDku450YXNrTGlzdEluZm/kuK3ojrflj5blt7LmtL7ovabnmoTnianotYTkv6Hmga8NCiAgICAgIHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5jbGVhcigpOw0KICAgICAgdGhpcy50YXNrTGlzdEluZm8uZm9yRWFjaCh0YXNrID0+IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIHRhc2tObzogdGFzay50YXNrTm8sDQogICAgICAgIH07DQogICAgICAgIGxpc3RUYXNrTWF0ZXJpYWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICBjb25zb2xlLmxvZygibGlzdFRhc2tNYXRlcmlhbCIsIHJlc3BvbnNlLnJvd3MpOw0KICAgICAgICAgIGxldCB0YXNrTWF0ZXJpYWxzID0gW107DQogICAgICAgICAgdGFza01hdGVyaWFscyA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGFza01hdGVyaWFscy5mb3JFYWNoKG1hdGVyaWFsID0+IHsNCiAgICAgICAgICAgIGlmICghdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmhhcyhtYXRlcmlhbC5tYXRlcmlhbElkKSkgew0KICAgICAgICAgICAgICB0aGlzLnRhc2tNYXRlcmlhbExpc3RNYXAuc2V0KG1hdGVyaWFsLm1hdGVyaWFsSWQsIHsNCiAgICAgICAgICAgICAgICB0YXNrTWF0ZXJpYWxJbmZvOiBtYXRlcmlhbCwNCiAgICAgICAgICAgICAgICB1c2VkTnVtOiBtYXRlcmlhbC5wbGFuTnVtDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNYXRlcmlhbCA9IHRoaXMudGFza01hdGVyaWFsTGlzdE1hcC5nZXQobWF0ZXJpYWwubWF0ZXJpYWxJZCk7DQogICAgICAgICAgICAgIGV4aXN0aW5nTWF0ZXJpYWwudXNlZE51bSArPSBtYXRlcmlhbC5wbGFuTnVtOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICAgIC8vIOWwhnRhc2tNYXRlcmlhbExpc3RNYXDovazmjaLkuLrmlbDnu4Tpm4blkIgNCiAgICAgICAgICB0aGlzLnRhc2tNYXRlcmlhbExpc3QgPSBBcnJheS5mcm9tKHRoaXMudGFza01hdGVyaWFsTGlzdE1hcCwgKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG1hdGVyaWFsSWQ6IGtleSwNCiAgICAgICAgICAgIC4uLnZhbHVlDQogICAgICAgICAgfSkpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCJ0YXNrTWF0ZXJpYWxBcnJheSIsIHRoaXMudGFza01hdGVyaWFsTGlzdCk7DQogICAgICAgICAgY29uc29sZS5sb2coInRhc2tNYXRlcmlhbExpc3RNYXAiLCB0aGlzLnRhc2tNYXRlcmlhbExpc3RNYXApOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmt7vliqDnianotYTooYwNCiAgICBhZGRNYXRlcmlhbFJvdygpIHsNCiAgICAgIHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0LnB1c2goew0KICAgICAgICBtYXRlcmlhbElkOiBudWxsLA0KICAgICAgICBtYXRlcmlhbE5hbWU6ICcnLA0KICAgICAgICBtYXRlcmlhbFNwZWM6ICcnLA0KICAgICAgICBwbGFuTnVtOiAwLA0KICAgICAgICByZW1haW5pbmdOdW06IDAsDQogICAgICAgIHVzZWROdW06IDAsDQogICAgICAgIGN1cnJlbnROdW06IDANCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDnp7vpmaTnianotYTooYwNCiAgICByZW1vdmVNYXRlcmlhbChpbmRleCkgew0KICAgICAgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3Quc3BsaWNlKGluZGV4LCAxKTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG54mp6LWE6YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlTWF0ZXJpYWxDaGFuZ2Uocm93LCBpbmRleCkgew0KICAgICAgY29uc29sZS5sb2coImhhbmRsZU1hdGVyaWFsQ2hhbmdlIiwgdGhpcy50YXNrTWF0ZXJpYWxMaXN0KTsNCg0KDQogICAgICBjb25zdCBzZWxlY3RlZE1hdGVyaWFsID0gdGhpcy50YXNrTWF0ZXJpYWxMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLm1hdGVyaWFsSWQgPT09IHJvdy5tYXRlcmlhbElkKTsNCiAgICAgIGlmIChzZWxlY3RlZE1hdGVyaWFsKSB7DQogICAgICAgIHJvdy51c2VkTnVtID0gc2VsZWN0ZWRNYXRlcmlhbC51c2VkTnVtOw0KICAgICAgfQ0KICAgICAgY29uc3Qgc2VsZWN0UGxhbk1hdGVyaWFsID0gdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMuZmluZChpdGVtID0+IGl0ZW0ubWF0ZXJpYWxJZCA9PT0gcm93Lm1hdGVyaWFsSWQpOw0KDQogICAgICBpZiAoc2VsZWN0UGxhbk1hdGVyaWFsKSB7DQogICAgICAgIHJvdy5wbGFuTnVtID0gc2VsZWN0UGxhbk1hdGVyaWFsLnBsYW5OdW07DQogICAgICAgIHJvdy5tYXRlcmlhbE5hbWUgPSBzZWxlY3RQbGFuTWF0ZXJpYWwubWF0ZXJpYWxOYW1lOw0KICAgICAgICByb3cubWF0ZXJpYWxTcGVjID0gc2VsZWN0UGxhbk1hdGVyaWFsLm1hdGVyaWFsU3BlYzsNCiAgICAgIH0NCg0KICAgICAgcm93LnJlbWFpbmluZ051bSA9IHJvdy5wbGFuTnVtIC0gcm93LnVzZWROdW07DQogICAgICByb3cuY3VycmVudE51bSA9IHJvdy5wbGFuTnVtIC0gcm93LnVzZWROdW07DQoNCiAgICAgIGNvbnNvbGUubG9nKCJoYW5kbGVNYXRlcmlhbENoYW5nZSIsIHJvdywgaW5kZXgpOw0KDQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueJqei1hOacgOWkp+WPr+eUqOaVsOmHjw0KICAgIGdldE1heEF2YWlsYWJsZU51bShyb3cpIHsNCiAgICAgIGlmICghcm93Lm1hdGVyaWFsSWQpIHJldHVybiAwOw0KDQogICAgICAvLyDku450YXNrTWF0ZXJpYWxMaXN0TWFw5Lit6I635Y+W5bey55So5pWw6YePDQogICAgICBjb25zdCBtYXRlcmlhbEluZm8gPSB0aGlzLnRhc2tNYXRlcmlhbExpc3RNYXAuZ2V0KHJvdy5tYXRlcmlhbElkKTsNCiAgICAgIGNvbnN0IHVzZWROdW0gPSBtYXRlcmlhbEluZm8gPyBtYXRlcmlhbEluZm8udXNlZE51bSA6IDA7DQoNCiAgICAgIHJldHVybiByb3cucGxhbk51bSAtIHVzZWROdW07DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaVsOmHj+WPmOWMlg0KICAgIGhhbmRsZU51bUNoYW5nZSh2YWx1ZSwgaW5kZXgpIHsNCiAgICAgIGlmICghdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3RbaW5kZXhdKSByZXR1cm47DQoNCiAgICAgIGNvbnN0IHJvdyA9IHRoaXMubWF0ZXJpYWxTZWxlY3Rpb25MaXN0W2luZGV4XTsNCiAgICAgIGlmICghcm93Lm1hdGVyaWFsSWQpIHJldHVybjsNCg0KICAgICAgLy8g5LuOdGFza01hdGVyaWFsTGlzdE1hcOS4reiOt+WPluW3sueUqOaVsOmHjw0KICAgICAgY29uc3QgbWF0ZXJpYWxJbmZvID0gdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChyb3cubWF0ZXJpYWxJZCk7DQogICAgICBjb25zdCB1c2VkTnVtID0gbWF0ZXJpYWxJbmZvID8gbWF0ZXJpYWxJbmZvLnVzZWROdW0gOiAwOw0KDQogICAgICAvLyDorqHnrpfmnIDlpKflj6/nlKjmlbDph48NCiAgICAgIGNvbnN0IG1heEF2YWlsYWJsZSA9IHJvdy5wbGFuTnVtIC0gdXNlZE51bTsNCg0KICAgICAgLy8g5aaC5p6c5b2T5YmN5YC85aSn5LqO5pyA5aSn5Y+v55So5pWw6YeP77yM5YiZ5bCG5YC8572u5Li65pyA5aSn5Y+v55So5pWw6YePDQogICAgICBpZiAodmFsdWUgPiBtYXhBdmFpbGFibGUpIHsNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHJvdy5jdXJyZW50TnVtID0gbWF4QXZhaWxhYmxlOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yik5pat54mp6LWE5piv5ZCm5Y+v6YCJDQogICAgaXNNYXRlcmlhbEF2YWlsYWJsZShtYXRlcmlhbCkgew0KICAgICAgLy8g5LuOdGFza01hdGVyaWFsTGlzdE1hcOS4reiOt+WPluW3sueUqOaVsOmHjw0KICAgICAgLy8gY29uc3QgbWF0ZXJpYWxJbmZvID0gdGhpcy50YXNrTWF0ZXJpYWxMaXN0TWFwLmdldChtYXRlcmlhbC5pZCk7DQogICAgICAvLyBjb25zdCB1c2VkTnVtID0gbWF0ZXJpYWxJbmZvID8gbWF0ZXJpYWxJbmZvLnVzZWROdW0gOiAwOw0KDQogICAgICAvLyBsZXQgc2VsZWN0ZWQgPSBmYWxzZTsNCg0KICAgICAgLy8gdGhpcy5hdmFpbGFibGVNYXRlcmlhbHMuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgIC8vICAgaWYgKGl0ZW0ubWF0ZXJpYWxJZCA9PT0gbWF0ZXJpYWwubWF0ZXJpYWxJZCkgew0KICAgICAgLy8gICAgIHNlbGVjdGVkID0gdHJ1ZTsNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gfSk7DQoNCiAgICAgIHJldHVybiB0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdC5zb21lKHJvdyA9PiByb3cubWF0ZXJpYWxJZCA9PT0gbWF0ZXJpYWwubWF0ZXJpYWxJZCk7Ow0KICAgIH0sDQoNCiAgICAvLyDkv67mlLnmj5DkuqTmtL7ovabooajljZXmlrnms5UNCiAgICBzdWJtaXREaXNwYXRjaEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzLmRpc3BhdGNoRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIC8vIOWIpOaWremdnuiuoemHj+S4lHRhc2tUeXBl5Li6MeeahOaDheWGtQ0KICAgICAgICAgIGlmICh0aGlzLnBsYW5JbmZvLm1lYXN1cmVGbGFnID09IDApIHsNCiAgICAgICAgICAgIGlmICh0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSA9PSAxKSB7DQogICAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3sue7j+aciXRhc2tUeXBl5Li6MeeahOS7u+WKoQ0KICAgICAgICAgICAgICBjb25zdCBoYXNUeXBlMVRhc2sgPSB0aGlzLnRhc2tMaXN0SW5mby5zb21lKHRhc2sgPT4gdGFzay50YXNrVHlwZSA9PT0gMSk7DQogICAgICAgICAgICAgIGlmIChoYXNUeXBlMVRhc2spIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+mdnuiuoemHj+WPquiDvea0vui9puWHuuWOguS4gOasoScpOw0KICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOaWsOmbhuWQiA0KICAgICAgICAgIGxldCByZXN1bHRMaXN0ID0gW107DQoNCiAgICAgICAgICBjb25zb2xlLmxvZygidGhpcy5wbGFuSW5mby5tZWFzdXJlRmxhZyIsIHRoaXMucGxhbkluZm8ubWVhc3VyZUZsYWcpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSIsIHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tUeXBlKTsNCg0KICAgICAgICAgIGlmICh0aGlzLnBsYW5JbmZvLm1lYXN1cmVGbGFnID09IDAgJiYgdGhpcy5kaXNwYXRjaEZvcm0udGFza1R5cGUgPT0gMikgew0KICAgICAgICAgICAgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QuZm9yRWFjaChzZWxSb3cgPT4gew0KICAgICAgICAgICAgICAvLyDlnKggcGxhbkluZm8ubWF0ZXJpYWxzIOS4reafpeaJvuebuOWQjCBtYXRlcmlhbElkIOeahOWFg+e0oA0KICAgICAgICAgICAgICBjb25zdCBwbGFuTWF0ZXJpYWwgPSAodGhpcy5wbGFuSW5mby5tYXRlcmlhbHMgfHwgW10pLmZpbmQoDQogICAgICAgICAgICAgICAgbWF0ID0+IG1hdC5tYXRlcmlhbElkID09PSBzZWxSb3cubWF0ZXJpYWxJZA0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICBpZiAocGxhbk1hdGVyaWFsKSB7DQogICAgICAgICAgICAgICAgLy8g5rex5ou36LSd5LiA5Lu977yM6YG/5YWN5b2x5ZON5Y6f5pWw5o2uDQogICAgICAgICAgICAgICAgY29uc3QgbmV3SXRlbSA9IHsgLi4ucGxhbk1hdGVyaWFsIH07DQogICAgICAgICAgICAgICAgbmV3SXRlbS5wbGFuTnVtID0gc2VsUm93LmN1cnJlbnROdW07IC8vIOiuvue9ruS4uuacrOasoeaVsOmHjw0KICAgICAgICAgICAgICAgIHJlc3VsdExpc3QucHVzaChuZXdJdGVtKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQoNCiAgICAgICAgICAgIC8vIHJlc3VsdExpc3Qg5Y2z5Li65L2g6ZyA6KaB55qE5paw6ZuG5ZCIDQogICAgICAgICAgICBjb25zb2xlLmxvZygndGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QnLCB0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdCk7DQogICAgICAgICAgICBjb25zb2xlLmxvZygncmVzdWx0TGlzdCcsIHJlc3VsdExpc3QpOw0KDQogICAgICAgICAgICAvLyDnianotYTmoKHpqozvvJrlv4XpobvmnInnianotYQNCiAgICAgICAgICAgIGlmICghdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QubGVuZ3RoKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36Iez5bCR6YCJ5oup5LiA56eN54mp6LWEJyk7DQogICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLy8g5qCh6aqM5q+P5LiA6KGM54mp6LWEDQogICAgICAgICAgICBjb25zdCBoYXNJbnZhbGlkTWF0ZXJpYWwgPSB0aGlzLm1hdGVyaWFsU2VsZWN0aW9uTGlzdC5zb21lKHJvdyA9PiB7DQogICAgICAgICAgICAgIC8vIOW/hemhu+mAieaLqeeJqei1hO+8jOaVsOmHjz4w77yM5LiU5pWw6YePPD3liankvZnmlbDph48NCiAgICAgICAgICAgICAgcmV0dXJuICgNCiAgICAgICAgICAgICAgICAhcm93Lm1hdGVyaWFsSWQgfHwNCiAgICAgICAgICAgICAgICByb3cuY3VycmVudE51bSA8PSAwIHx8DQogICAgICAgICAgICAgICAgcm93LmN1cnJlbnROdW0gPiByb3cucmVtYWluaW5nTnVtDQogICAgICAgICAgICAgICk7DQogICAgICAgICAgICB9KTsNCg0KICAgICAgICAgICAgaWYgKGhhc0ludmFsaWRNYXRlcmlhbCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeeJqei1hOS4lOacrOasoeaVsOmHj+mcgOWkp+S6jjDkuJTkuI3otoXov4fliankvZnmlbDph48nKTsNCiAgICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZygidGhpcy5wbGFuSW5mby5tYXRlcmlhbHMiLCB0aGlzLnBsYW5JbmZvLm1hdGVyaWFscyk7DQogICAgICAgICAgICByZXN1bHRMaXN0ID0gdGhpcy5wbGFuSW5mby5tYXRlcmlhbHMgPyB0aGlzLnBsYW5JbmZvLm1hdGVyaWFscy5tYXAoaXRlbSA9PiAoeyAuLi5pdGVtIH0pKSA6IFtdOw0KICAgICAgICAgICAgY29uc29sZS5sb2coIjEyMzMyMSIsIHJlc3VsdExpc3QpOw0KICAgICAgICAgIH0NCg0KDQoNCg0KDQogICAgICAgICAgaWYgKHRoaXMucGxhbkluZm8ubWVhc3VyZUZsYWcgPT0gMSAmJiB0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSAhPT0gMikgew0KICAgICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0udGFza1N0YXR1cyA9IDE7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnRhc2tTdGF0dXMgPSA0Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGlmICh0aGlzLmRpc3BhdGNoRm9ybS50YXNrVHlwZSA9PSAyKSB7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS50YXNrU3RhdHVzID0gNTsNCiAgICAgICAgICB9DQoNCg0KICAgICAgICAgIC8v5piv5ZCm55u05L6b6buY6K6k5Li6MA0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmlzRGlyZWN0U3VwcGx5ID0gMDsNCiAgICAgICAgICAvLyB0b2RvIOS7u+WKoeeKtuaAgeehruiupA0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0ucGxhbk5vID0gdGhpcy5wbGFuSW5mby5wbGFuTm87DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uY2FyTnVtID0gdGhpcy5kaXNwYXRjaEZvcm0uY2FyTnVtYmVyOw0KICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLmNvbXBhbnlOYW1lID0gdGhpcy5kaXNwYXRjaEZvcm0uY29tcGFueTsNCiAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5kcml2ZXJMaWNlbnNlSW1nID0gdGhpcy5kaXNwYXRjaEZvcm0uZHJpdmVyTGljZW5zZUltZ3M7DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uZHJpdmVyTmFtZSA9IHRoaXMuZGlzcGF0Y2hGb3JtLm5hbWU7DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0ubW9iaWxlUGhvbmUgPSB0aGlzLmRpc3BhdGNoRm9ybS5waG9uZTsNCiAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5mYWNlSW1nID0gdGhpcy5kaXNwYXRjaEZvcm0ucGhvdG87DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uZHJpdmluZ0xpY2Vuc2VJbWcgPSB0aGlzLmRpc3BhdGNoRm9ybS52ZWhpY2xlTGljZW5zZUltZ3M7DQogICAgICAgICAgdGhpcy5kaXNwYXRjaEZvcm0uaWRDYXJkTm8gPSB0aGlzLmRpc3BhdGNoRm9ybS5pZENhcmQ7DQogICAgICAgICAgaWYgKHRoaXMuZGlzcGF0Y2hGb3JtLnNleCA9PSAiMSIpIHsNCiAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hGb3JtLnNleCA9IDE7DQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmRpc3BhdGNoRm9ybS5zZXggPT0gIjIiKSB7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS5zZXggPSAyOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAodGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09ICLlm73kupQiKSB7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPSAxOw0KICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09ICLlm73lha0iKSB7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPSAyOw0KICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5kaXNwYXRjaEZvcm0udmVoaWNsZUVtaXNzaW9uU3RhbmRhcmRzID09ICLmlrDog73mupAiKSB7DQogICAgICAgICAgICB0aGlzLmRpc3BhdGNoRm9ybS52ZWhpY2xlRW1pc3Npb25TdGFuZGFyZHMgPSAzOw0KICAgICAgICAgIH0NCiAgICAgICAgICBjb25zb2xlLmxvZygidGhpcy5kaXNwYXRjaEZvcm0iLCB0aGlzLmRpc3BhdGNoRm9ybSk7DQoNCiAgICAgICAgICBsZXQgZGlzcGF0Y2hJbmZvID0ge307DQogICAgICAgICAgZGlzcGF0Y2hJbmZvLmNhck51bSA9IHRoaXMuZGlzcGF0Y2hGb3JtLmNhck51bTsNCg0KICAgICAgICAgIGlzQWxsb3dEaXNwYXRjaChkaXNwYXRjaEluZm8pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgbGV0IHJvdyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgICBpZiAocm93ID4gMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlvZPliY3ovabmnInmraPlnKjmiafooYznmoTku7vliqEiKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgbGV0IHBhcmFtID0ge307DQogICAgICAgICAgICAgIHBhcmFtLmxlYXZlVGFzayA9IHRoaXMuZGlzcGF0Y2hGb3JtOw0KICAgICAgICAgICAgICBwYXJhbS5sZWF2ZVRhc2tNYXRlcmlhbExpc3QgPSByZXN1bHRMaXN0Ow0KICAgICAgICAgICAgICBhZGRUYXNrQW5kTWF0ZXJpYWxBbmRBZGRMZWF2ZUxvZyhwYXJhbSkudGhlbihyZXMgPT4gew0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJhZGRUYXNrQW5kTWF0ZXJpYWxBbmRBZGRMZWF2ZUxvZyIsIHJlcykNCiAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+a0vui9puaIkOWKnycpOw0KICAgICAgICAgICAgICAgICAgdGhpcy5kaXNwYXRjaERpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdFRhc2tJbmZvKCk7DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIC8vIOWFtuS7luWksei0peWOn+WboA0KICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubWVzc2FnZSB8fCAn5rS+6L2m5aSx6LSlJyk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KS5jYXRjaChlcnIgPT4gew0KICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2Rpc3BhdGNoIGVycm9yOicsIGVycik7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc5byC5bi477yM56iN5ZCO6YeN6K+VJyk7DQogICAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICAgIC8vIGFkZFRhc2tBbmRNYXRlcmlhbCh0aGlzLmRpc3BhdGNoRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIC8vICAgY29uc29sZS5sb2coImFkZFRhc2tBbmRNYXRlcmlhbCIsIHJlc3BvbnNlKTsNCiAgICAgICAgICAgICAgLy8gICBsZXQgc25vd0lkID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICAgICAgLy8gICB0aGlzLnBsYW5JbmZvLm1hdGVyaWFscy5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAvLyAgICAgaXRlbS50YXNrTm8gPSBzbm93SWQ7DQogICAgICAgICAgICAgIC8vICAgICBhZGRUYXNrTWF0ZXJpYWwoaXRlbSk7DQogICAgICAgICAgICAgIC8vICAgfSk7DQoNCiAgICAgICAgICAgICAgLy8gICBjb25zb2xlLmxvZygi55Sf5oiQ5rS+6L2m5pel5b+XIik7DQoNCiAgICAgICAgICAgICAgLy8gICAvL+eUn+aIkOa0vui9puaXpeW/lw0KICAgICAgICAgICAgICAvLyAgIGxldCBsZWF2ZVRhc2tMb2cgPSB7fTsNCg0KDQogICAgICAgICAgICAgIC8vICAgbGVhdmVUYXNrTG9nLmxvZ1R5cGUgPSAyOw0KICAgICAgICAgICAgICAvLyAgIGxlYXZlVGFza0xvZy50YXNrTm8gPSBzbm93SWQ7DQogICAgICAgICAgICAgIC8vICAgbGVhdmVUYXNrTG9nLmFwcGx5Tm8gPSB0aGlzLmFwcGx5Tm87DQogICAgICAgICAgICAgIC8vICAgbGVhdmVUYXNrTG9nLmluZm8gPSAn5rS+6L2m5Lu75Yqh5Yib5bu677yaJyArIHRoaXMuZGlzcGF0Y2hGb3JtLmNhck51bSArICcgJyArIHRoaXMuZGlzcGF0Y2hGb3JtLmRyaXZlck5hbWUNCiAgICAgICAgICAgICAgLy8gICBhZGRMZWF2ZUxvZyhsZWF2ZVRhc2tMb2cpOw0KDQogICAgICAgICAgICAgIC8vICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmtL7ovabmiJDlip8nKTsNCiAgICAgICAgICAgICAgLy8gICB0aGlzLmRpc3BhdGNoRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgICAvLyAgIHRoaXMuZ2V0TGlzdFRhc2tJbmZvKCk7DQogICAgICAgICAgICAgIC8vIH0pOw0KDQogICAgICAgICAgICAgIHRoaXMuZGlzcGF0Y2hEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICB9DQogICAgICAgICAgICBjb25zb2xlLmxvZygidGhpcy5pc0FsbG93RGlzcGF0Y2giLCByZXNwb25zZSk7DQogICAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ2Rpc3BhdGNoIGVycm9yOicsIGVycik7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvZHnu5zlvILluLjvvIznqI3lkI7ph43or5UnKTsNCiAgICAgICAgICB9KTsNCg0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMluaXpeacn+aXtumXtA0KICAgIGZvcm1hdERhdGVUaW1lKGRhdGUpIHsNCiAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICBjb25zdCBtb250aCA9IChkYXRlLmdldE1vbnRoKCkgKyAxKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBkYXkgPSBkYXRlLmdldERhdGUoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBob3VycyA9IGRhdGUuZ2V0SG91cnMoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBtaW51dGVzID0gZGF0ZS5nZXRNaW51dGVzKCkudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3Qgc2Vjb25kcyA9IGRhdGUuZ2V0U2Vjb25kcygpLnRvU3RyaW5nKCkucGFkU3RhcnQoMiwgJzAnKTsNCg0KICAgICAgcmV0dXJuIGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fSAke2hvdXJzfToke21pbnV0ZXN9OiR7c2Vjb25kc31gOw0KICAgIH0sDQoNCiAgICAvLyDmiZPljbDlip/og70NCiAgICBoYW5kbGVQcmludCgpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5omT5Y2w5Yqf6IO95bCa5pyq5a6e546wJyk7DQogICAgICAvLyDlrp7pmYXpobnnm67kuK3lj6/ku6XosIPnlKjmtY/op4jlmajmiZPljbDlip/og70NCiAgICAgIC8vIHdpbmRvdy5wcmludCgpOw0KICAgIH0sDQoNCiAgICAvLyDov5Tlm57mjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLiR0YWIuY2xvc2VPcGVuUGFnZSh0aGlzLiRyb3V0ZSk7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICIvbGVhdmUvbGVhdmVQbGFuTGlzdCIsIHF1ZXJ5OiB7IHQ6IERhdGUubm93KCkgfSB9KTsNCiAgICB9LA0KDQogICAgLy8g6Lez6L2s5Yiw5Lu75Yqh6K+m5oOF6aG16Z2iDQogICAgZ29Ub1Rhc2tEZXRhaWwocm93KSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIHBhdGg6IGAvbGVhdmUvcGxhbi90YXNrLyR7cm93LnRhc2tOb31gDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgZ2V0VGFza1R5cGVUZXh0KHRhc2tUeXBlKSB7DQogICAgICBjb25zdCBzdGFuZGFyZE1hcCA9IHsNCiAgICAgICAgMTogJ+WHuuWOgicsDQogICAgICAgIDI6ICfov5TljoInLA0KICAgICAgICAzOiAn6Leo5Yy66LCD5ouoJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGFuZGFyZE1hcFt0YXNrVHlwZV0gfHwgJ+acquefpSc7DQogICAgfSwNCg0KICAgIGdldFN0YXR1c1RleHQoc3RhbmRhcmQpIHsNCiAgICAgIGNvbnN0IHN0YW5kYXJkTWFwID0gew0KICAgICAgICAxOiAn5b6F6L+H55qu6YeNJywNCiAgICAgICAgMjogJ+W+heijhei0pycsDQogICAgICAgIDM6ICflvoXov4fmr5vph40nLA0KICAgICAgICA0OiAn5b6F5Ye65Y6CJywNCiAgICAgICAgNTogJ+W+hei/lOWOgicsDQogICAgICAgIDY6ICflvoXov4fmr5vph40o5aSN56OFKScsDQogICAgICAgIDc6ICflvoXljbjotKcnLA0KICAgICAgICA4OiAn5b6F6L+H55qu6YeNKOWkjeejhSknLA0KICAgICAgICA5OiAn5a6M5oiQJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGFuZGFyZE1hcFtzdGFuZGFyZF0gfHwgJ+acquefpSc7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluiuoeWIkueKtuaAgeexu+Weiw0KICAgIGdldFBsYW5TdGF0dXNUeXBlKHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAnMSc6ICd3YXJuaW5nJywgIC8vIOW+heWIhuWOguWuoeaJuQ0KICAgICAgICAnMic6ICd3YXJuaW5nJywgIC8vIOW+heWIhuWOguWkjeWuoQ0KICAgICAgICAnMyc6ICd3YXJuaW5nJywgIC8vIOW+heeUn+S6p+aMh+aMpeS4reW/g+WuoeaJuQ0KICAgICAgICAnNCc6ICdzdWNjZXNzJywgIC8vIOWuoeaJueWujOaIkA0KICAgICAgICAnNSc6ICdwcmltYXJ5JywgIC8vIOW3suWHuuWOgg0KICAgICAgICAnNic6ICdpbmZvJywgICAgIC8vIOmDqOWIhuaUtui0pw0KICAgICAgICAnNyc6ICdzdWNjZXNzJywgIC8vIOW3suWujOaIkA0KICAgICAgICAnMTEnOiAnZGFuZ2VyJywgIC8vIOmps+Wbng0KICAgICAgICAnMTInOiAnZGFuZ2VyJywgIC8vIOW6n+W8gw0KICAgICAgICAnMTMnOiAnZGFuZ2VyJyAgIC8vIOi/h+acnw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICdpbmZvJw0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDojrflj5borqHliJLkuIvmiYDmnInku7vliqHnmoTku7vliqHnianotYQNCiAgICAgKiBAcmV0dXJucyB7UHJvbWlzZTx2b2lkPn0NCiAgICAgKi8NCiAgICBhc3luYyBnZXRBbGxUYXNrTWF0ZXJpYWxzKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5riF56m6546w5pyJ5pWw5o2uDQogICAgICAgIHRoaXMudGFza01hdGVyaWFsTWFwLmNsZWFyKCk7DQoNCiAgICAgICAgLy8g6I635Y+W6K+l6K6h5YiS5LiL5omA5pyJ5Lu75Yqh55qE5Lu75Yqh54mp6LWEDQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBhcHBseU5vOiB0aGlzLmFwcGx5Tm8NCiAgICAgICAgfTsNCg0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGxpc3RUYXNrTWF0ZXJpYWwocGFyYW1zKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5yb3dzKSB7DQogICAgICAgICAgLy8g5bCG5Lu75Yqh54mp6LWE5oyJ54mp6LWESUTliIbnu4TlrZjlgqgNCiAgICAgICAgICByZXNwb25zZS5yb3dzLmZvckVhY2gobWF0ZXJpYWwgPT4gew0KICAgICAgICAgICAgY29uc3Qga2V5ID0gbWF0ZXJpYWwubWF0ZXJpYWxJZDsNCiAgICAgICAgICAgIGlmICghdGhpcy50YXNrTWF0ZXJpYWxNYXAuaGFzKGtleSkpIHsNCiAgICAgICAgICAgICAgdGhpcy50YXNrTWF0ZXJpYWxNYXAuc2V0KGtleSwgew0KICAgICAgICAgICAgICAgIG1hdGVyaWFsSWQ6IG1hdGVyaWFsLm1hdGVyaWFsSWQsDQogICAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXRlcmlhbC5tYXRlcmlhbE5hbWUsDQogICAgICAgICAgICAgICAgbWF0ZXJpYWxTcGVjOiBtYXRlcmlhbC5tYXRlcmlhbFNwZWMsDQogICAgICAgICAgICAgICAgcGxhbk51bTogbWF0ZXJpYWwucGxhbk51bSwNCiAgICAgICAgICAgICAgICB1c2VkTnVtOiAwLA0KICAgICAgICAgICAgICAgIHRhc2tNYXRlcmlhbHM6IFtdIC8vIOWtmOWCqOavj+S4quS7u+WKoeeahOWFt+S9k+eJqei1hOS/oeaBrw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgY29uc3QgbWF0ZXJpYWxJbmZvID0gdGhpcy50YXNrTWF0ZXJpYWxNYXAuZ2V0KGtleSk7DQogICAgICAgICAgICAvLyDntK/liqDmr4/kuKrku7vliqHnianotYTnmoTorqHliJLmlbDph4/kvZzkuLrlt7Lkvb/nlKjmlbDph48NCiAgICAgICAgICAgIG1hdGVyaWFsSW5mby51c2VkTnVtICs9IG1hdGVyaWFsLnBsYW5OdW07DQogICAgICAgICAgICBtYXRlcmlhbEluZm8udGFza01hdGVyaWFscy5wdXNoKHsNCiAgICAgICAgICAgICAgdGFza05vOiBtYXRlcmlhbC50YXNrTm8sDQogICAgICAgICAgICAgIGNhck51bTogbWF0ZXJpYWwuY2FyTnVtLA0KICAgICAgICAgICAgICBwbGFuTnVtOiBtYXRlcmlhbC5wbGFuTnVtLA0KICAgICAgICAgICAgICBjcmVhdGVUaW1lOiBtYXRlcmlhbC5jcmVhdGVUaW1lDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOabtOaWsOeJqei1hOmAieaLqeWIl+ihqOS4reeahOW3suS9v+eUqOaVsOmHjw0KICAgICAgICB0aGlzLnVwZGF0ZU1hdGVyaWFsVXNlZE51bSgpOw0KDQogICAgICAgIGNvbnNvbGUubG9nKCdUYXNrIE1hdGVyaWFsIE1hcDonLCB0aGlzLnRhc2tNYXRlcmlhbE1hcCk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bku7vliqHnianotYTlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bku7vliqHnianotYTlpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5pu05paw54mp6LWE6YCJ5oup5YiX6KGo5Lit55qE5bey5L2/55So5pWw6YePDQogICAgICovDQogICAgdXBkYXRlTWF0ZXJpYWxVc2VkTnVtKCkgew0KICAgICAgdGhpcy5tYXRlcmlhbFNlbGVjdGlvbkxpc3QuZm9yRWFjaChyb3cgPT4gew0KICAgICAgICBpZiAocm93Lm1hdGVyaWFsSWQpIHsNCiAgICAgICAgICBjb25zdCBtYXRlcmlhbEluZm8gPSB0aGlzLnRhc2tNYXRlcmlhbE1hcC5nZXQocm93Lm1hdGVyaWFsSWQpOw0KICAgICAgICAgIGlmIChtYXRlcmlhbEluZm8pIHsNCiAgICAgICAgICAgIC8vIOebtOaOpeS9v+eUqOe0r+WKoOeahOiuoeWIkuaVsOmHj+S9nOS4uuW3suS9v+eUqOaVsOmHjw0KICAgICAgICAgICAgcm93LnVzZWROdW0gPSBtYXRlcmlhbEluZm8udXNlZE51bTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDnianotYTnoa7orqTmjInpkq7ngrnlh7vkuovku7YNCiAgICBhc3luYyBoYW5kbGVNYXRlcmlhbENvbmZpcm0oKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDmoKHpqozmiYDmnInku7vliqHnmoR0YXNrU3RhdHVz5piv5ZCm5Li6OQ0KICAgICAgICBpZiAodGhpcy50YXNrTGlzdEluZm8gJiYgdGhpcy50YXNrTGlzdEluZm8ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnN0IHVuZmluaXNoZWRUYXNrcyA9IHRoaXMudGFza0xpc3RJbmZvLmZpbHRlcih0YXNrID0+IHRhc2sudGFza1N0YXR1cyAhPT0gOSk7DQogICAgICAgICAgaWYgKHVuZmluaXNoZWRUYXNrcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflrZjlnKjmnKrlrozmiJDnmoTku7vliqHvvIzml6Dms5Xov5vooYznianotYTnoa7orqQnKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAvLyDosIPnlKjlkI7nq6/mjqXlj6PvvIzkvKDpgJJhcHBseU5vDQogICAgICAgIGF3YWl0IGNvbmZpcm1NYXRlcmlhbCh7IGFwcGx5Tm86IHRoaXMuYXBwbHlObyB9KTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfnianotYTnoa7orqTmiJDlip8nKTsNCiAgICAgICAgLy8g5Yi35paw6K+m5oOFDQogICAgICAgIHRoaXMuZ2V0TGlzdFRhc2tJbmZvKCk7DQogICAgICAgIC8vIOmHjeaWsOiOt+WPlnBsYW5JbmZvDQogICAgICAgIGRldGFpbFBsYW4odGhpcy5hcHBseU5vKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLnBsYW5JbmZvID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgfSk7DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+eJqei1hOehruiupOWksei0pScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4cA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;AAKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "detail.vue", "sourceRoot": "src/views/leave/plan", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"card-header\">\r\n        <h3>申请详情</h3>\r\n      </div>\r\n\r\n      <!-- 基本信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">基本信息</div>\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"申请编号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 申请编号</template>\r\n            {{ planInfo.applyNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划号\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划号</template>\r\n            {{ planInfo.planNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划状态\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-flag\"></i> 计划状态</template>\r\n            <el-tag :type=\"getPlanStatusType(planInfo.planStatus)\">{{ getPlanStatusText(planInfo.planStatus) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"计划类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-order\"></i> 计划类型</template>\r\n            <el-tag :type=\"getPlanTypeTagType(planInfo.planType)\">{{ getPlanTypeText(planInfo.planType) }}</el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"业务类型\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-management\"></i> 业务类型</template>\r\n            <el-tag :type=\"getBusinessCategoryTagType(planInfo.businessCategory)\">{{\r\n              getBusinessCategoryText(planInfo.businessCategory) }}</el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"是否计量\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-operation\"></i> 是否计量</template>\r\n            <el-tag :type=\"planInfo.measureFlag === 1 ? 'success' : 'danger'\">\r\n              {{ planInfo.measureFlag === 1 ? '计量' : '不计量' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.plannedAmount\">\r\n            <template slot=\"label\"><i class=\"el-icon-document\"></i> 计划量（吨）</template>\r\n            {{ planInfo.plannedAmount }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"是否复审\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-check\"></i> 是否复审</template>\r\n            <el-tag :type=\"planInfo.secApproveFlag === 1 ? 'warning' : 'info'\">\r\n              {{ planInfo.secApproveFlag === 1 ? '是' : '否' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请单位\" v-if=\"planInfo.sourceCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-office-building\"></i> 申请单位</template>\r\n            {{ planInfo.sourceCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"收货单位\" v-if=\"planInfo.receiveCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-school\"></i> 收货单位</template>\r\n            {{ planInfo.receiveCompany }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"返回单位\" v-if=\"planInfo.targetCompany\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-home\"></i> 返回单位</template>\r\n            {{ planInfo.targetCompany }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划返回时间\" v-if=\"planInfo.planReturnTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-time\"></i> 计划返回时间</template>\r\n            {{ planInfo.planReturnTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"退货单位\" v-if=\"planInfo.refundDepartment\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-shop\"></i> 退货单位</template>\r\n            {{ planInfo.refundDepartment }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"开始时间\" v-if=\"planInfo.startTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 开始时间</template>\r\n            {{ planInfo.startTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"结束时间\" v-if=\"planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 结束时间</template>\r\n            {{ planInfo.endTime }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"有效期\" v-if=\"!planInfo.endTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-date\"></i> 有效期</template>\r\n            {{ planInfo.expireTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"监装人\" v-if=\"planInfo.monitor\">\r\n            <template slot=\"label\"><i class=\"el-icon-user\"></i> 监装人</template>\r\n            {{ planInfo.monitor }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"物资专管员\" v-if=\"planInfo.specialManager\">\r\n            <template slot=\"label\"><i class=\"el-icon-s-custom\"></i> 物资专管员</template>\r\n            {{ planInfo.specialManager }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"物资类型\" v-if=\"planInfo.itemType\">\r\n            <template slot=\"label\"><i class=\"el-icon-goods\"></i> 物资类型</template>\r\n            <el-tag :type=\"getMaterialTypeTagType(planInfo.itemType)\">\r\n              {{ getMaterialTypeText(planInfo.itemType) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"出厂原因\" v-if=\"planInfo.reason\">\r\n            <template slot=\"label\"><i class=\"el-icon-info\"></i> 出厂原因</template>\r\n            {{ planInfo.reason }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"合同号\" v-if=\"planInfo.contractNo\">\r\n            <template slot=\"label\"><i class=\"el-icon-tickets\"></i> 合同号</template>\r\n            {{ planInfo.contractNo }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"申请时间\" v-if=\"planInfo.applyTime\">\r\n            <template slot=\"label\"><i class=\"el-icon-timer\"></i> 申请时间</template>\r\n            {{ planInfo.applyTime }}\r\n          </el-descriptions-item>\r\n\r\n          <el-descriptions-item label=\"申请人\" v-if=\"planInfo.applyUserName\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 申请人</template>\r\n            {{ planInfo.applyUserName }}\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"计划量\" v-if=\"planInfo.planned_amount\">\r\n            <template slot=\"label\"><i class=\"el-icon-user-solid\"></i> 计划量(吨)</template>\r\n            {{ planInfo.planned_amount }}\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 新增图片列表部分 -->\r\n      <div class=\"section-container\" v-if=\"imageList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-picture-outline\"></i> 申请图片</span>\r\n        </div>\r\n        <div class=\"image-container\">\r\n          <viewer :images=\"imageList\">\r\n            <div class=\"image-list\">\r\n              <div class=\"image-item\" v-for=\"(image, index) in imageList\" :key=\"'img-' + index\">\r\n                <img :src=\"image.url\" :alt=\"image.name\">\r\n                <div class=\"image-name\">{{ image.name }}</div>\r\n              </div>\r\n            </div>\r\n          </viewer>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 新增文件列表部分 -->\r\n      <div class=\"section-container\" v-if=\"fileList.length > 0\">\r\n        <div class=\"section-title\">\r\n          <span><i class=\"el-icon-document\"></i> 申请附件</span>\r\n        </div>\r\n        <div class=\"file-container\">\r\n          <div class=\"file-list\">\r\n            <div class=\"file-item\" v-for=\"(file, index) in fileList\" :key=\"'file-' + index\"\r\n              @click=\"downloadFile(file.url, file.name)\">\r\n              <i class=\"el-icon-document file-icon\"></i>\r\n              <div class=\"file-name\">{{ file.name }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 物资列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">物资列表</div>\r\n        <el-table :data=\"planInfo.materials\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"materialSpec\" label=\"物资型号规格\" width=\"180\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"单位\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"remark\" label=\"备注\" width=\"150\">\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 派车信息部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">\r\n          <span>派车信息</span>\r\n          <el-button type=\"primary\" size=\"small\" icon=\"el-icon-truck\" @click=\"openDispatchDialog\"\r\n            :disabled=\"!canDispatchCar\" class=\"dispatch-btn\">\r\n            派车\r\n          </el-button>\r\n        </div>\r\n\r\n        <el-table v-if=\"taskListInfo.length > 0\" :data=\"taskListInfo\" style=\"width: 100%\" border>\r\n          <el-table-column type=\"index\" width=\"50\" label=\"序号\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"carNum\" label=\"车牌号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"driverName\" label=\"司机姓名\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"mobilePhone\" label=\"司机手机号\" width=\"120\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\" :formatter=\"taskTypeFormat\">\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"doormanReceiveNum\" label=\"门卫确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0 \">\r\n          </el-table-column>\r\n          <el-table-column prop=\"factoryReceiveNum\" label=\"分厂确认数量\" width=\"180\" v-if=\"planInfo.measureFlag == 0\">\r\n          </el-table-column> -->\r\n          <!-- <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"measureUnit\" label=\"计量单位\" width=\"120\" v-if=\"planInfo.measureFlag == 1\">\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.tare || '-' }} {{ scope.row.tare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重\" width=\"100\" v-if=\"planInfo.measureFlag == 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.gross || '-' }} {{ scope.row.gross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"tareWeight\" label=\"皮重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secTare || '-' }} {{ scope.row.secTare ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"grossWeight\" label=\"毛重(复磅)\" width=\"100\"\r\n            v-if=\"planInfo.measureFlag == 1 && planInfo.planType !== 1\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.secGross || '-' }} {{ scope.row.secGross ? 't' : '' }}\r\n            </template>\r\n          </el-table-column>\r\n\r\n\r\n          <el-table-column prop=\"createTime\" label=\"派车时间\" width=\"160\">\r\n          </el-table-column>\r\n          <el-table-column prop=\"taskStatus\" label=\"任务状态\" width=\"120\" :formatter=\"taskStatusFormat\">\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"goToTaskDetail(scope.row)\">\r\n                任务详情\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <div v-else class=\"empty-data\">\r\n          <el-empty description=\"暂无派车记录\"></el-empty>\r\n        </div>\r\n\r\n        <!-- 物资确认按钮 -->\r\n        <div style=\"text-align: right; margin-top: 15px;\">\r\n          <el-button type=\"primary\" icon=\"el-icon-finished\" @click=\"handleMaterialConfirm\">\r\n            物资确认\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n      <!-- v-if=\"canShowMaterialConfirm\" -->\r\n\r\n      <!-- 审核内容部分 -->\r\n      <div class=\"section-container\" v-if=\"planInfo.approveButtonShow\">\r\n        <div class=\"section-title\">审核内容</div>\r\n        <el-form label-width=\"80px\" :model=\"approveForm\" ref=\"approveForm\">\r\n          <el-form-item label=\"审核建议\">\r\n            <el-input type=\"textarea\" v-model=\"approveForm.approveContent\" :rows=\"4\" placeholder=\"请输入审核建议\"\r\n              maxlength=\"200\" show-word-limit></el-input>\r\n            <div style=\"color: #909399; font-size: 12px; margin-top: 5px; margin-bottom: 10px;\">审核建议可不填，默认通过为同意，驳回为拒绝\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 日志列表部分 -->\r\n      <div class=\"section-container\">\r\n        <div class=\"section-title\">日志列表</div>\r\n        <el-timeline>\r\n          <el-timeline-item v-for=\"(log, index) in planInfo.leaveLogs\" :key=\"index\" :timestamp=\"log.createTime\"\r\n            :color=\"getLogColor(log)\">\r\n            {{ log.info }}\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n\r\n      <!-- 固定底部操作栏 -->\r\n      <div class=\"fixed-bottom-action\">\r\n        <el-row :gutter=\"10\" type=\"flex\" justify=\"center\" align=\"middle\">\r\n          <!-- 返回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\">\r\n            <el-button size=\"medium\" @click=\"cancel\">返回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 通过按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.approveButtonShow\">\r\n            <el-button size=\"medium\" type=\"primary\" icon=\"el-icon-check\" @click=\"handleApprove\">通过</el-button>\r\n          </el-col>\r\n\r\n          <!-- 驳回按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.rejectButtonShow\">\r\n            <el-button size=\"medium\" type=\"danger\" icon=\"el-icon-close\" @click=\"handleReject\">驳回</el-button>\r\n          </el-col>\r\n\r\n          <!-- 废弃按钮 -->\r\n          <el-col :span=\"2\" :xs=\"6\" v-if=\"planInfo.discardButtonShow\">\r\n            <el-button size=\"medium\" type=\"success\" icon=\"el-icon-delete\" @click=\"handleDiscard\">废弃</el-button>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n    </el-card>\r\n\r\n    <!-- 派车弹框 -->\r\n    <el-dialog title=\"派车\" :visible.sync=\"dispatchDialogVisible\" width=\"1200px\" append-to-body destroy-on-close\r\n      @closed=\"resetDispatchForm\">\r\n      <el-form ref=\"dispatchForm\" :model=\"dispatchForm\" :rules=\"dispatchRules\" label-width=\"100px\"\r\n        class=\"dispatch-form\">\r\n\r\n        <el-form-item label=\"货车司机\" prop=\"driverId\" :rules=\"[{ required: true, message: '司机信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.driverId\" filterable :filter-method=\"filterDriverData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleDriverChange\">\r\n            <el-option v-for=\"item in filteredDriverOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.driverInfo\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewDriverWindow\">前往新增或修改司机信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"司机名称\" prop=\"name\" v-if=\"dispatchForm.name != null\">\r\n          <el-input v-model=\"dispatchForm.name\" placeholder=\"请输入司机名称\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\" v-if=\"dispatchForm.phone != null\">\r\n          <el-input v-model=\"dispatchForm.phone\" placeholder=\"请输入手机号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idCard\" v-if=\"dispatchForm.idCard != null\">\r\n          <el-input v-model=\"dispatchForm.idCard\" placeholder=\"请输入身份证号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"人脸照片\" prop=\"faceImg\" v-if=\"dispatchForm.photo != null && dispatchForm.photo != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.faceImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n                      lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.photo\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"驾驶证照片\" prop=\"driverLicenseImgs\"\r\n          v-if=\"dispatchForm.driverLicenseImgs != null && dispatchForm.driverLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.drivingLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.driverLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"行驶证照片\" prop=\"vehicleLicenseImgs\"\r\n          v-if=\"dispatchForm.vehicleLicenseImgs != null && dispatchForm.vehicleLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.driverLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"dispatchForm.vehicleLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"货车\" prop=\"carUUId\" :rules=\"[{ required: true, message: '货车信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"dispatchForm.carUUId\" filterable :filter-method=\"filterCarData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleCarChange\">\r\n            <el-option v-for=\"item in filteredCarOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.carNumber\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewCarWindow\">前往新增或修改货车信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车牌号\" prop=\"carNumber\" v-if=\"dispatchForm.carNumber != null\">\r\n          <el-input v-model=\"dispatchForm.carNumber\" placeholder=\"请输入车牌号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"车辆排放标准\" prop=\"vehicleEmissionStandards\"\r\n          v-if=\"dispatchForm.vehicleEmissionStandards != null\">\r\n          <el-select v-model=\"dispatchForm.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled\r\n            style=\"width:300px\">\r\n            <el-option v-for=\"dict in vehicleEmissionStandardsOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"任务类型\" prop=\"taskType\" :rules=\"[{ required: true, message: '任务类型不能为空' }]\"\r\n          v-if=\"isTaskTypeEdit == true\">\r\n          <el-select v-model=\"dispatchForm.taskType\" placeholder=\"请选择车任务类型\" style=\"width:300px\">\r\n            <el-option v-for=\"dict in taskTypeOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 新增物资选择表格 -->\r\n        <el-form-item label=\"物资选择\" prop=\"selectedMaterials\"\r\n          v-if=\"planInfo.measureFlag == 0 && dispatchForm.taskType == 2\">\r\n          <el-table :data=\"materialSelectionList\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" width=\"50\" label=\"序号\"></el-table-column>\r\n            <el-table-column prop=\"materialName\" label=\"物资名称\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <el-select v-model=\"scope.row.materialId\" placeholder=\"请选择物资\"\r\n                  @change=\"handleMaterialChange(scope.row, scope.$index)\">\r\n                  <el-option v-for=\"item in availableMaterials\" :key=\"item.materialId\" :label=\"item.materialName\"\r\n                    :value=\"item.materialId\" :disabled=\"isMaterialAvailable(item)\">\r\n\r\n                  </el-option>\r\n                </el-select>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"materialSpec\" label=\"物资规格\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.materialSpec }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"planNum\" label=\"计划数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.planNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"remainingNum\" label=\"剩余数量\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.remainingNum }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"currentNum\" label=\"本次数量\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number v-model=\"scope.row.currentNum\" :min=\"0\" :max=\"getMaxAvailableNum(scope.row)\"\r\n                  @change=\"handleNumChange($event, scope.$index)\" :disabled=\"!scope.row.materialId\">\r\n                </el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button type=\"text\" icon=\"el-icon-delete\" @click=\"removeMaterial(scope.$index)\"\r\n                  :disabled=\"materialSelectionList.length === 1\">\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <div style=\"margin-top: 10px;\">\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addMaterialRow\">添加物资</el-button>\r\n          </div>\r\n        </el-form-item>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dispatchDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitDispatchForm\">确 认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { detailPlan, approve, discard, listTaskMaterial, confirmMaterial } from \"@/api/leave/plan\";\r\nimport { listAllTask, addTask, addTaskMaterial, addTaskAndMaterial, addLeaveLog, isAllowDispatch, addTaskAndMaterialAndAddLeaveLog } from \"@/api/leave/task\";\r\nimport { listAllDriver,  getXctgDriverUserListByPage, getXctgDriverCarListByPage } from \"@/api/dgcb/driver/driver\";\r\nimport { mount } from \"sortablejs\";\r\nexport default {\r\n  name: \"DetailLeavePlan\",\r\n  data() {\r\n    // 验证车牌号\r\n    const validateCarNumber = (rule, value, callback) => {\r\n      const pattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的车牌号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证手机号\r\n    const validatePhone = (rule, value, callback) => {\r\n      const pattern = /^1[3-9]\\d{9}$/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的手机号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    // 验证身份证号\r\n    const validateIdCard = (rule, value, callback) => {\r\n      const pattern = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n      if (!pattern.test(value)) {\r\n        callback(new Error('请输入正确的身份证号'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      isTaskTypeEdit: true,\r\n      vehicleEmissionStandardsOptions: [],\r\n      taskTypeOptions: [],\r\n      carList: [],\r\n      searchCarQuery: '',\r\n      filteredCarOptions: [],\r\n      driverList: [],\r\n      searchDriverQuery: '',\r\n      filteredDriverOptions: [],\r\n      //审核表单\r\n      approveForm: {\r\n        applyNo: null,\r\n        approveContent: '',//审核意见\r\n        approveFlag: true,//审核状态\r\n      },\r\n\r\n      // 图片列表\r\n      imageList: [],\r\n\r\n      // 文件列表\r\n      fileList: [],\r\n\r\n      // 派车弹框可见性\r\n      dispatchDialogVisible: false,\r\n\r\n      taskListInfo: [],\r\n\r\n      // 派车表单数据\r\n      dispatchForm: {\r\n        // carNumber: '',\r\n        // driverName: '',\r\n        // driverPhone: '',\r\n        // driverIdCard: ''\r\n      },\r\n\r\n      // 派车表单验证规则\r\n      dispatchRules: {\r\n        carNumber: [\r\n          { required: true, message: '请输入车牌号', trigger: 'blur' },\r\n          { validator: validateCarNumber, trigger: 'blur' }\r\n        ],\r\n        driverName: [\r\n          { required: true, message: '请输入司机姓名', trigger: 'blur' },\r\n          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }\r\n        ],\r\n        driverPhone: [\r\n          { required: true, message: '请输入司机手机号', trigger: 'blur' },\r\n          { validator: validatePhone, trigger: 'blur' }\r\n        ],\r\n        driverIdCard: [\r\n          { required: true, message: '请输入司机身份证号', trigger: 'blur' },\r\n          { validator: validateIdCard, trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      // 派车列表数据\r\n      dispatchList: [\r\n        {\r\n          id: 1,\r\n          carNumber: '京A12345',\r\n          driverName: '王小明',\r\n          driverPhone: '13800138000',\r\n          driverIdCard: '110101199001010001',\r\n          dispatchTime: '2025-03-18 09:30:00',\r\n          status: 2,\r\n          tareWeight: 8500,\r\n          grossWeight: 15800,\r\n          recheckedGrossWeight: 15750,\r\n          recheckedTareWeight: 8480\r\n        },\r\n        {\r\n          id: 2,\r\n          carNumber: '京B98765',\r\n          driverName: '李大壮',\r\n          driverPhone: '13900139000',\r\n          driverIdCard: '110101199102020002',\r\n          dispatchTime: '2025-03-19 14:15:00',\r\n          status: 1,\r\n          tareWeight: 7800,\r\n          grossWeight: 12600,\r\n          recheckedGrossWeight: null,\r\n          recheckedTareWeight: null\r\n        }\r\n      ],\r\n\r\n      // 计划详情信息\r\n      planInfo: {},\r\n      applyNo: null,\r\n      taskQueryParams: {\r\n        applyNo: null,\r\n      },\r\n\r\n      taskMaterialList: null,\r\n      // 物资选择相关数据\r\n      materialSelectionList: [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        usedNum: 0,\r\n        remainingNum: 0,\r\n        currentNum: 0\r\n      }],\r\n      availableMaterials: [], // 可选的物资列表\r\n      taskMaterialListMap: new Map(), // 已派车的物资列表\r\n      taskMaterialMap: new Map(), // 存储所有任务物资的映射\r\n    };\r\n  },\r\n  computed: {\r\n    // 判断是否可以派车\r\n    canDispatchCar() {\r\n      // 判断申请单是否已通过\r\n      // const isPlanApproved = this.planInfo.planStatus === 2;\r\n\r\n      // // 如果是非计量类型，且已经派过车，则不能再派车\r\n      // if (this.planInfo.measureFlag !== 1 && this.dispatchList.length > 0) {\r\n      //   return false;\r\n      // }\r\n\r\n      return true;\r\n    },\r\n    // 默认显示前50条，若有搜索，则显示搜索后的数据\r\n    displayDriverListOptions() {\r\n      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);\r\n    },\r\n    displayCarListOptions() {\r\n      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);\r\n    },\r\n    canShowMaterialConfirm() {\r\n      // 只有planStatus为5或6时显示（已出厂/部分收货），且不是已完成/废弃/驳回/过期\r\n      return [5, 6].includes(this.planInfo.planStatus);\r\n    }\r\n  },\r\n  activated() {\r\n    this.getDicts(\"xctg_driver_car_emission_standards\").then(response => {\r\n      this.vehicleEmissionStandardsOptions = response.data;\r\n    });\r\n    // 初始化任务类型选项（在获取计划信息后会重新更新）\r\n    this.getDicts(\"leave_task_type\").then(response => {\r\n      this.taskTypeOptions = response.data;\r\n    });\r\n    // 获取路由参数中的ID\r\n    const applyNo = this.$route.params.applyNo;\r\n    this.applyNo = applyNo\r\n    this.taskQueryParams.applyNo = applyNo;\r\n    this.approveForm.applyNo = applyNo;\r\n    if (applyNo) {\r\n      detailPlan(applyNo).then(response => {\r\n        this.planInfo = response.data;\r\n        this.taskTypeEditUpdate();\r\n        console.log(\"this.planInfo\", this.planInfo);\r\n        // 解析图片和文件数据\r\n        this.parseImageAndFileData();\r\n        // 获取任务信息后更新任务类型选项\r\n        this.getListTaskInfo();\r\n      });\r\n    };\r\n    this.getDriverList();\r\n    this.getCarList();\r\n\r\n\r\n\r\n  },\r\n\r\n\r\n  methods: {\r\n    handleApprove() {\r\n      this.approveForm.approveFlag = true;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('审核通过');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleReject() {\r\n      this.approveForm.approveFlag = false;\r\n      console.log(\"this.approveForm\", this.approveForm);\r\n      approve(this.approveForm).then(response => {\r\n        this.$message.success('驳回成功');\r\n        // 跳转到列表页面并刷新\r\n        this.$router.push({\r\n          path: \"/leave/leavePlanList\",\r\n          query: {\r\n            t: Date.now(),\r\n            refresh: true // 添加刷新标记\r\n          }\r\n        });\r\n      }).catch(error => {\r\n        this.$message.error('审核失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    handleDiscard() {\r\n      discard(this.planInfo).then(response => {\r\n        this.$message.success('废弃成功');\r\n\r\n        if (window.history.length > 1) {\r\n          this.$router.go(-1);\r\n        } else {\r\n          this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('废弃失败');\r\n        console.error('Approval error:', error);\r\n      });\r\n    },\r\n    taskTypeEditUpdate() {\r\n      if (this.planInfo.planType !== 2) {\r\n        this.isTaskTypeEdit = false;\r\n      }\r\n    },\r\n    // 更新任务类型选项\r\n    updateTaskTypeOptions() {\r\n      // 获取原始的任务类型选项\r\n      this.getDicts(\"leave_task_type\").then(response => {\r\n        let options = response.data;\r\n        console.log(\"原始任务类型选项:\", options);\r\n        console.log(\"计划类型:\", this.planInfo.planType);\r\n        console.log(\"任务数量:\", this.taskListInfo.length);\r\n\r\n        // 对于出厂返回任务（planType=2）\r\n        if (this.planInfo.planType === 2) {\r\n          // 如果当前任务数为0，只显示出厂选项\r\n          if (this.taskListInfo.length === 0) {\r\n            options = options.filter(option =>\r\n              option.dictValue === '1' || option.dictValue === 1\r\n            ); // 只保留出厂选项，兼容字符串和数字类型\r\n            console.log(\"过滤后只保留出厂选项:\", options);\r\n          }\r\n          // 如果已有任务，显示所有选项（出厂和返厂）\r\n        } else {\r\n          // 对于其他计划类型，保持原有逻辑\r\n          if (this.planInfo.planType !== 3) {\r\n            options = options.filter(option =>\r\n              option.dictValue !== '3' && option.dictValue !== 3\r\n            ); // 移除跨区调拨选项，兼容字符串和数字类型\r\n          }\r\n        }\r\n\r\n        console.log(\"最终任务类型选项:\", options);\r\n        this.taskTypeOptions = options;\r\n        // 强制刷新下拉框\r\n        this.$nextTick(() => {\r\n          this.$forceUpdate();\r\n        });\r\n      });\r\n    },\r\n    getListTaskInfo() {\r\n      listAllTask(this.taskQueryParams).then(response => {\r\n        console.log(\"response.data\", response.rows);\r\n        this.taskListInfo = response.data;\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        // 获取所有任务物资\r\n        this.getAllTaskMaterials();\r\n        // 更新任务类型选项（基于当前任务数）\r\n        this.updateTaskTypeOptions();\r\n      });\r\n    },\r\n    openNewDriverWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    openNewCarWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    // 1国五，2国六，3新能源字典翻译\r\n    vehicleEmissionStandardsFormat(row, column) {\r\n      return this.selectDictLabel(this.vehicleEmissionStandardsOptions, row.vehicleEmissionStandards);\r\n    },\r\n\r\n    taskTypeFormat(row, column) {\r\n      return this.getTaskTypeText(row.taskType);\r\n    },\r\n    taskStatusFormat(row, column) {\r\n      return this.getStatusText(row.taskStatus);\r\n    },\r\n\r\n    /** 查询司机信息列表 */\r\n    getCarList() {\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverCarListByPage().then(response => {\r\n        this.carList = response.rows;\r\n        this.filteredCarOptions = this.carList;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterCarData(query) {\r\n      this.searchCarQuery = query;\r\n\r\n      if (this.searchCarQuery) {\r\n        // 调用后端接口进行搜索\r\n        const searchParams = {\r\n          carNumber: query\r\n        };\r\n        getXctgDriverCarListByPage(searchParams).then(response => {\r\n          this.filteredCarOptions = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('搜索货车失败:', error);\r\n          this.filteredCarOptions = [];\r\n        });\r\n      } else {\r\n        // 如果没有搜索条件，显示前50条数据\r\n        this.filteredCarOptions = this.carList.slice(0, 50);\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleDriverChange() {\r\n      if (this.dispatchForm.driverId != null) {\r\n        this.driverList.forEach(item => {\r\n          if (item.id == this.dispatchForm.driverId) {\r\n            this.dispatchForm.name = item.name;\r\n            this.dispatchForm.idCard = item.idCard;\r\n            this.dispatchForm.company = item.company;\r\n            this.dispatchForm.phone = item.phone;\r\n            this.dispatchForm.photo = item.photo;\r\n            this.dispatchForm.faceImgList = item.faceImgList;\r\n            this.dispatchForm.driverLicenseImgs = item.driverLicenseImgs;\r\n            this.dispatchForm.vehicleLicenseImgs = item.vehicleLicenseImgs;\r\n            this.dispatchForm.sex = item.gender;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n    //通过driverId获取司机信息\r\n    handleCarChange() {\r\n      console.log(\"handleCarChange\")\r\n      if (this.dispatchForm.carUUId != null) {\r\n        this.carList.forEach(item => {\r\n          if (item.id == this.dispatchForm.carUUId) {\r\n            this.dispatchForm.carNumber = item.carNumber;\r\n\r\n            if (item.vehicleEmissionStandards == 1) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"国五\";\r\n            } else if (item.vehicleEmissionStandards == 2) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"国六\";\r\n            } else if (item.vehicleEmissionStandards == 3) {\r\n              this.dispatchForm.vehicleEmissionStandards = \"新能源\";\r\n            } else {\r\n              this.dispatchForm.vehicleEmissionStandards = \"\";\r\n            }\r\n            this.dispatchForm.licensePlateColor = item.licensePlateColor;\r\n            this.dispatchForm.carId = item.carId;\r\n            this.dispatchForm.trailerNumber = item.trailerNumber;\r\n            this.dispatchForm.trailerId = item.trailerId;\r\n            this.dispatchForm.axisType = item.axisType;\r\n            this.dispatchForm.driverWeight = item.driverWeight;\r\n            this.dispatchForm.maxWeight = item.maxWeight;\r\n            this.dispatchForm.engineNumber = item.engineNumber;\r\n            this.dispatchForm.vinNumber = item.vinNumber;\r\n          }\r\n\r\n        });\r\n      }\r\n    },\r\n    /** 查询司机信息列表 */\r\n    getDriverList() {\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverUserListByPage().then(response => {\r\n        this.driverList = response.rows;\r\n        console.log(\"this.driverList\", this.driverList);\r\n        this.filteredDriverOptions = this.driverList;\r\n      });\r\n    },\r\n    // 搜索过滤逻辑\r\n    filterDriverData(query) {\r\n      this.searchDriverQuery = query;\r\n\r\n      if (this.searchDriverQuery) {\r\n        // 调用后端接口进行搜索\r\n        const searchParams = {\r\n          searchValue: query\r\n        };\r\n        getXctgDriverUserListByPage(searchParams).then(response => {\r\n          this.filteredDriverOptions = response.rows || [];\r\n        }).catch(error => {\r\n          console.error('搜索货车司机失败:', error);\r\n          this.filteredDriverOptions = [];\r\n        });\r\n      } else {\r\n        // 如果没有搜索条件，显示前50条数据\r\n        this.filteredDriverOptions = this.driverList.slice(0, 50);\r\n      }\r\n    },\r\n    // 解析图片和文件数据\r\n    parseImageAndFileData() {\r\n      // 解析图片数据\r\n      if (this.planInfo.applyImgUrl) {\r\n        try {\r\n          this.imageList = JSON.parse(this.planInfo.applyImgUrl);\r\n        } catch (e) {\r\n          console.error('解析图片数据失败:', e);\r\n          this.imageList = [];\r\n        }\r\n      }\r\n\r\n      // 解析文件数据\r\n      if (this.planInfo.applyFileUrl) {\r\n        try {\r\n          this.fileList = JSON.parse(this.planInfo.applyFileUrl);\r\n        } catch (e) {\r\n          console.error('解析文件数据失败:', e);\r\n          this.fileList = [];\r\n        }\r\n      }\r\n    },\r\n\r\n    // 下载文件\r\n    downloadFile(url, fileName) {\r\n      if (!url) {\r\n        this.$message.error('文件链接无效');\r\n        return;\r\n      }\r\n\r\n      // 创建一个a元素用于下载\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName || '下载文件';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    },\r\n\r\n    // 获取计划类型文本\r\n    getPlanTypeText(type) {\r\n      const typeMap = {\r\n        1: '出厂不返回',\r\n        2: '出厂返回',\r\n        3: '跨区调拨',\r\n        4: '退货申请'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取业务类型文本\r\n    getBusinessCategoryText(category) {\r\n      const categoryMap = {\r\n        1: '通用',\r\n        11: '通用',\r\n        12: '委外加工',\r\n        21: '有计划量计量',\r\n        22: '短期',\r\n        23: '钢板（圆钢）',\r\n        31: '通用'\r\n      };\r\n      return categoryMap[category] || '未知类型';\r\n    },\r\n\r\n    // 获取物资类型文本\r\n    getMaterialTypeText(type) {\r\n      const typeMap = {\r\n        1: '钢材',\r\n        2: '钢板',\r\n        3: '其他'\r\n      };\r\n      return typeMap[type] || '未知类型';\r\n    },\r\n\r\n    // 获取计划状态文本\r\n    getPlanStatusText(status) {\r\n      const statusMap = {\r\n        1: '待分厂审批',\r\n        2: '待分厂复审',\r\n        3: '待生产指挥中心审批',\r\n        4: '审批完成',\r\n        5: '已出厂',\r\n        6: '部分收货',\r\n        7: '已完成',\r\n        11: '驳回',\r\n        12: '废弃',\r\n        13: '过期'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取日志颜色\r\n    getLogColor(log) {\r\n      const logTypeColorMap = {\r\n        1: '#409EFF', // 创建\r\n        2: '#67C23A', // 审批\r\n        3: '#E6A23C', // 流转\r\n        4: '#F56C6C', // 驳回\r\n        5: '#909399'  // 其他\r\n      };\r\n      return logTypeColorMap[log.logType] || '#409EFF';\r\n    },\r\n\r\n    // 获取派车状态文本\r\n    getDispatchStatusText(status) {\r\n      const statusMap = {\r\n        0: '待出发',\r\n        1: '已出发',\r\n        2: '已到达',\r\n        3: '已完成',\r\n        4: '已取消'\r\n      };\r\n      return statusMap[status] || '未知状态';\r\n    },\r\n\r\n    // 获取派车状态类型（用于标签颜色）\r\n    getDispatchStatusType(status) {\r\n      const statusMap = {\r\n        0: 'info',\r\n        1: 'primary',\r\n        2: 'success',\r\n        3: 'success',\r\n        4: 'danger'\r\n      };\r\n      return statusMap[status] || 'info';\r\n    },\r\n\r\n    // 获取计划类型标签样式\r\n    getPlanTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'success',  // 出厂不返回\r\n        2: 'warning',  // 出厂返回\r\n        3: 'info',     // 跨区调拨\r\n        4: 'danger'    // 退货申请\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取物资类型标签样式\r\n    getMaterialTypeTagType(type) {\r\n      const typeMap = {\r\n        1: 'primary',  // 钢材\r\n        2: 'success',  // 钢板\r\n        3: 'info'      // 其他\r\n      };\r\n      return typeMap[type] || 'info';\r\n    },\r\n\r\n    // 获取业务类型标签样式\r\n    getBusinessCategoryTagType(category) {\r\n      const typeMap = {\r\n        '1': 'primary',   // 通用\r\n        '11': 'primary',  // 通用\r\n        '12': 'warning',  // 委外加工\r\n        '21': 'success',  // 有计划量计量\r\n        '22': 'info',     // 短期\r\n        '23': 'danger',   // 钢板（圆钢）\r\n        '31': 'primary'   // 通用\r\n      };\r\n      return typeMap[category] || 'info';\r\n    },\r\n\r\n    // 打开派车弹框\r\n    openDispatchDialog() {\r\n      // 初始化物资数据\r\n      this.availableMaterials = this.planInfo.materials || [];\r\n      console.log(\"this.availableMaterials\", this.availableMaterials);\r\n\r\n      // 获取已派车的物资列表，并在回调中初始化 materialSelectionList\r\n      this.getTaskMaterialListAndInitSelection();\r\n      // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n      // 判断用户角色权限\r\n      const roles = this.$store.getters.roles;\r\n      console.log(\"roles\", roles);\r\n      if (!roles.includes('leave.supplier') && !roles.includes('leave.applicant')) {\r\n        this.$message.error('您没有派车权限');\r\n        return;\r\n      }\r\n\r\n      console.log(\"this.planInfo.planStatus\", this.planInfo.planStatus);\r\n      if (![4, 5, 6].includes(this.planInfo.planStatus)) {\r\n        this.$message.warning('当前状态无法派车');\r\n        return;\r\n      }\r\n\r\n\r\n\r\n\r\n      console.log(\"openDispatchDialog\", this.taskListInfo.length);\r\n      if (this.planInfo.businessCategory == 22 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('短期计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      if (this.planInfo.businessCategory == 23 && this.taskListInfo.length >= 1) {\r\n        this.$message.warning('钢板（圆钢）计划只允许派一次车');\r\n        return;\r\n      }\r\n\r\n      this.dispatchForm = {};\r\n\r\n      // 更新任务类型选项\r\n      this.updateTaskTypeOptions();\r\n\r\n      if (this.planInfo.planType == 1) {\r\n        this.dispatchForm.taskType = \"1\"\r\n      } else if (this.planInfo.planType == 3) {\r\n        this.dispatchForm.taskType = \"3\"\r\n      } else if (this.planInfo.planType == 4) {\r\n        this.dispatchForm.taskType = \"1\"\r\n      } else if (this.planInfo.planType == 2) {\r\n        // 对于出厂返回任务，根据当前任务数决定默认任务类型\r\n        if (this.taskListInfo.length === 0) {\r\n          this.dispatchForm.taskType = \"1\"; // 默认选择出厂\r\n        } else {\r\n          this.dispatchForm.taskType = \"2\"; // 默认选择返厂\r\n        }\r\n      }\r\n      console.log(this.dispatchForm.taskType),\r\n        this.dispatchDialogVisible = true;\r\n\r\n\r\n    },\r\n\r\n    // 新增方法\r\n    getTaskMaterialListAndInitSelection() {\r\n      // 清空已用数量映射\r\n      this.taskMaterialListMap.clear();\r\n      // 统计所有已派车物资\r\n      const type2List = this.taskListInfo.filter(item => item.taskType === 2);\r\n      console.log(\"type2List\", type2List);\r\n      if (!type2List || type2List.length === 0) {\r\n        // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n        this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n          // const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n          // const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n          return {\r\n            materialId: mat.materialId,\r\n            materialName: mat.materialName,\r\n            materialSpec: mat.materialSpec,\r\n            planNum: mat.planNum,\r\n            usedNum: 0,\r\n            remainingNum: mat.planNum,\r\n            currentNum: mat.planNum\r\n          };\r\n        });\r\n      } else {\r\n        console.log(\"this.taskListInfo\", this.taskListInfo);\r\n        type2List.forEach(task => {\r\n          const params = { taskNo: task.taskNo };\r\n          listTaskMaterial(params).then(response => {\r\n            let taskMaterials = response.rows || [];\r\n            taskMaterials.forEach(material => {\r\n              if (!this.taskMaterialListMap.has(material.materialId)) {\r\n                this.taskMaterialListMap.set(material.materialId, {\r\n                  taskMaterialInfo: material,\r\n                  usedNum: material.planNum\r\n                });\r\n              } else {\r\n                const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n                existingMaterial.usedNum += material.planNum;\r\n              }\r\n            });\r\n\r\n            // 将taskMaterialListMap转换为数组集合\r\n            this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n              materialId: key,\r\n              ...value\r\n            }));\r\n\r\n            // 初始化 materialSelectionList：全部选上且数量为剩余数量\r\n            this.materialSelectionList = (this.planInfo.materials || []).map(mat => {\r\n              const usedNum = (this.taskMaterialListMap.get(mat.materialId)?.usedNum) || 0;\r\n              const remainingNum = Math.max((mat.planNum || 0) - usedNum, 0);\r\n\r\n              return {\r\n                materialId: mat.materialId,\r\n                materialName: mat.materialName,\r\n                materialSpec: mat.materialSpec,\r\n                planNum: mat.planNum,\r\n                usedNum: usedNum,\r\n                remainingNum: remainingNum,\r\n                currentNum: remainingNum\r\n              };\r\n            });\r\n\r\n            this.materialSelectionList = this.materialSelectionList.filter(item => item.remainingNum > 0);\r\n          });\r\n        });\r\n      }\r\n\r\n         // 判断非计量且taskType为1的情况\r\n      if (this.planInfo.measureFlag == 0) {\r\n        if (this.dispatchForm.taskType == 1) {\r\n          // 检查是否已经有taskType为1的任务\r\n          const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n          if (hasType1Task) {\r\n            this.$message.warning('非计量只能派车出厂一次');\r\n            return;\r\n          }\r\n          console.log(\"hasType1Task\", hasType1Task)\r\n        }\r\n      }\r\n\r\n\r\n    },\r\n\r\n    // 重置派车表单\r\n    resetDispatchForm() {\r\n      this.$refs.dispatchForm && this.$refs.dispatchForm.resetFields();\r\n      this.materialSelectionList = [{\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      }];\r\n    },\r\n\r\n    // 获取已派车的物资列表\r\n    getTaskMaterialList() {\r\n      // 从taskListInfo中获取已派车的物资信息\r\n      this.taskMaterialListMap.clear();\r\n      this.taskListInfo.forEach(task => {\r\n        const params = {\r\n          taskNo: task.taskNo,\r\n        };\r\n        listTaskMaterial(params).then(response => {\r\n          console.log(\"listTaskMaterial\", response.rows);\r\n          let taskMaterials = [];\r\n          taskMaterials = response.rows;\r\n          taskMaterials.forEach(material => {\r\n            if (!this.taskMaterialListMap.has(material.materialId)) {\r\n              this.taskMaterialListMap.set(material.materialId, {\r\n                taskMaterialInfo: material,\r\n                usedNum: material.planNum\r\n              });\r\n            } else {\r\n              const existingMaterial = this.taskMaterialListMap.get(material.materialId);\r\n              existingMaterial.usedNum += material.planNum;\r\n            }\r\n          });\r\n          // 将taskMaterialListMap转换为数组集合\r\n          this.taskMaterialList = Array.from(this.taskMaterialListMap, ([key, value]) => ({\r\n            materialId: key,\r\n            ...value\r\n          }));\r\n          console.log(\"taskMaterialArray\", this.taskMaterialList);\r\n          console.log(\"taskMaterialListMap\", this.taskMaterialListMap);\r\n        });\r\n      });\r\n    },\r\n\r\n    // 添加物资行\r\n    addMaterialRow() {\r\n      this.materialSelectionList.push({\r\n        materialId: null,\r\n        materialName: '',\r\n        materialSpec: '',\r\n        planNum: 0,\r\n        remainingNum: 0,\r\n        usedNum: 0,\r\n        currentNum: 0\r\n      });\r\n    },\r\n\r\n    // 移除物资行\r\n    removeMaterial(index) {\r\n      this.materialSelectionList.splice(index, 1);\r\n    },\r\n\r\n    // 处理物资选择变化\r\n    handleMaterialChange(row, index) {\r\n      console.log(\"handleMaterialChange\", this.taskMaterialList);\r\n\r\n\r\n      const selectedMaterial = this.taskMaterialList.find(item => item.materialId === row.materialId);\r\n      if (selectedMaterial) {\r\n        row.usedNum = selectedMaterial.usedNum;\r\n      }\r\n      const selectPlanMaterial = this.planInfo.materials.find(item => item.materialId === row.materialId);\r\n\r\n      if (selectPlanMaterial) {\r\n        row.planNum = selectPlanMaterial.planNum;\r\n        row.materialName = selectPlanMaterial.materialName;\r\n        row.materialSpec = selectPlanMaterial.materialSpec;\r\n      }\r\n\r\n      row.remainingNum = row.planNum - row.usedNum;\r\n      row.currentNum = row.planNum - row.usedNum;\r\n\r\n      console.log(\"handleMaterialChange\", row, index);\r\n\r\n    },\r\n\r\n    // 获取物资最大可用数量\r\n    getMaxAvailableNum(row) {\r\n      if (!row.materialId) return 0;\r\n\r\n      // 从taskMaterialListMap中获取已用数量\r\n      const materialInfo = this.taskMaterialListMap.get(row.materialId);\r\n      const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      return row.planNum - usedNum;\r\n    },\r\n\r\n    // 处理数量变化\r\n    handleNumChange(value, index) {\r\n      if (!this.materialSelectionList[index]) return;\r\n\r\n      const row = this.materialSelectionList[index];\r\n      if (!row.materialId) return;\r\n\r\n      // 从taskMaterialListMap中获取已用数量\r\n      const materialInfo = this.taskMaterialListMap.get(row.materialId);\r\n      const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      // 计算最大可用数量\r\n      const maxAvailable = row.planNum - usedNum;\r\n\r\n      // 如果当前值大于最大可用数量，则将值置为最大可用数量\r\n      if (value > maxAvailable) {\r\n        this.$nextTick(() => {\r\n          row.currentNum = maxAvailable;\r\n        });\r\n      }\r\n    },\r\n\r\n    // 判断物资是否可选\r\n    isMaterialAvailable(material) {\r\n      // 从taskMaterialListMap中获取已用数量\r\n      // const materialInfo = this.taskMaterialListMap.get(material.id);\r\n      // const usedNum = materialInfo ? materialInfo.usedNum : 0;\r\n\r\n      // let selected = false;\r\n\r\n      // this.availableMaterials.forEach(item => {\r\n      //   if (item.materialId === material.materialId) {\r\n      //     selected = true;\r\n      //   }\r\n      // });\r\n\r\n      return this.materialSelectionList.some(row => row.materialId === material.materialId);;\r\n    },\r\n\r\n    // 修改提交派车表单方法\r\n    submitDispatchForm() {\r\n      this.$refs.dispatchForm.validate(valid => {\r\n        if (valid) {\r\n          // 判断非计量且taskType为1的情况\r\n          if (this.planInfo.measureFlag == 0) {\r\n            if (this.dispatchForm.taskType == 1) {\r\n              // 检查是否已经有taskType为1的任务\r\n              const hasType1Task = this.taskListInfo.some(task => task.taskType === 1);\r\n              if (hasType1Task) {\r\n                this.$message.warning('非计量只能派车出厂一次');\r\n                return;\r\n              }\r\n            }\r\n          }\r\n\r\n          // 新集合\r\n          let resultList = [];\r\n\r\n          console.log(\"this.planInfo.measureFlag\", this.planInfo.measureFlag);\r\n          console.log(\"this.dispatchForm.taskType\", this.dispatchForm.taskType);\r\n\r\n          if (this.planInfo.measureFlag == 0 && this.dispatchForm.taskType == 2) {\r\n            this.materialSelectionList.forEach(selRow => {\r\n              // 在 planInfo.materials 中查找相同 materialId 的元素\r\n              const planMaterial = (this.planInfo.materials || []).find(\r\n                mat => mat.materialId === selRow.materialId\r\n              );\r\n              if (planMaterial) {\r\n                // 深拷贝一份，避免影响原数据\r\n                const newItem = { ...planMaterial };\r\n                newItem.planNum = selRow.currentNum; // 设置为本次数量\r\n                resultList.push(newItem);\r\n              }\r\n            });\r\n\r\n            // resultList 即为你需要的新集合\r\n            console.log('this.materialSelectionList', this.materialSelectionList);\r\n            console.log('resultList', resultList);\r\n\r\n            // 物资校验：必须有物资\r\n            if (!this.materialSelectionList.length) {\r\n              this.$message.warning('请至少选择一种物资');\r\n              return;\r\n            }\r\n\r\n            // 校验每一行物资\r\n            const hasInvalidMaterial = this.materialSelectionList.some(row => {\r\n              // 必须选择物资，数量>0，且数量<=剩余数量\r\n              return (\r\n                !row.materialId ||\r\n                row.currentNum <= 0 ||\r\n                row.currentNum > row.remainingNum\r\n              );\r\n            });\r\n\r\n            if (hasInvalidMaterial) {\r\n              this.$message.warning('请选择物资且本次数量需大于0且不超过剩余数量');\r\n              return;\r\n            }\r\n          } else {\r\n            console.log(\"this.planInfo.materials\", this.planInfo.materials);\r\n            resultList = this.planInfo.materials ? this.planInfo.materials.map(item => ({ ...item })) : [];\r\n            console.log(\"123321\", resultList);\r\n          }\r\n\r\n\r\n\r\n\r\n\r\n          if (this.planInfo.measureFlag == 1 && this.dispatchForm.taskType !== 2) {\r\n            this.dispatchForm.taskStatus = 1;\r\n          } else {\r\n            this.dispatchForm.taskStatus = 4;\r\n          }\r\n\r\n          if (this.dispatchForm.taskType == 2) {\r\n            this.dispatchForm.taskStatus = 5;\r\n          }\r\n\r\n\r\n          //是否直供默认为0\r\n          this.dispatchForm.isDirectSupply = 0;\r\n          // todo 任务状态确认\r\n          this.dispatchForm.applyNo = this.applyNo;\r\n          this.dispatchForm.planNo = this.planInfo.planNo;\r\n          this.dispatchForm.carNum = this.dispatchForm.carNumber;\r\n          this.dispatchForm.companyName = this.dispatchForm.company;\r\n          this.dispatchForm.driverLicenseImg = this.dispatchForm.driverLicenseImgs;\r\n          this.dispatchForm.driverName = this.dispatchForm.name;\r\n          this.dispatchForm.mobilePhone = this.dispatchForm.phone;\r\n          this.dispatchForm.faceImg = this.dispatchForm.photo;\r\n          this.dispatchForm.drivingLicenseImg = this.dispatchForm.vehicleLicenseImgs;\r\n          this.dispatchForm.idCardNo = this.dispatchForm.idCard;\r\n          if (this.dispatchForm.sex == \"1\") {\r\n            this.dispatchForm.sex = 1;\r\n          } else if (this.dispatchForm.sex == \"2\") {\r\n            this.dispatchForm.sex = 2;\r\n          }\r\n          if (this.dispatchForm.vehicleEmissionStandards == \"国五\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 1;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"国六\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 2;\r\n          } else if (this.dispatchForm.vehicleEmissionStandards == \"新能源\") {\r\n            this.dispatchForm.vehicleEmissionStandards = 3;\r\n          }\r\n          console.log(\"this.dispatchForm\", this.dispatchForm);\r\n\r\n          let dispatchInfo = {};\r\n          dispatchInfo.carNum = this.dispatchForm.carNum;\r\n\r\n          isAllowDispatch(dispatchInfo).then(response => {\r\n            let row = response.data;\r\n            if (row > 0) {\r\n              this.$message.error(\"当前车有正在执行的任务\")\r\n            } else {\r\n              let param = {};\r\n              param.leaveTask = this.dispatchForm;\r\n              param.leaveTaskMaterialList = resultList;\r\n              addTaskAndMaterialAndAddLeaveLog(param).then(res => {\r\n                console.log(\"addTaskAndMaterialAndAddLeaveLog\", res)\r\n                if (res.code == 200) {\r\n                  this.$message.success('派车成功');\r\n                  this.dispatchDialogVisible = false;\r\n                  this.getListTaskInfo();\r\n                } else {\r\n                  // 其他失败原因\r\n                  this.$message.error(res.message || '派车失败');\r\n                }\r\n              }).catch(err => {\r\n                console.error('dispatch error:', err);\r\n                this.$message.error('网络异常，稍后重试');\r\n              });\r\n\r\n              // addTaskAndMaterial(this.dispatchForm).then(response => {\r\n              //   console.log(\"addTaskAndMaterial\", response);\r\n              //   let snowId = response.data;\r\n              //   this.planInfo.materials.forEach(item => {\r\n              //     item.taskNo = snowId;\r\n              //     addTaskMaterial(item);\r\n              //   });\r\n\r\n              //   console.log(\"生成派车日志\");\r\n\r\n              //   //生成派车日志\r\n              //   let leaveTaskLog = {};\r\n\r\n\r\n              //   leaveTaskLog.logType = 2;\r\n              //   leaveTaskLog.taskNo = snowId;\r\n              //   leaveTaskLog.applyNo = this.applyNo;\r\n              //   leaveTaskLog.info = '派车任务创建：' + this.dispatchForm.carNum + ' ' + this.dispatchForm.driverName\r\n              //   addLeaveLog(leaveTaskLog);\r\n\r\n              //   this.$message.success('派车成功');\r\n              //   this.dispatchDialogVisible = false;\r\n              //   this.getListTaskInfo();\r\n              // });\r\n\r\n              this.dispatchDialogVisible = false;\r\n            }\r\n            console.log(\"this.isAllowDispatch\", response);\r\n          }).catch(err => {\r\n            console.error('dispatch error:', err);\r\n            this.$message.error('网络异常，稍后重试');\r\n          });\r\n\r\n        } else {\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 格式化日期时间\r\n    formatDateTime(date) {\r\n      const year = date.getFullYear();\r\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n      const day = date.getDate().toString().padStart(2, '0');\r\n      const hours = date.getHours().toString().padStart(2, '0');\r\n      const minutes = date.getMinutes().toString().padStart(2, '0');\r\n      const seconds = date.getSeconds().toString().padStart(2, '0');\r\n\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    // 打印功能\r\n    handlePrint() {\r\n      this.$message.success('打印功能尚未实现');\r\n      // 实际项目中可以调用浏览器打印功能\r\n      // window.print();\r\n    },\r\n\r\n    // 返回按钮\r\n    cancel() {\r\n      this.$tab.closeOpenPage(this.$route);\r\n      this.$router.push({ path: \"/leave/leavePlanList\", query: { t: Date.now() } });\r\n    },\r\n\r\n    // 跳转到任务详情页面\r\n    goToTaskDetail(row) {\r\n      this.$router.push({\r\n        path: `/leave/plan/task/${row.taskNo}`\r\n      });\r\n    },\r\n\r\n    getTaskTypeText(taskType) {\r\n      const standardMap = {\r\n        1: '出厂',\r\n        2: '返厂',\r\n        3: '跨区调拨'\r\n      };\r\n      return standardMap[taskType] || '未知';\r\n    },\r\n\r\n    getStatusText(standard) {\r\n      const standardMap = {\r\n        1: '待过皮重',\r\n        2: '待装货',\r\n        3: '待过毛重',\r\n        4: '待出厂',\r\n        5: '待返厂',\r\n        6: '待过毛重(复磅)',\r\n        7: '待卸货',\r\n        8: '待过皮重(复磅)',\r\n        9: '完成'\r\n      };\r\n      return standardMap[standard] || '未知';\r\n    },\r\n\r\n    // 获取计划状态类型\r\n    getPlanStatusType(status) {\r\n      const statusMap = {\r\n        '1': 'warning',  // 待分厂审批\r\n        '2': 'warning',  // 待分厂复审\r\n        '3': 'warning',  // 待生产指挥中心审批\r\n        '4': 'success',  // 审批完成\r\n        '5': 'primary',  // 已出厂\r\n        '6': 'info',     // 部分收货\r\n        '7': 'success',  // 已完成\r\n        '11': 'danger',  // 驳回\r\n        '12': 'danger',  // 废弃\r\n        '13': 'danger'   // 过期\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /**\r\n     * 获取计划下所有任务的任务物资\r\n     * @returns {Promise<void>}\r\n     */\r\n    async getAllTaskMaterials() {\r\n      try {\r\n        // 清空现有数据\r\n        this.taskMaterialMap.clear();\r\n\r\n        // 获取该计划下所有任务的任务物资\r\n        const params = {\r\n          applyNo: this.applyNo\r\n        };\r\n\r\n        const response = await listTaskMaterial(params);\r\n        if (response.code === 200 && response.rows) {\r\n          // 将任务物资按物资ID分组存储\r\n          response.rows.forEach(material => {\r\n            const key = material.materialId;\r\n            if (!this.taskMaterialMap.has(key)) {\r\n              this.taskMaterialMap.set(key, {\r\n                materialId: material.materialId,\r\n                materialName: material.materialName,\r\n                materialSpec: material.materialSpec,\r\n                planNum: material.planNum,\r\n                usedNum: 0,\r\n                taskMaterials: [] // 存储每个任务的具体物资信息\r\n              });\r\n            }\r\n\r\n            const materialInfo = this.taskMaterialMap.get(key);\r\n            // 累加每个任务物资的计划数量作为已使用数量\r\n            materialInfo.usedNum += material.planNum;\r\n            materialInfo.taskMaterials.push({\r\n              taskNo: material.taskNo,\r\n              carNum: material.carNum,\r\n              planNum: material.planNum,\r\n              createTime: material.createTime\r\n            });\r\n          });\r\n        }\r\n\r\n        // 更新物资选择列表中的已使用数量\r\n        this.updateMaterialUsedNum();\r\n\r\n        console.log('Task Material Map:', this.taskMaterialMap);\r\n      } catch (error) {\r\n        console.error('获取任务物资失败:', error);\r\n        this.$message.error('获取任务物资失败');\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 更新物资选择列表中的已使用数量\r\n     */\r\n    updateMaterialUsedNum() {\r\n      this.materialSelectionList.forEach(row => {\r\n        if (row.materialId) {\r\n          const materialInfo = this.taskMaterialMap.get(row.materialId);\r\n          if (materialInfo) {\r\n            // 直接使用累加的计划数量作为已使用数量\r\n            row.usedNum = materialInfo.usedNum;\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // 物资确认按钮点击事件\r\n    async handleMaterialConfirm() {\r\n      try {\r\n        // 校验所有任务的taskStatus是否为9\r\n        if (this.taskListInfo && this.taskListInfo.length > 0) {\r\n          const unfinishedTasks = this.taskListInfo.filter(task => task.taskStatus !== 9);\r\n          if (unfinishedTasks.length > 0) {\r\n            this.$message.error('存在未完成的任务，无法进行物资确认');\r\n            return;\r\n          }\r\n        }\r\n\r\n        // 调用后端接口，传递applyNo\r\n        await confirmMaterial({ applyNo: this.applyNo });\r\n        this.$message.success('物资确认成功');\r\n        // 刷新详情\r\n        this.getListTaskInfo();\r\n        // 重新获取planInfo\r\n        detailPlan(this.applyNo).then(response => {\r\n          this.planInfo = response.data;\r\n        });\r\n      } catch (e) {\r\n        this.$message.error('物资确认失败');\r\n      }\r\n    },\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.box-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 5px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.section-container {\r\n  margin-bottom: 30px;\r\n  border-radius: 8px;\r\n  background: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n  overflow: hidden;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.section-container:nth-child(1) {\r\n  border-top: 4px solid #8957e5;\r\n  /* 基本信息模块 - 紫色 */\r\n}\r\n\r\n.section-container:nth-child(2) {\r\n  border-top: 4px solid #409EFF;\r\n  /* 图片列表模块 - 蓝色 */\r\n}\r\n\r\n.section-container:nth-child(3) {\r\n  border-top: 4px solid #F56C6C;\r\n  /* 文件列表模块 - 红色 */\r\n}\r\n\r\n.section-container:nth-child(4) {\r\n  border-top: 4px solid #67C23A;\r\n  /* 物资列表模块 - 绿色 */\r\n}\r\n\r\n.section-container:nth-child(5) {\r\n  border-top: 4px solid #E6A23C;\r\n  /* 派车信息模块 - 橙色 */\r\n}\r\n\r\n.section-container:nth-child(6) {\r\n  border-top: 4px solid #909399;\r\n  /* 日志列表模块 - 灰色 */\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  padding: 15px 20px;\r\n  margin-bottom: 15px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: #fafafa;\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.section-title::before {\r\n  content: '';\r\n  width: 4px;\r\n  height: 16px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  border-radius: 2px;\r\n}\r\n\r\n.section-container:nth-child(1) .section-title {\r\n  color: #8957e5;\r\n}\r\n\r\n.section-container:nth-child(2) .section-title {\r\n  color: #409EFF;\r\n}\r\n\r\n.section-container:nth-child(3) .section-title {\r\n  color: #F56C6C;\r\n}\r\n\r\n.section-container:nth-child(4) .section-title {\r\n  color: #67C23A;\r\n}\r\n\r\n.section-container:nth-child(5) .section-title {\r\n  color: #E6A23C;\r\n}\r\n\r\n.section-container:nth-child(6) .section-title {\r\n  color: #909399;\r\n}\r\n\r\n.section-container .el-descriptions,\r\n.section-container .el-table,\r\n.section-container .el-timeline {\r\n  padding: 0 20px 20px;\r\n}\r\n\r\n.fixed-bottom-action {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  padding: 15px 0;\r\n  text-align: center;\r\n}\r\n\r\n.dispatch-btn {\r\n  margin-left: 15px;\r\n}\r\n\r\n.empty-data {\r\n  padding: 30px 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.el-dialog__body {\r\n  padding: 20px 30px 0;\r\n}\r\n\r\n.image-container,\r\n.file-container {\r\n  padding: 20px;\r\n}\r\n\r\n.image-list,\r\n.file-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.image-item {\r\n  width: 150px;\r\n  height: 180px;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s;\r\n  cursor: pointer;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.image-item img {\r\n  width: 100%;\r\n  height: 150px;\r\n  object-fit: cover;\r\n  display: block;\r\n}\r\n\r\n.image-name {\r\n  padding: 5px;\r\n  text-align: center;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n  min-width: 180px;\r\n  max-width: 250px;\r\n}\r\n\r\n.file-item:hover {\r\n  background-color: #ecf5ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 24px;\r\n  margin-right: 8px;\r\n  color: #909399;\r\n}\r\n\r\n.file-item:hover .file-icon {\r\n  color: #409EFF;\r\n}\r\n\r\n.file-name {\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 新增物资选择相关样式 */\r\n.el-input-number {\r\n  width: 120px;\r\n}\r\n\r\n.material-selection {\r\n  margin-top: 20px;\r\n}\r\n\r\n.material-selection .el-table {\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.dispatch-log-dialog .el-dialog__body {\r\n  padding: 20px;\r\n}\r\n\r\n.el-table {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n\r\n  th {\r\n    background-color: #fafafa !important;\r\n    color: #606266;\r\n    font-weight: bold;\r\n  }\r\n\r\n  td {\r\n    padding: 12px 0;\r\n  }\r\n}\r\n\r\n.el-timeline {\r\n  padding: 20px !important;\r\n\r\n  .el-timeline-item__node {\r\n    width: 12px;\r\n    height: 12px;\r\n  }\r\n\r\n  .el-timeline-item__content {\r\n    padding: 0 0 0 0px;\r\n  }\r\n}\r\n\r\n.el-descriptions {\r\n  .el-descriptions-item__label {\r\n    background-color: #fafafa;\r\n  }\r\n}\r\n\r\n.el-tag {\r\n  border-radius: 12px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.el-button--text {\r\n  color: #409EFF;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n\r\n  &:hover {\r\n    color: #66b1ff;\r\n    background-color: transparent;\r\n  }\r\n\r\n  &:focus {\r\n    color: #409EFF;\r\n  }\r\n}\r\n</style>\r\n"]}]}